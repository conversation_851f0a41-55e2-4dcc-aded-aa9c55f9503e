package com.knet.order.export;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.export.ExportDataProvider;
import com.knet.order.model.dto.req.AdminOrderListQueryRequest;
import com.knet.order.model.dto.resp.ExportAdminSaleOrderResponse;
import com.knet.order.service.IOrderExportQueryService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * 订单导出数据提供者
 * 实现ExportDataProvider接口，为导出服务提供订单数据
 *
 * <AUTHOR>
 * @since 2025-09-25
 */
@Slf4j
@Component("orderExportDataProvider")
@Schema(description = "订单导出数据提供者")
public class OrderExportDataProvider implements ExportDataProvider {
    @Resource
    private IOrderExportQueryService orderExportQueryService;

    @Override
    public Stream<Map<String, Object>> queryExportData(Map<String, Object> exportParams, String lastId, int pageSize) {
        // 为了兼容原有接口，将lastId转换为pageNo
        int pageNo = 1;
        if (lastId != null && !lastId.isEmpty()) {
            try {
                pageNo = Integer.parseInt(lastId);
            } catch (NumberFormatException e) {
                log.warn("无法解析lastId为页码，使用默认值1: {}", lastId);
            }
        }
        return queryExportData(exportParams, pageNo, pageSize);
    }

    /**
     * 使用页码进行分页查询的导出数据方法
     */
    public Stream<Map<String, Object>> queryExportData(Map<String, Object> exportParams, int pageNo, int pageSize) {
        log.info("查询订单导出数据 - params: {}, pageNo: {}, pageSize: {}", exportParams, pageNo, pageSize);
        try {
            AdminOrderListQueryRequest request = buildQueryRequest(exportParams, pageNo, pageSize);
            IPage<ExportAdminSaleOrderResponse> result = orderExportQueryService.queryAllOrderList(request);
            List<ExportAdminSaleOrderResponse> orders = result.getRecords();
            log.info("查询到订单导出数据 {} 条，当前页: {}", orders.size(), pageNo);
            return orders.stream().map(this::convertToMap);
        } catch (Exception e) {
            log.error("查询订单导出数据失败 - params: {}, pageNo: {}", exportParams, pageNo, e);
            return Stream.empty();
        }
    }

    @Override
    public long getEstimatedCount(Map<String, Object> exportParams) {
        log.info("获取订单导出数据总数预估 - params: {}", exportParams);
        try {
            AdminOrderListQueryRequest request = buildQueryRequest(exportParams, null, 1);
            IPage<ExportAdminSaleOrderResponse> result = orderExportQueryService.queryAllOrderList(request);
            long count = result.getTotal();
            log.info("订单导出数据预估总数: {}", count);
            return count;
        } catch (Exception e) {
            log.error("获取订单导出数据总数失败 - params: {}", exportParams, e);
            return 0L;
        }
    }

    @Override
    public boolean validateExportPermission(String userId, Map<String, Object> exportParams) {
        log.info("订单导出权限验证 - userId: {}, params: {}", userId, exportParams);
        // 根据要求，订单导出不需要验证权限，直接返回true
        log.info("订单导出无需权限验证，直接允许 - userId: {}", userId);
        return true;
    }

    @Override
    public String getProviderName() {
        return "订单数据提供者";
    }

    /**
     * 构建查询请求对象，使用现有的AdminOrderListQueryRequest
     */
    private AdminOrderListQueryRequest buildQueryRequest(Map<String, Object> exportParams, int pageNo, int pageSize) {
        AdminOrderListQueryRequest request = new AdminOrderListQueryRequest();
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        if (MapUtil.isNotEmpty(exportParams)) {
            if (exportParams.containsKey("parentOrderId")) {
                request.setParentOrderId((String) exportParams.get("parentOrderId"));
            }
            if (exportParams.containsKey("orderId")) {
                request.setOrderId((String) exportParams.get("orderId"));
            }
            if (exportParams.containsKey("account")) {
                request.setAccount((String) exportParams.get("account"));
            }
            if (exportParams.containsKey("searchType")) {
                String searchTypeStr = (String) exportParams.get("searchType");
                try {
                    request.setSearchType(com.knet.common.enums.OrderSearchType.valueOf(searchTypeStr));
                } catch (Exception e) {
                    log.warn("解析订单状态失败: {}", searchTypeStr);
                }
            }
            if (exportParams.containsKey("startTime")) {
                Object startTimeObj = exportParams.get("startTime");
                if (startTimeObj != null) {
                    try {
                        request.setStartTime(DateUtil.parse(startTimeObj.toString()));
                    } catch (Exception e) {
                        log.warn("解析开始时间失败，将忽略此参数: {}", startTimeObj, e);
                    }
                }
            }
            if (exportParams.containsKey("endTime")) {
                Object startTimeObj = exportParams.get("endTime");
                if (startTimeObj != null) {
                    try {
                        request.setEndTime(DateUtil.parse(startTimeObj.toString()));
                    } catch (Exception e) {
                        log.warn("解析开始时间失败，将忽略此参数: {}", startTimeObj, e);
                    }
                }
            }
        }
        return request;
    }

    /**
     * 构建查询请求对象（兼容原有接口）
     */
    private AdminOrderListQueryRequest buildQueryRequest(Map<String, Object> exportParams, String lastId, int pageSize) {
        // 对于获取总数的情况，使用第1页
        int pageNo = 1;
        if (lastId != null && !lastId.isEmpty()) {
            try {
                pageNo = Integer.parseInt(lastId);
            } catch (NumberFormatException e) {
                log.warn("无法解析lastId为页码，使用默认值1: {}", lastId);
            }
        }
        return buildQueryRequest(exportParams, pageNo, pageSize);
    }

    /**
     * 将订单对象转换为Map格式
     */
    private Map<String, Object> convertToMap(ExportAdminSaleOrderResponse order) {
        Map<String, Object> row = new HashMap<>(16);
        row.put("parentOrderId", order.getParentOrderId());
        row.put("itemNo", order.getItemNo());
        row.put("userAccount", order.getUserAccount());
        row.put("sku", order.getSku());
        row.put("productName", order.getProductName());
        row.put("size", order.getSize());
        row.put("trackingNo", order.getTrackingNo());
        row.put("price", order.getPrice());
        row.put("count", order.getCount());
        row.put("itemStatus", order.getItemStatus() != null ? order.getItemStatus().name() : "");
        row.put("status", order.getStatus() != null ? order.getStatus().name() : "");
        row.put("totalQuantity", order.getTotalQuantity());
        row.put("totalAmount", order.getTotalAmount());
        row.put("createTime", order.getCreateTime() != null ?
                DateUtil.format(order.getCreateTime(), "yyyy-MM-dd HH:mm:ss") : "");
        row.put("paidTime", order.getCreateTime() != null ?
                DateUtil.format(order.getPaidTime(), "yyyy-MM-dd HH:mm:ss") : "");
        return row;
    }
}
