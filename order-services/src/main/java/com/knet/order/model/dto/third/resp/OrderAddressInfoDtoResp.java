package com.knet.order.model.dto.third.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2025/09/22
 * @description: 订单地址信息返回体（包含地址快照）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class OrderAddressInfoDtoResp extends BaseResponse {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderId;

    @Schema(description = "快递地址ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long addressId;

    @Schema(description = "账单地址ID")
    private Long billAddressId;

    @Schema(description = "快递地址快照信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private UserAddressDtoResp shippingAddress;

    @Schema(description = "账单地址快照信息")
    private UserInvoiceAddressDtoResp billingAddress;
}
