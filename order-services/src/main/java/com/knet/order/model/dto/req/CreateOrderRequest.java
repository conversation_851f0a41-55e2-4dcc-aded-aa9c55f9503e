package com.knet.order.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3 16:30
 * @description: 创建订单请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "创建订单请求")
public class CreateOrderRequest extends BaseRequest {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "订单商品项列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "订单商品项不能为空")
    @Valid
    private List<OrderItemRequest> items;

    @Schema(description = "地址id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单地址id 不能为空")
    private Long addressId;

    @Schema(description = "发票地址ID，对应SysUserInvoiceAddress的ID字段")
    private Long billAddressId;

    /**
     * 订单商品项请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "订单商品项请求")
    public static class OrderItemRequest {

        @Schema(description = "商品SKU", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "商品SKU不能为空")
        private String sku;

        @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "商品名称不能为空")
        private String productName;

        @Schema(description = "商品图片URL")
        private String imageUrl;

        @Schema(description = "尺码明细列表", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "尺码明细不能为空")
        @Valid
        private List<SizeDetailRequest> sizeDetails;
    }

    /**
     * 尺码明细请求（支持相同SKU相同尺码不同定价）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "尺码明细请求")
    public static class SizeDetailRequest {

        @Schema(description = "尺码值", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
        @NotBlank(message = "尺码值不能为空")
        private String size;

        @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        @NotNull(message = "数量不能为空")
        @Min(value = 1, message = "数量必须大于0")
        private Integer quantity;

        @Schema(description = "单价（单位：美元）", requiredMode = Schema.RequiredMode.REQUIRED, example = "99.99")
        @NotNull(message = "单价不能为空")
        private String unitPrice;
    }
}
