package com.knet.order.model.dto.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/9/19
 * @description: 地址快照响应对象，用于展示订单中保存的地址信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(description = "地址快照响应对象")
public class AddressSnapshotResponse {

    @Schema(description = "地址ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    @Schema(description = "收件人全名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fullName;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "国家名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String country;

    @Schema(description = "地址行1（街道信息）")
    private String addressLine1;

    @Schema(description = "地址行2（补充信息）")
    private String addressLine2;

    @Schema(description = "城市名称")
    private String city;

    @Schema(description = "州/省名称")
    private String state;

    @Schema(description = "邮政编码")
    private String zipCode;

    @Schema(description = "国际电话区号")
    private String phonePrefix;

    @Schema(description = "本地电话号码")
    private String phoneNumber;

    @Schema(description = "收件人邮箱（仅账单地址）")
    private String email;

    @Schema(description = "是否为默认地址（仅账单地址）")
    private Integer isDefault;

    /**
     * 获取完整手机号
     */
    public String getFullPhoneNumber() {
        if (phonePrefix != null && phoneNumber != null) {
            return "+" + phonePrefix + phoneNumber;
        }
        return phoneNumber;
    }

    /**
     * 获取完整地址字符串
     */
    public String getFullAddress() {
        StringBuilder address = new StringBuilder();

        if (addressLine1 != null) {
            address.append(addressLine1);
        }

        if (addressLine2 != null && !addressLine2.trim().isEmpty()) {
            if (address.length() > 0) address.append(", ");
            address.append(addressLine2);
        }

        if (city != null) {
            if (address.length() > 0) address.append(", ");
            address.append(city);
        }

        if (state != null) {
            if (address.length() > 0) address.append(", ");
            address.append(state);
        }

        if (zipCode != null) {
            if (address.length() > 0) address.append(" ");
            address.append(zipCode);
        }

        if (country != null) {
            if (address.length() > 0) address.append(", ");
            address.append(country);
        }

        return address.toString();
    }
}
