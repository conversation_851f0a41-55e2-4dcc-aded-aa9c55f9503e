package com.knet.order.model.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BasePageRequest;
import com.knet.common.enums.OrderSearchType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/7 10:00
 * @description: 管理员订单列表查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AdminOrderListQueryRequest extends BasePageRequest {

    @Schema(description = "父订单订单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String parentOrderId;

    @Schema(description = "子订单订单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String orderId;

    @Schema(description = "用户账号（模糊搜索）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String account;
    /**
     * 订单状态，查询全部不传值
     *
     * @see OrderSearchType
     */
    @Schema(description = "订单状态，查询全部不传值", example = "PENDING")
    private OrderSearchType searchType;

    @Schema(description = "开始时间", example = "2025-09-01 00:00:00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "结束时间", example = "2025-09-02 23:59:59", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @Schema(description = "用户ID列表", requiredMode = Schema.RequiredMode.NOT_REQUIRED, hidden = true)
    private List<Long> userIds;
}
