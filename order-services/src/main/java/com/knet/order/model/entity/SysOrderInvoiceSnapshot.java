package com.knet.order.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 订单发票快照表
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_order_invoice_snapshot", description = "订单发票快照表")
@TableName("sys_order_invoice_snapshot")
public class SysOrderInvoiceSnapshot extends BaseEntity {

    /**
     * 发票编号
     */
    @Schema(description = "发票编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceNumber;

    /**
     * 订单ID
     */
    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    /**
     * 发票日期
     */
    @Schema(description = "发票日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private Date invoiceDate;

    /**
     * 到期日期
     */
    @Schema(description = "到期日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private Date dueDate;

    /**
     * 发票总金额
     */
    @Schema(description = "发票总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal totalAmount;

    /**
     * 账单地址信息（JSON格式）
     */
    @Schema(description = "账单地址信息（JSON格式）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String billToAddress;

    /**
     * 收货地址信息（JSON格式）
     */
    @Schema(description = "收货地址信息（JSON格式）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String shipToAddress;

    /**
     * 订单商品明细（JSON格式）
     */
    @Schema(description = "订单商品明细（JSON格式）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderItems;

    /**
     * 发票PDF文件S3地址
     */
    @Schema(description = "发票PDF文件S3地址")
    private String invoicePdfUrl;

    /**
     * 发票状态：0-待生成，1-已生成，2-已发送，3-发送失败
     */
    @Schema(description = "发票状态：0-待生成，1-已生成，2-已发送，3-发送失败")
    private Integer invoiceStatus;
}
