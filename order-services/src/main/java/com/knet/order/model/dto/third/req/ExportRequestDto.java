package com.knet.order.model.dto.third.req;

import com.knet.common.base.BaseRequest;
import com.knet.common.enums.ExportType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 导出请求DTO
 * order-services模块独立定义，避免对export-services的依赖
 *
 * <AUTHOR> Export Team
 * @since 2025-09-25
 */
@NoArgsConstructor
@Builder
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "导出请求")
public class ExportRequestDto extends BaseRequest {

    @Schema(description = "业务系统标识", example = "order", allowableValues = {"order", "goods", "user", "payment"})
    @NotBlank(message = "系统标识不能为空")
    private String systemId;

    @Schema(description = "userId")
    @NotNull(message = "userId不能为空")
    private Long userId;

    @Schema(description = "模板JSON配置（调用方提供的简化模板）",
            example = "{\"columns\":[{\"field\":\"orderId\",\"title\":\"订单ID\"},{\"field\":\"userName\",\"title\":\"用户名\"}]}")
    @NotNull(message = "模板配置不能为空")
    private Object templateJson;

    @Schema(description = "导出参数（查询条件等）", example = "{\"startDate\":\"2025-01-01\",\"endDate\":\"2025-12-31\"}")
    private Map<String, Object> exportParams;

    @Schema(description = "文件名前缀（可选，默认使用系统ID）", example = "order_export")
    private String fileNamePrefix;

    @Schema(description = "任务备注", example = "导出2025年订单数据")
    private String remark;

    @Schema(description = "数据提供者标识(用于标识具体的数据接口方法)", example = "adminOrderExport", requiredMode = Schema.RequiredMode.REQUIRED)
    private String dataProvider;

    @Schema(description = "导出类型标识(用于区分同一服务的不同导出场景)", example = "ADMIN_ORDER_LIST", requiredMode = Schema.RequiredMode.REQUIRED)
    private ExportType exportType;
}
