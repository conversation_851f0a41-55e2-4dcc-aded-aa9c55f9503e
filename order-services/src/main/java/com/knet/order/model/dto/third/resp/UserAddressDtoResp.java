package com.knet.order.model.dto.third.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2025/2/17 09:59
 * @description: 用户地址信息返回体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UserAddressDtoResp extends BaseResponse {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;
    /**
     * 用户ID
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;
    /**
     * 收件人全名
     */
    @Schema(description = "收件人全名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fullName;

    /**
     * 公司名称
     */
    @Schema(description = "公司名称")
    private String companyName;

    /**
     * 国家名称（建议存储ISO国家代码）
     */
    @Schema(description = "国家名称（建议存储ISO国家代码）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String country;

    /**
     * 地址行1（街道信息）
     */
    @Schema(description = "地址行1（街道信息）")
    private String addressLine1;

    /**
     * 地址行2（补充信息）
     */
    @Schema(description = "地址行2（补充信息）")
    private String addressLine2;

    /**
     * 城市名称
     */
    @Schema(description = "城市名称")
    private String city;

    /**
     * 州/省名称
     */
    @Schema(description = "州/省名称")
    private String state;

    /**
     * 邮政编码
     */
    @Schema(description = "邮政编码")
    private String zipCode;

    /**
     * 国际电话区号(如86/852)
     */
    @Schema(description = "国际电话区号(如86/852)")
    private String phonePrefix;

    /**
     * 本地电话号码(不含国际区号)
     */
    @Schema(description = "本地电话号码(不含国际区号)")
    private String phoneNumber;

    /**
     * 完整手机号
     */
    public String getFullPhoneNumber() {
        if (phonePrefix != null && phoneNumber != null) {
            return "+" + phonePrefix + phoneNumber;
        }
        return phoneNumber;
    }
}
