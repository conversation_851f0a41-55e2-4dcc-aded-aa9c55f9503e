package com.knet.order.model.dto.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/9/19 13:28
 * @description: 订单快照重新生成返回结果体
 */
@Data
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class OrderInvoicePdfResponse extends BaseResponse {
    @Schema(description = "发票PDF文件S3地址")
    private String invoicePdfUrl;
}
