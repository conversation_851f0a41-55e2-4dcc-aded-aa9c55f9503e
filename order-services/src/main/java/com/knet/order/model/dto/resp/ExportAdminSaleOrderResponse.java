package com.knet.order.model.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.enums.KnetOrderItemStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/9/26 11:03
 * @description: 管理员订单导出响应
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
public class ExportAdminSaleOrderResponse extends BaseResponse {

    @Schema(description = "父订单ID")
    private String parentOrderId;

    @Schema(description = "子订单订单号")
    private String itemNo;

    @Schema(description = "userId")
    private Long userId;

    @Schema(description = "用户账号")
    private String userAccount;

    @Schema(description = "商品SKU")
    private String sku;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "尺码")
    private String size;

    @Schema(description = "图片地址")
    private String imageUrl;

    @Schema(description = "物流单号")
    private String trackingNo;

    @Schema(description = "商品单价（美元）")
    private String price;

    @Schema(description = "数量")
    private Integer count;
    /**
     * @see KnetOrderItemStatus
     */
    @Schema(description = "子订单状态")
    private KnetOrderItemStatus itemStatus;
    /**
     * @see KnetOrderGroupStatus
     */
    @Schema(description = "父单状态")
    private KnetOrderGroupStatus status;

    @Schema(description = "总商品数量")
    private Integer totalQuantity;

    @Schema(description = "订单总金额")
    private String totalAmount;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    @Schema(description = "订单创建时间")
    private Date createTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    @Schema(description = "订单付款时间")
    private Date paidTime;
}
