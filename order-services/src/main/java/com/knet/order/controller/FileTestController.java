package com.knet.order.controller;

import com.knet.common.base.HttpResult;
import com.knet.common.utils.S3FileUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传下载测试控制器
 * 用于测试S3文件上传下载功能
 *
 * <AUTHOR>
 * @date 2025/09/11
 */
@Slf4j
@RestController
@RequestMapping("/api/test/file")
@Tag(name = "文件上传下载测试", description = "S3文件上传下载功能测试接口")
public class FileTestController {

    @Resource
    private S3FileUtil s3FileUtil;

    /**
     * 生成预签名上传URL
     *
     * @param fileName 文件名
     * @param fileType 文件类型 (MIME type)
     * @param folder   文件夹路径 (可选)
     * @return 预签名上传URL信息
     */
    @PostMapping("/upload-url")
    @Operation(summary = "生成预签名上传URL", description = "生成用于客户端直接上传到S3的预签名URL")
    public HttpResult<S3FileUtil.PresignedUploadInfo> generateUploadUrl(
            @Parameter(description = "文件名", required = true)
            @RequestParam String fileName,
            @Parameter(description = "文件类型 (MIME type)", required = true)
            @RequestParam String fileType,
            @Parameter(description = "文件夹路径", required = false)
            @RequestParam(required = false) String folder) {
        try {
            log.info("生成预签名上传URL请求: fileName={}, fileType={}, folder={}", fileName, fileType, folder);
            S3FileUtil.PresignedUploadInfo uploadInfo = s3FileUtil.generatePresignedUploadUrl(fileName, fileType, folder);
            log.info("预签名上传URL生成成功: fileKey={}", uploadInfo.getFileKey());
            return HttpResult.ok(uploadInfo);
        } catch (Exception e) {
            log.error("生成预签名上传URL失败", e);
            return HttpResult.error("生成预签名上传URL失败: " + e.getMessage());
        }
    }

    /**
     * 生成预签名下载URL
     *
     * @param fileKey 文件在S3中的key
     * @return 预签名下载URL
     */
    @PostMapping("/download-url")
    @Operation(summary = "生成预签名下载URL", description = "生成用于客户端直接从S3下载的预签名URL")
    public HttpResult<Map<String, String>> generateDownloadUrl(
            @Parameter(description = "文件key", required = true)
            @RequestParam String fileKey) {
        try {
            log.info("生成预签名下载URL请求: fileKey={}", fileKey);
            String downloadUrl = s3FileUtil.generatePresignedDownloadUrl(fileKey);
            Map<String, String> result = new HashMap<>();
            result.put("fileKey", fileKey);
            result.put("downloadUrl", downloadUrl);
            log.info("预签名下载URL生成成功");
            return HttpResult.ok(result);
        } catch (Exception e) {
            log.error("生成预签名下载URL失败", e);
            return HttpResult.error("生成预签名下载URL失败: " + e.getMessage());
        }
    }

    /**
     * 直接上传文件 (用于测试-已经测试通过)
     *
     * @param file   上传的文件
     * @param folder 文件夹路径 (可选)
     * @return 文件下载地址
     */
    @PostMapping("/upload")
    @Operation(summary = "直接上传文件", description = "服务端直接上传文件到S3 (仅用于测试)")
    public HttpResult<Map<String, String>> uploadFile(
            @Parameter(description = "上传的文件", required = true)
            @RequestPart("file") MultipartFile file,
            @Parameter(description = "文件夹路径", required = false)
            @RequestParam(required = false) String folder) {
        try {
            if (file == null || file.isEmpty()) {
                return HttpResult.error("文件不能为空");
            }
            String fileName = file.getOriginalFilename();
            log.info("直接上传文件请求: fileName={}, size={} bytes, folder={}",
                    fileName, file.getSize(), folder);
            // 使用字节数组上传
            byte[] fileBytes = file.getBytes();
            String downloadUrl = s3FileUtil.uploadFile(fileBytes, fileName);
            // 从下载URL中提取文件key
            String fileKey = downloadUrl.replace("https://uploadsb2b.knetgroup.com/", "");
            Map<String, String> result = new HashMap<>();
            result.put("fileName", fileName);
            result.put("fileKey", fileKey);
            result.put("downloadUrl", downloadUrl);
            result.put("fileSize", String.valueOf(file.getSize()));
            log.info("文件上传成功: fileKey={}", fileKey);
            return HttpResult.ok(result);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return HttpResult.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param fileKey 文件在S3中的key
     * @return 文件是否存在
     */
    @GetMapping("/exists")
    @Operation(summary = "检查文件是否存在", description = "检查指定文件在S3中是否存在")
    public HttpResult<Map<String, Object>> checkFileExists(
            @Parameter(description = "文件key", required = true)
            @RequestParam String fileKey) {
        try {
            log.info("检查文件存在性请求: fileKey={}", fileKey);
            boolean exists = s3FileUtil.fileExists(fileKey);
            Map<String, Object> result = new HashMap<>();
            result.put("fileKey", fileKey);
            result.put("exists", exists);
            log.info("文件存在性检查完成: exists={}", exists);
            return HttpResult.ok(result);
        } catch (Exception e) {
            log.error("检查文件存在性失败", e);
            return HttpResult.error("检查文件存在性失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件信息
     *
     * @param fileKey 文件在S3中的key
     * @return 文件信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取文件信息", description = "获取指定文件的详细信息")
    public HttpResult<S3FileUtil.FileInfo> getFileInfo(
            @Parameter(description = "文件key", required = true)
            @RequestParam String fileKey) {
        try {
            log.info("获取文件信息请求: fileKey={}", fileKey);
            S3FileUtil.FileInfo fileInfo = s3FileUtil.getFileInfo(fileKey);
            log.info("文件信息获取成功");
            return HttpResult.ok(fileInfo);
        } catch (Exception e) {
            log.error("获取文件信息失败", e);
            return HttpResult.error("获取文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param fileKey 文件在S3中的key
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除文件", description = "从S3中删除指定文件")
    public HttpResult<Map<String, Object>> deleteFile(
            @Parameter(description = "文件key", required = true)
            @RequestParam String fileKey) {
        try {
            log.info("删除文件请求: fileKey={}", fileKey);
            boolean deleted = s3FileUtil.deleteFile(fileKey);
            Map<String, Object> result = new HashMap<>();
            result.put("fileKey", fileKey);
            result.put("deleted", deleted);
            log.info("文件删除完成: deleted={}", deleted);
            return HttpResult.ok(result);
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return HttpResult.error("删除文件失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查 - 测试S3连接
     *
     * @return S3连接状态
     */
    @GetMapping("/health")
    @Operation(summary = "S3连接健康检查", description = "测试S3服务连接状态")
    public HttpResult<Map<String, Object>> healthCheck() {
        try {
            log.info("S3连接健康检查");
            // 生成一个测试用的预签名URL来检查连接
            String testFileName = "health-check-" + System.currentTimeMillis() + ".txt";
            S3FileUtil.PresignedUploadInfo uploadInfo = s3FileUtil.generatePresignedUploadUrl(
                    testFileName, "text/plain", "health-check");
            Map<String, Object> result = new HashMap<>();
            result.put("status", "healthy");
            result.put("message", "S3连接正常");
            result.put("timestamp", System.currentTimeMillis());
            result.put("testFileKey", uploadInfo.getFileKey());
            log.info("S3连接健康检查通过");
            return HttpResult.ok(result);
        } catch (Exception e) {
            log.error("S3连接健康检查失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("status", "unhealthy");
            result.put("message", "S3连接异常: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
            return HttpResult.error(500, "S3连接健康检查失败", result);
        }
    }
}
