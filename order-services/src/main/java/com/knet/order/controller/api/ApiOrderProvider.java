package com.knet.order.controller.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.base.HttpResult;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.order.export.OrderExportDataProvider;
import com.knet.order.model.dto.req.ReplaceProductRequest;
import com.knet.order.model.dto.resp.ReplaceProductResp;
import com.knet.order.model.dto.third.req.KnetB2bOrderQueryRequest;
import com.knet.order.model.dto.third.req.UpdatedOrderRequest;
import com.knet.order.model.dto.third.resp.KnetB2bOrderQueryVo;
import com.knet.order.model.dto.third.resp.OrderDetailDtoResp;
import com.knet.order.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.vo.SubOrderGroupVo;
import com.knet.order.model.vo.SubOrderItemVo;
import com.knet.order.service.IApiOrdersService;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderInvoiceSnapshotService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2025/3/11 16:51
 * @description: 订单对外提供服务
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = "订单服务-对外提供接口", description = "订单服务-对外提供接口")
public class ApiOrderProvider {
    @Resource
    private IApiOrdersService apiOrdersService;
    @Resource
    private ISysOrderGroupService orderGroupService;
    @Resource
    private ISysOrderInvoiceSnapshotService orderInvoiceSnapshotService;
    @Resource
    private OrderExportDataProvider orderExportDataProvider;

    /**
     * 根据订单ID获取 订单商品明细信息
     *
     * @param prentOrderId 订单ID
     * @return 订单商品明细信息
     */
    @GetMapping("/order/{prentOrderId}")
    @Operation(summary = "根据订单ID获取订单商品明细信息", description = "供其他服务调用，获取订单商品明细信息")
    public HttpResult<List<SubOrderItemVo>> getOrderItemsByOrderId(
            @Parameter(description = "订单ID", required = true, example = "1")
            @PathVariable("prentOrderId") String prentOrderId) {
        log.info("获取订单商品明细信息: prentOrderId={}", prentOrderId);
        try {
            List<SubOrderItemVo> items = apiOrdersService.getOrderItemsByPrentOrderId(prentOrderId);
            if (CollUtil.isEmpty(items)) {
                log.warn("订单商品明细不存在: prentOrderId={}", prentOrderId);
                return HttpResult.error("订单商品明细不存在");
            }
            log.info("获取订单商品明细信息成功: prentOrderId={}", prentOrderId);
            return HttpResult.ok(items);
        } catch (Exception e) {
            log.error("获取订单商品明细信息失败: prentOrderId={}, error={}", prentOrderId, e.getMessage(), e);
            return HttpResult.error("获取订单商品明细信息失败: " + e.getMessage());
        }
    }


    @GetMapping("/order/item/{itemId}")
    @Operation(summary = "根据itemId获取订单item详情", description = "供其他服务调用，根据itemId获取订单item详情")
    public HttpResult<SysOrderItem> getOrderItemDetail(
            @Parameter(description = "订单ID", required = true, example = "1")
            @PathVariable("itemId") String itemId) {
        log.info("获取订单商品明细信息: itemId={}", itemId);
        try {
            SysOrderItem item = apiOrdersService.getOrderItemDetails(itemId);
            log.info("获取订单商品明细信息成功: itemId={}", itemId);
            return HttpResult.ok(item);
        } catch (Exception e) {
            log.error("获取订单商品明细信息失败: itemId={}, error={}", itemId, e.getMessage(), e);
            return HttpResult.error("获取订单商品明细信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID获取 订单信息
     *
     * @param prentOrderId 订单ID
     * @return 订单信息
     */
    @GetMapping("/order/group/{prentOrderId}")
    @Operation(summary = "根据订单ID获取订单信息", description = "供其他服务调用，获取订单信息")
    public HttpResult<SubOrderGroupVo> getOrderGroupByOrderId(
            @Parameter(description = "订单ID", required = true, example = "1")
            @PathVariable("prentOrderId") String prentOrderId) {
        log.info("获取订单信息: prentOrderId={}", prentOrderId);
        try {
            SubOrderGroupVo dto = apiOrdersService.getOrderGroupByPrentOrderId(prentOrderId);
            if (BeanUtil.isEmpty(dto)) {
                log.warn("订单订单信息 不存在: prentOrderId={}", prentOrderId);
                return HttpResult.error("订单信息 不存在");
            }
            log.info("获取订单信息 成功: prentOrderId={}", prentOrderId);
            return HttpResult.ok(dto);
        } catch (Exception e) {
            log.error("获取订单信息 失败: prentOrderId={}, error={}", prentOrderId, e.getMessage(), e);
            return HttpResult.error("获取订单信息 失败: " + e.getMessage());
        }
    }

    /**
     * 自定义获取订单信息列表
     * 根据状态、等条件查询b2b订单列表，支持分页
     *
     * @param request 查询条件
     * @return 订单信息列表
     */
    @ModifyHeader(handlerType = "API_KEY")
    @Loggable(value = "knet 自定义获取订单信息列表")
    @PostMapping("/order/list")
    @Operation(summary = "自定义获取订单信息列表", description = "供其他服务调用，根据状态、等条件查询b2b订单列表，支持分页")
    public HttpResult<IPage<KnetB2bOrderQueryVo>> queryB2bOrderList(@Validated @RequestBody KnetB2bOrderQueryRequest request) {
        log.info("自定义获取订单信息列表: {}", request);
        try {
            // 如果订单状态为已取消，直接返回空分页结果
            if (KnetOrderItemStatus.CANCELLED.equals(request.getStatus())) {
                log.info("订单状态为已取消，返回空列表");
                Page<KnetB2bOrderQueryVo> emptyPage = new Page<>(request.getPageNo(), request.getPageSize());
                return HttpResult.ok(emptyPage);
            }
            IPage<KnetB2bOrderQueryVo> page = apiOrdersService.getOrderItemsByPage(request);
            log.info("自定义获取订单信息列表 成功:返回条数 {}", page.getSize());
            return HttpResult.ok(page);
        } catch (Exception e) {
            log.error("<自定义获取订单信息列表> 失败: {}", e.getMessage(), e);
            return HttpResult.error("<自定义获取订单信息列表> 失败" + e.getMessage());
        }
    }

    /**
     * 更新订单状态
     *
     * @param request 更新订单状态请求
     * @return 更新结果
     */
    @ModifyHeader(handlerType = "API_KEY")
    @Loggable(value = "knet 更新订单状态")
    @PostMapping("/order/status")
    @Operation(summary = "更新订单状态", description = "供其他服务调用，更新订单状态")
    public HttpResult<Boolean> updateOrderStatus(@Validated @RequestBody UpdatedOrderRequest request) {
        log.info("更新订单状态: {}", request);
        try {
            log.info("更新订单状态 成功");
            boolean updated = apiOrdersService.updateOrderStatus(request);
            if (!updated) {
                log.info("更新订单状态 失败");
                return HttpResult.error("更新订单状态 失败");
            }
            return HttpResult.ok(true);
        } catch (Exception e) {
            log.error("更新订单状态 失败: {}", e.getMessage(), e);
            return HttpResult.error("更新订单状态 失败" + e.getMessage());
        }
    }

    /**
     * 根据父订单号获取订单item详情列表
     *
     * @param prentOrderId 父订单号
     * @return 订单项详情
     */
    @GetMapping("/order/items/{prentOrderId}")
    @Operation(summary = "根据父订单号获取订单item详情列表", description = "供其他服务调用，根据父订单号获取订单item详情列表")
    HttpResult<List<SysOrderItem>> getOrderItemList(
            @Parameter(description = "父订单号", required = true, example = "1")
            @PathVariable("prentOrderId") String prentOrderId) {
        log.info("根据父订单号获取订单item详情列表: prentOrderId={}", prentOrderId);
        try {
            List<SysOrderItem> items = apiOrdersService.queryOrderItemsByPrentOrderId(prentOrderId);
            log.info("根据父订单号获取订单item详情列表 成功: prentOrderId={}", prentOrderId);
            return HttpResult.ok(items);
        } catch (Exception e) {
            log.error("根据父订单号获取订单item详情列表 失败: prentOrderId={}, error={}", prentOrderId, e.getMessage(), e);
            return HttpResult.error("根据父订单号获取订单item详情列表 失败" + e.getMessage());
        }
    }

    /**
     * 检查oneId冲突
     *
     * @param oneIds         要检查的oneId列表
     * @param excludeOrderId 排除的订单ID
     * @return 冲突的oneId列表
     */
    @PostMapping("/order/check-oneid-conflicts")
    @Operation(summary = "检查oneId冲突", description = "检查指定的oneId是否已被其他订单使用")
    public HttpResult<List<String>> checkOneIdConflicts(
            @RequestParam("oneIds") List<String> oneIds,
            @RequestParam("excludeOrderId") String excludeOrderId) {
        log.info("检查oneId冲突: oneIds={}, excludeOrderId={}", oneIds, excludeOrderId);
        try {
            List<String> conflictOneIds = apiOrdersService.checkOneIdConflicts(oneIds, excludeOrderId);
            log.info("oneId冲突检查完成: 冲突数量={}", conflictOneIds.size());
            return HttpResult.ok(conflictOneIds);
        } catch (Exception e) {
            log.error("检查oneId冲突失败: oneIds={}, excludeOrderId={}, error={}", oneIds, excludeOrderId, e.getMessage(), e);
            return HttpResult.error("检查oneId冲突失败: " + e.getMessage());
        }
    }

    /**
     * 订单超卖替换
     *
     * @param request 替换请求
     * @return 替换结果
     */
    @ModifyHeader(handlerType = "API_KEY")
    @Loggable(value = "订单超卖替换")
    @PostMapping("/order/replace-product")
    @Operation(summary = "订单超卖替换", description = "第三方传参数订单itemNo和新oneId替换订单对应的oneId与listingId")
    public HttpResult<ReplaceProductResp> replaceOrderProduct(
            @Validated @RequestBody ReplaceProductRequest request) {
        log.info("订单超卖替换: {}", request);
        try {
            ReplaceProductResp result = apiOrdersService.replaceOrderProduct(request);
            log.info("订单超卖替换 替换订单商品成功: itemNo={}, newOneId={}", request.getItemNo(), request.getNewOneId());
            return HttpResult.ok(result);
        } catch (Exception e) {
            log.error("订单超卖替换 替换订单商品失败: {}, error={}", request, e.getMessage(), e);
            return HttpResult.error("订单超卖替换 替换订单商品失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单详情（用于发票生成）
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    @GetMapping("/order/detail/{orderId}")
    @Operation(summary = "获取订单详情", description = "供其他服务调用，获取订单详细信息")
    public HttpResult<OrderDetailDtoResp> getOrderDetail(
            @Parameter(description = "订单ID", required = true)
            @PathVariable("orderId") String orderId) {
        log.info("获取订单详情: orderId={}", orderId);
        try {
            OrderDetailDtoResp orderDetail = orderGroupService.getOrderDetailForApi(orderId);
            if (orderDetail == null) {
                log.warn("订单不存在: orderId={}", orderId);
                return HttpResult.error("订单不存在");
            }
            log.info("获取订单详情成功: orderId={}", orderId);
            return HttpResult.ok(orderDetail);
        } catch (Exception e) {
            log.error("获取订单详情失败: orderId={}, error={}", orderId, e.getMessage(), e);
            return HttpResult.error("获取订单详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取发票快照
     *
     * @param invoiceNumber 发票编号
     * @return 发票快照
     */
    @GetMapping("/invoice/snapshot/number/{invoiceNumber}")
    @Operation(summary = "获取发票快照", description = "供其他服务调用，获取发票快照信息")
    public HttpResult<OrderInvoiceSnapshotDtoResp> getInvoiceSnapshot(
            @Parameter(description = "发票编号", required = true)
            @PathVariable("invoiceNumber") String invoiceNumber) {
        log.info("获取发票快照: invoiceNumber={}", invoiceNumber);
        try {
            OrderInvoiceSnapshotDtoResp invoiceSnapshot = orderInvoiceSnapshotService.getInvoiceSnapshotForApi(invoiceNumber);
            if (invoiceSnapshot == null) {
                log.warn("发票快照不存在: invoiceNumber={}", invoiceNumber);
                return HttpResult.error("发票快照不存在");
            }
            log.info("获取发票快照成功: invoiceNumber={}", invoiceNumber);
            return HttpResult.ok(invoiceSnapshot);
        } catch (Exception e) {
            log.error("获取发票快照失败: invoiceNumber={}, error={}", invoiceNumber, e.getMessage(), e);
            return HttpResult.error("获取发票快照失败: " + e.getMessage());
        }
    }

    /**
     * 更新发票PDF地址
     *
     * @param invoiceNumber 发票编号
     * @param pdfUrl        PDF地址
     * @return 更新结果
     */
    @PutMapping("/invoice/pdf-url")
    @Operation(summary = "更新发票PDF地址", description = "供其他服务调用，更新发票PDF地址")
    public HttpResult<Void> updateInvoicePdfUrl(
            @Parameter(description = "发票编号", required = true)
            @RequestParam("invoiceNumber") String invoiceNumber,
            @Parameter(description = "PDF地址", required = true)
            @RequestParam("pdfUrl") String pdfUrl) {
        log.info("更新发票PDF地址: invoiceNumber={}, pdfUrl={}", invoiceNumber, pdfUrl);
        try {
            orderInvoiceSnapshotService.updateInvoicePdfUrl(invoiceNumber, pdfUrl);
            log.info("更新发票PDF地址成功: invoiceNumber={}", invoiceNumber);
            return HttpResult.ok();
        } catch (Exception e) {
            log.error("更新发票PDF地址失败: invoiceNumber={}, error={}", invoiceNumber, e.getMessage(), e);
            return HttpResult.error("更新发票PDF地址失败: " + e.getMessage());
        }
    }

    /**
     * 更新发票状态
     *
     * @param invoiceNumber 发票编号
     * @param status        发票状态
     * @return 更新结果
     */
    @PutMapping("/invoice/status")
    @Operation(summary = "更新发票状态", description = "供其他服务调用，更新发票状态")
    public HttpResult<Void> updateInvoiceStatus(
            @Parameter(description = "发票编号", required = true)
            @RequestParam("invoiceNumber") String invoiceNumber,
            @Parameter(description = "发票状态", required = true)
            @RequestParam("status") Integer status) {
        log.info("更新发票状态: invoiceNumber={}, status={}", invoiceNumber, status);
        try {
            orderInvoiceSnapshotService.updateInvoiceStatus(invoiceNumber, status);
            log.info("更新发票状态成功: invoiceNumber={}", invoiceNumber);
            return HttpResult.ok();
        } catch (Exception e) {
            log.error("更新发票状态失败: invoiceNumber={}, error={}", invoiceNumber, e.getMessage(), e);
            return HttpResult.error("更新发票状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取失败的发票编号列表
     *
     * @return 失败的发票编号列表
     */
    @GetMapping("/invoice/failed")
    @Operation(summary = "获取失败的发票编号列表", description = "获取PDF地址为空或状态为失败的发票编号列表")
    public HttpResult<List<String>> getFailedInvoiceNumbers() {
        log.info("获取失败的发票编号列表");
        try {
            List<String> failedInvoiceNumbers = orderInvoiceSnapshotService.getFailedInvoiceNumbers();
            log.info("获取失败的发票编号列表成功: 数量={}", failedInvoiceNumbers.size());
            return HttpResult.ok(failedInvoiceNumbers);
        } catch (Exception e) {
            log.error("获取失败的发票编号列表失败: error={}", e.getMessage(), e);
            return HttpResult.error("获取失败的发票编号列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID获取发票快照
     *
     * @param orderId 订单ID
     * @return 发票快照信息
     */
    @GetMapping("/invoice/snapshot/orderId/{orderId}")
    @Operation(summary = "根据订单ID获取发票快照", description = "获取订单对应的发票快照信息")
    public HttpResult<OrderInvoiceSnapshotDtoResp> getInvoiceSnapshotByOrderId(@PathVariable("orderId") String orderId) {
        log.info("获取发票快照: orderId={}", orderId);
        try {
            OrderInvoiceSnapshotDtoResp snapshot = orderInvoiceSnapshotService.getInvoiceSnapshotByOrderId(orderId);
            if (snapshot != null) {
                log.info("获取发票快照成功: orderId={}, invoiceNumber={}", orderId, snapshot.getInvoiceNumber());
                return HttpResult.ok(snapshot);
            } else {
                log.warn("发票快照不存在: orderId={}", orderId);
                return HttpResult.error("发票快照不存在");
            }
        } catch (Exception e) {
            log.error("获取发票快照失败: orderId={}, error={}", orderId, e.getMessage(), e);
            return HttpResult.error("获取发票快照失败: " + e.getMessage());
        }
    }


    /**
     * 订单导出数据查询接口
     * 供导出服务调用获取数据
     */
    @Operation(summary = "查询订单导出数据", description = "分页查询订单数据用于导出")
    @PostMapping(value = "/export/data/admin/query", produces = MediaType.APPLICATION_JSON_VALUE)
    public HttpResult<List<Map<String, Object>>> queryExportData(
            @Parameter(description = "导出参数", required = true)
            @RequestBody Map<String, Object> exportParams,
            @Parameter(description = "当前页码", required = true)
            @RequestParam(value = "pageNo") int pageNo,
            @Parameter(description = "分页大小", required = true)
            @RequestParam("pageSize") int pageSize) {
        try {
            List<Map<String, Object>> data = orderExportDataProvider.queryExportData(exportParams, pageNo, pageSize).collect(toList());
            return HttpResult.ok(data);
        } catch (Exception e) {
            log.error("查询订单导出数据失败", e);
            return HttpResult.error("查询导出数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单导出数据总数
     */
    @Operation(summary = "获取订单导出数据总数", description = "获取符合条件的订单数据总数")
    @PostMapping("/export/data/admin/count")
    public HttpResult<Long> getEstimatedCount(
            @Parameter(description = "导出参数", required = true)
            @RequestBody Map<String, Object> exportParams) {
        try {
            long count = orderExportDataProvider.getEstimatedCount(exportParams);
            return HttpResult.ok(count);
        } catch (Exception e) {
            log.error("获取订单导出数据总数失败", e);
            return HttpResult.error("获取数据总数失败: " + e.getMessage());
        }
    }
}
