package com.knet.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.order.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderInvoiceSnapshot;
import com.knet.order.model.entity.SysOrderItem;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 针对表【sys_order_invoice_snapshot(订单发票快照表)】的数据库操作Service
 */
public interface ISysOrderInvoiceSnapshotService extends IService<SysOrderInvoiceSnapshot> {

    /**
     * 创建订单发票快照
     *
     * @param orderId       订单ID
     * @param userId        用户ID
     * @param invoiceNumber 发票编号
     * @param sysOrderGroup 父订单
     * @param orderItems    订单明细
     * @return 发票快照
     */
    SysOrderInvoiceSnapshot createInvoiceSnapshot(String orderId, Long userId, String invoiceNumber, SysOrderGroup sysOrderGroup, List<SysOrderItem> orderItems);


    /**
     * 根据发票编号获取发票快照
     *
     * @param invoiceNumber 发票编号
     * @return 发票快照
     */
    SysOrderInvoiceSnapshot getInvoiceSnapshotByInvoiceNumber(String invoiceNumber);

    /**
     * 更新发票PDF地址
     *
     * @param invoiceNumber 发票编号
     * @param pdfUrl        PDF地址
     */
    void updateInvoicePdfUrl(String invoiceNumber, String pdfUrl);

    /**
     * 更新发票状态
     *
     * @param invoiceNumber 发票编号
     * @param status        发票状态
     */
    void updateInvoiceStatus(String invoiceNumber, Integer status);

    /**
     * 获取发票快照（用于API调用）
     *
     * @param invoiceNumber 发票编号
     * @return 发票快照
     */
    OrderInvoiceSnapshotDtoResp getInvoiceSnapshotForApi(String invoiceNumber);

    /**
     * 获取失败的发票编号列表
     * 包括PDF地址为空或状态为失败的发票
     *
     * @return 失败的发票编号列表
     */
    List<String> getFailedInvoiceNumbers();

    /**
     * 根据订单ID获取发票快照
     *
     * @param orderId 订单ID
     * @return 发票快照信息
     */
    OrderInvoiceSnapshotDtoResp getInvoiceSnapshotByOrderId(String orderId);

    /**
     * 根据订单ID删除发票快照
     *
     * @param orderId 订单ID
     * @return 删除结果
     */
    boolean deleteInvoiceSnapshotByOrderId(String orderId);
}
