package com.knet.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.order.model.dto.third.resp.OrderAddressInfoDtoResp;
import com.knet.order.model.dto.third.resp.OrderDetailDtoResp;
import com.knet.order.model.dto.third.resp.UserAddressDtoResp;
import com.knet.order.model.dto.third.resp.UserInvoiceAddressDtoResp;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.vo.OrderItemDataVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order(B2B订单主表)】的数据库操作Service
 */
public interface ISysOrderGroupService extends IService<SysOrderGroup> {


    /**
     * 创建订单主表记录
     *
     * @param userId          用户ID
     * @param productGroupMap 商品分组数据
     * @param addressId       订单地址
     * @return 订单主表记录
     */
    SysOrderGroup createOrderGroup(Long userId, Map<String, List<OrderItemDataVo>> productGroupMap, Long addressId);

    /**
     * 创建订单主表记录（包含发票地址）
     *
     * @param userId          用户ID
     * @param productGroupMap 商品分组数据
     * @param addressId       订单地址
     * @param billAddressId   发票地址ID
     * @return 订单主表记录
     */
    SysOrderGroup createOrderGroup(Long userId, Map<String, List<OrderItemDataVo>> productGroupMap, Long addressId, Long billAddressId);

    /**
     * 根据订单号获取订单主表记录
     *
     * @param orderId 订单号
     * @return 订单主表记录
     */
    SysOrderGroup getOrderGroupByOrderId(String orderId);

    /**
     * 更新订单状态
     *
     * @param orderId 订单ID
     * @param status  新状态
     * @return 是否更新成功
     */
    boolean updateOrderStatus(String orderId, KnetOrderGroupStatus status);

    /**
     * 检查订单是否已经取消
     *
     * @param prentOrderId 母订单ID
     * @return 是否已经取消
     */
    boolean checkOrderIsCancelled(String prentOrderId);

    /**
     * 获取订单详情（用于API调用）
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    OrderDetailDtoResp getOrderDetailForApi(String orderId);

    /**
     * 解析快递地址快照
     *
     * @param shippingAddressSnapshot 快递地址快照JSON字符串
     * @return 快递地址信息
     */
    UserAddressDtoResp parseShippingAddressSnapshot(String shippingAddressSnapshot);

    /**
     * 解析账单地址快照
     *
     * @param billingAddressSnapshot 账单地址快照JSON字符串
     * @return 账单地址信息
     */
    UserInvoiceAddressDtoResp parseBillingAddressSnapshot(String billingAddressSnapshot);

    /**
     * 获取订单的完整地址信息（包含快照）
     *
     * @param orderId 订单ID
     * @return 订单地址信息
     */
    OrderAddressInfoDtoResp getOrderAddressInfo(String orderId);
}
