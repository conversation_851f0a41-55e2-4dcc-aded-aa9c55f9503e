package com.knet.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.exception.ServiceException;
import com.knet.order.mapper.SysOrderInvoiceSnapshotMapper;
import com.knet.order.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;
import com.knet.order.model.dto.third.resp.UserAddressDtoResp;
import com.knet.order.model.dto.third.resp.UserInvoiceAddressDtoResp;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderInvoiceSnapshot;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.service.ISysOrderInvoiceSnapshotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 订单发票快照服务实现类
 */
@Slf4j
@Service
public class SysOrderInvoiceSnapshotServiceImpl extends ServiceImpl<SysOrderInvoiceSnapshotMapper, SysOrderInvoiceSnapshot>
        implements ISysOrderInvoiceSnapshotService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysOrderInvoiceSnapshot createInvoiceSnapshot(
            String parentOrderId, Long userId, String invoiceNumber, SysOrderGroup orderGroup, List<SysOrderItem> orderItems) {
        log.info("创建订单发票快照: parentOrderId={}, userId={}, invoiceNumber={}", parentOrderId, userId, invoiceNumber);
        try {
            //从快照中获取收获地址、账单地址信息
            UserAddressDtoResp shippingAddress = parseShippingAddressSnapshot(orderGroup.getShippingAddressSnapshot());
            UserInvoiceAddressDtoResp invoiceAddress = parseBillingAddressSnapshot(orderGroup.getBillingAddressSnapshot());
            SysOrderInvoiceSnapshot snapshot = SysOrderInvoiceSnapshot.builder()
                    .invoiceNumber(invoiceNumber)
                    .orderId(parentOrderId)
                    .userId(userId)
                    .invoiceDate(DateUtil.date())
                    .dueDate(DateUtil.date())
                    .totalAmount(orderGroup.getTotalAmount())
                    .invoiceStatus(0)
                    .build();
            if (BeanUtil.isEmpty(invoiceAddress)) {
                snapshot.setBillToAddress("");
            } else {
                snapshot.setBillToAddress(JSON.toJSONString(buildBillToAddress(invoiceAddress, shippingAddress)));
            }
            snapshot.setShipToAddress(JSON.toJSONString(buildShipToAddress(shippingAddress)));
            snapshot.setOrderItems(JSON.toJSONString(orderItems));
            this.save(snapshot);
            log.info("订单发票快照创建成功: invoiceNumber={}", invoiceNumber);
            return snapshot;
        } catch (Exception e) {
            log.error("创建订单发票快照失败: parentOrderId={}, error={}", parentOrderId, e.getMessage(), e);
            throw new ServiceException("创建订单发票快照失败: " + e.getMessage());
        }
    }


    @Override
    public SysOrderInvoiceSnapshot getInvoiceSnapshotByInvoiceNumber(String invoiceNumber) {
        LambdaQueryWrapper<SysOrderInvoiceSnapshot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOrderInvoiceSnapshot::getInvoiceNumber, invoiceNumber);
        return this.getOne(queryWrapper);
    }

    @Override
    public void updateInvoicePdfUrl(String invoiceNumber, String pdfUrl) {
        LambdaUpdateWrapper<SysOrderInvoiceSnapshot> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysOrderInvoiceSnapshot::getInvoiceNumber, invoiceNumber)
                .set(SysOrderInvoiceSnapshot::getInvoicePdfUrl, pdfUrl);
        this.update(updateWrapper);
        log.info("更新发票PDF地址成功: invoiceNumber={}, pdfUrl={}", invoiceNumber, pdfUrl);
    }

    @Override
    public void updateInvoiceStatus(String invoiceNumber, Integer status) {
        LambdaUpdateWrapper<SysOrderInvoiceSnapshot> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysOrderInvoiceSnapshot::getInvoiceNumber, invoiceNumber)
                .set(SysOrderInvoiceSnapshot::getInvoiceStatus, status);
        this.update(updateWrapper);
        log.info("更新发票状态成功: invoiceNumber={}, status={}", invoiceNumber, status);
    }

    /**
     * 构建账单地址
     */
    private Object buildBillToAddress(UserInvoiceAddressDtoResp invoiceAddress, UserAddressDtoResp shippingAddress) {
        if (invoiceAddress != null) {
            // 构建完整地址字符串
            StringBuilder addressBuilder = new StringBuilder();
            if (StrUtil.isNotBlank(invoiceAddress.getAddressLine1())) {
                addressBuilder.append(invoiceAddress.getAddressLine1());
            }
            if (StrUtil.isNotBlank(invoiceAddress.getAddressLine2())) {
                if (addressBuilder.length() > 0) {
                    addressBuilder.append(", ");
                }
                addressBuilder.append(invoiceAddress.getAddressLine2());
            }
            return BillToAddress.builder()
                    .recipient(invoiceAddress.getFullName())
                    .street(addressBuilder.toString())
                    .city(StrUtil.nullToEmpty(invoiceAddress.getCity()))
                    .state(StrUtil.nullToEmpty(invoiceAddress.getState()))
                    .zipCode(StrUtil.nullToEmpty(invoiceAddress.getZipCode()))
                    .country(StrUtil.nullToEmpty(invoiceAddress.getCountry()))
                    .build();
        } else {
            //做个invoiceAddress异常的兜底设计
            return BillToAddress.builder()
                    .recipient(shippingAddress.getFullName())
                    .street(shippingAddress.getAddressLine1())
                    .city(shippingAddress.getCity())
                    .state(shippingAddress.getState())
                    .zipCode(shippingAddress.getZipCode())
                    .country(shippingAddress.getCountry())
                    .build();
        }
    }

    /**
     * 构建收货地址
     */
    private Object buildShipToAddress(UserAddressDtoResp shippingAddress) {
        return ShipToAddress.builder()
                .recipient(shippingAddress.getFullName())
                .street(shippingAddress.getAddressLine1())
                .city(shippingAddress.getCity())
                .state(shippingAddress.getState())
                .zipCode(shippingAddress.getZipCode())
                .country(shippingAddress.getCountry())
                .build();
    }

    /**
     * 构建完整地址字符串
     */
    private String buildFullAddress(UserAddressDtoResp address) {
        StringBuilder fullAddress = new StringBuilder();
        if (StrUtil.isNotBlank(address.getAddressLine1())) {
            fullAddress.append(address.getAddressLine1());
        }
        if (StrUtil.isNotBlank(address.getAddressLine2())) {
            fullAddress.append(", ").append(address.getAddressLine2());
        }
        if (StrUtil.isNotBlank(address.getCity())) {
            fullAddress.append(", ").append(address.getCity());
        }
        if (StrUtil.isNotBlank(address.getState())) {
            fullAddress.append(", ").append(address.getState());
        }
        if (StrUtil.isNotBlank(address.getZipCode())) {
            fullAddress.append(" ").append(address.getZipCode());
        }
        if (StrUtil.isNotBlank(address.getCountry())) {
            fullAddress.append(", ").append(address.getCountry());
        }
        return fullAddress.toString();
    }

    /**
     * 账单地址内部类
     */
    private static class BillToAddress {
        public String recipient;
        public String street;
        public String city;
        public String state;
        public String zipCode;
        public String country;

        public static BillToAddressBuilder builder() {
            return new BillToAddressBuilder();
        }

        public static class BillToAddressBuilder {
            private BillToAddress address = new BillToAddress();

            public BillToAddressBuilder recipient(String recipient) {
                address.recipient = recipient;
                return this;
            }

            public BillToAddressBuilder street(String street) {
                address.street = street;
                return this;
            }

            public BillToAddressBuilder city(String city) {
                address.city = city;
                return this;
            }

            public BillToAddressBuilder state(String state) {
                address.state = state;
                return this;
            }

            public BillToAddressBuilder zipCode(String zipCode) {
                address.zipCode = zipCode;
                return this;
            }

            public BillToAddressBuilder country(String country) {
                address.country = country;
                return this;
            }

            public BillToAddress build() {
                return address;
            }
        }
    }

    /**
     * 收货地址内部类
     */
    private static class ShipToAddress {
        public String recipient;
        public String street;
        public String city;
        public String state;
        public String zipCode;
        public String country;

        public static ShipToAddressBuilder builder() {
            return new ShipToAddressBuilder();
        }

        public static class ShipToAddressBuilder {
            private ShipToAddress address = new ShipToAddress();

            public ShipToAddressBuilder recipient(String recipient) {
                address.recipient = recipient;
                return this;
            }

            public ShipToAddressBuilder street(String street) {
                address.street = street;
                return this;
            }

            public ShipToAddressBuilder city(String city) {
                address.city = city;
                return this;
            }

            public ShipToAddressBuilder state(String state) {
                address.state = state;
                return this;
            }

            public ShipToAddressBuilder zipCode(String zipCode) {
                address.zipCode = zipCode;
                return this;
            }

            public ShipToAddressBuilder country(String country) {
                address.country = country;
                return this;
            }

            public ShipToAddress build() {
                return address;
            }
        }
    }

    @Override
    public OrderInvoiceSnapshotDtoResp getInvoiceSnapshotForApi(String invoiceNumber) {
        SysOrderInvoiceSnapshot snapshot = this.getInvoiceSnapshotByInvoiceNumber(invoiceNumber);
        if (snapshot == null) {
            return null;
        }
        return OrderInvoiceSnapshotDtoResp.builder()
                .invoiceNumber(snapshot.getInvoiceNumber())
                .orderId(snapshot.getOrderId())
                .userId(snapshot.getUserId())
                .invoiceDate(snapshot.getInvoiceDate())
                .dueDate(snapshot.getDueDate())
                .totalAmount(snapshot.getTotalAmount())
                .billToAddress(snapshot.getBillToAddress())
                .shipToAddress(snapshot.getShipToAddress())
                .orderItems(snapshot.getOrderItems())
                .invoicePdfUrl(snapshot.getInvoicePdfUrl())
                .invoiceStatus(snapshot.getInvoiceStatus())
                .build();
    }

    @Override
    public List<String> getFailedInvoiceNumbers() {
        log.info("获取失败的发票编号列表");
        try {
            LambdaQueryWrapper<SysOrderInvoiceSnapshot> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.and(wrapper -> wrapper
                    .isNull(SysOrderInvoiceSnapshot::getInvoicePdfUrl)
                    .or()
                    .eq(SysOrderInvoiceSnapshot::getInvoiceStatus, 0)
                    .or()
                    .eq(SysOrderInvoiceSnapshot::getInvoiceStatus, 3)
            );
            queryWrapper.select(SysOrderInvoiceSnapshot::getInvoiceNumber);

            List<SysOrderInvoiceSnapshot> snapshots = this.list(queryWrapper);
            List<String> invoiceNumbers = snapshots.stream()
                    .map(SysOrderInvoiceSnapshot::getInvoiceNumber)
                    .toList();

            log.info("获取失败的发票编号列表成功: 数量={}", invoiceNumbers.size());
            return invoiceNumbers;
        } catch (Exception e) {
            log.error("获取失败的发票编号列表失败: error={}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public OrderInvoiceSnapshotDtoResp getInvoiceSnapshotByOrderId(String orderId) {
        log.info("获取发票快照: orderId={}", orderId);
        try {
            LambdaQueryWrapper<SysOrderInvoiceSnapshot> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysOrderInvoiceSnapshot::getOrderId, orderId);
            SysOrderInvoiceSnapshot snapshot = this.getOne(queryWrapper);
            if (snapshot == null) {
                log.warn("发票快照不存在: orderId={}", orderId);
                return null;
            }

            OrderInvoiceSnapshotDtoResp response = new OrderInvoiceSnapshotDtoResp();
            BeanUtils.copyProperties(snapshot, response);
            log.info("获取发票快照成功: orderId={}, invoiceNumber={}", orderId, snapshot.getInvoiceNumber());
            return response;
        } catch (Exception e) {
            log.error("获取发票快照异常: orderId={}, error={}", orderId, e.getMessage(), e);
            return null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteInvoiceSnapshotByOrderId(String orderId) {
        log.info("删除发票快照: orderId={}", orderId);
        try {
            LambdaQueryWrapper<SysOrderInvoiceSnapshot> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysOrderInvoiceSnapshot::getOrderId, orderId);
            SysOrderInvoiceSnapshot snapshot = this.getOne(queryWrapper);
            if (snapshot == null) {
                log.warn("发票快照不存在: orderId={}", orderId);
                return true;
            }
            boolean result = this.remove(queryWrapper);
            if (result) {
                log.info("发票快照删除成功: orderId={}, invoiceNumber={}", orderId, snapshot.getInvoiceNumber());
            } else {
                log.error("发票快照删除失败: orderId={}", orderId);
            }
            return result;
        } catch (Exception e) {
            log.error("删除发票快照异常: orderId={}, error={}", orderId, e.getMessage(), e);
            return false;
        }
    }

    private UserAddressDtoResp parseShippingAddressSnapshot(String shippingAddressSnapshot) {
        try {
            if (shippingAddressSnapshot == null || shippingAddressSnapshot.trim().isEmpty()) {
                log.warn("快递地址快照为空");
                return null;
            }
            return JSONUtil.toBean(shippingAddressSnapshot, UserAddressDtoResp.class);
        } catch (Exception e) {
            log.error("解析快递地址快照失败: snapshot={}, error={}", shippingAddressSnapshot, e.getMessage(), e);
            return null;
        }
    }

    private UserInvoiceAddressDtoResp parseBillingAddressSnapshot(String billingAddressSnapshot) {
        try {
            if (billingAddressSnapshot == null || billingAddressSnapshot.trim().isEmpty()) {
                log.warn("账单地址快照为空");
                return null;
            }
            return JSONUtil.toBean(billingAddressSnapshot, UserInvoiceAddressDtoResp.class);
        } catch (Exception e) {
            log.error("解析账单地址快照失败: snapshot={}, error={}", billingAddressSnapshot, e.getMessage(), e);
            return null;
        }
    }
}
