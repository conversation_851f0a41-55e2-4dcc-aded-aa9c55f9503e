package com.knet.order.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.base.HttpResult;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RandomStrUtil;
import com.knet.order.mapper.SysOrderGroupMapper;
import com.knet.order.model.dto.third.resp.*;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.vo.OrderItemDataVo;
import com.knet.order.openfeign.ApiUserServiceProvider;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderInvoiceSnapshotService;
import com.knet.order.service.ISysOrderItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order(B2B订单主表)】的数据库操作Service实现
 */
@Slf4j
@Service
public class SysOrderGroupServiceImpl extends ServiceImpl<SysOrderGroupMapper, SysOrderGroup> implements ISysOrderGroupService {

    @Resource
    private RandomStrUtil randomStrUtil;
    @Resource
    private ISysOrderItemService orderItemService;
    @Resource
    private ISysOrderInvoiceSnapshotService orderInvoiceSnapshotService;
    @Resource
    private ApiUserServiceProvider apiUserServiceProvider;
    @Resource(name = "orderThreadPoolExecutor")
    private Executor orderThreadPoolExecutor;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysOrderGroup createOrderGroup(Long userId, Map<String, List<OrderItemDataVo>> productGroupMap, Long addressId) {
        String parentOrderId = randomStrUtil.getOrderId();
        BigDecimal totalAmount = calculateTotalAmountFromGroups(productGroupMap);
        // 获取快递地址快照
        String shippingAddressSnapshot = getAddressSnapshot(addressId);
        SysOrderGroup orderGroup = SysOrderGroup.createOrderGroupWithSnapshot(
                parentOrderId, userId, totalAmount, addressId, shippingAddressSnapshot, null, null);
        this.save(orderGroup);
        return orderGroup;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysOrderGroup createOrderGroup(Long userId, Map<String, List<OrderItemDataVo>> productGroupMap, Long addressId, Long billAddressId) {
        String parentOrderId = randomStrUtil.getOrderId();
        BigDecimal totalAmount = calculateTotalAmountFromGroups(productGroupMap);
        CompletableFuture<String> shippingFuture =
                CompletableFuture.supplyAsync(() -> getAddressSnapshot(addressId), orderThreadPoolExecutor);
        CompletableFuture<String> billingFuture =
                billAddressId != null ? CompletableFuture.supplyAsync(() -> getBillingAddressSnapshot(billAddressId), orderThreadPoolExecutor)
                        : CompletableFuture.completedFuture(null);
        String shippingAddressSnapshot = shippingFuture.join();
        String billingAddressSnapshot = billingFuture.join();
        SysOrderGroup orderGroup = SysOrderGroup.createOrderGroupWithSnapshot(
                parentOrderId, userId, totalAmount, addressId, shippingAddressSnapshot,
                billAddressId, billingAddressSnapshot);
        this.save(orderGroup);
        return orderGroup;
    }

    /**
     * 获取快递地址快照
     *
     * @param addressId 地址ID
     * @return 地址快照JSON字符串
     */
    private String getAddressSnapshot(Long addressId) {
        try {
            log.info("获取快递地址快照: addressId={}", addressId);
            HttpResult<UserAddressDtoResp> addressResult = apiUserServiceProvider.getAddressById(addressId);
            if (addressResult == null || !addressResult.success() || addressResult.getData() == null) {
                log.error("获取快递地址失败: addressId={}, result={}", addressId, addressResult);
                throw new ServiceException("获取快递地址信息失败");
            }
            UserAddressDtoResp addressInfo = addressResult.getData();
            String snapshot = JSONUtil.toJsonStr(addressInfo);
            log.info("快递地址快照生成成功: addressId={}", addressId);
            return snapshot;
        } catch (Exception e) {
            log.error("获取快递地址快照失败: addressId={}, error={}", addressId, e.getMessage(), e);
            throw new ServiceException("获取快递地址快照失败: " + e.getMessage());
        }
    }

    /**
     * 获取账单地址快照
     *
     * @param billAddressId 账单地址ID
     * @return 账单地址快照JSON字符串
     */
    private String getBillingAddressSnapshot(Long billAddressId) {
        try {
            log.info("获取账单地址快照: billAddressId={}", billAddressId);
            HttpResult<UserInvoiceAddressDtoResp> billingAddressResult = apiUserServiceProvider.getOrderInvoiceAddress(billAddressId);
            if (billingAddressResult == null || !billingAddressResult.success() || billingAddressResult.getData() == null) {
                log.error("获取账单地址失败: billAddressId={}, result={}", billAddressId, billingAddressResult);
                throw new ServiceException("获取账单地址信息失败");
            }
            UserInvoiceAddressDtoResp billingAddressInfo = billingAddressResult.getData();
            String snapshot = JSONUtil.toJsonStr(billingAddressInfo);
            log.info("账单地址快照生成成功: billAddressId={}", billAddressId);
            return snapshot;
        } catch (Exception e) {
            log.error("获取账单地址快照失败: billAddressId={}, error={}", billAddressId, e.getMessage(), e);
            throw new ServiceException("获取账单地址快照失败: " + e.getMessage());
        }
    }

    /**
     * 计算母订单总金额（从商品分组计算）
     */
    private BigDecimal calculateTotalAmountFromGroups(Map<String, List<OrderItemDataVo>> productGroupMap) {
        return productGroupMap.values()
                .stream()
                .flatMap(List::stream)
                .map(item -> item.getUnitPrice().multiply(new BigDecimal(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public SysOrderGroup getOrderGroupByOrderId(String orderId) {
        LambdaQueryWrapper<SysOrderGroup> groupQueryWrapper = new LambdaQueryWrapper<>();
        groupQueryWrapper.eq(SysOrderGroup::getOrderId, orderId);
        return this.getOne(groupQueryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderStatus(String parentOrderId, KnetOrderGroupStatus status) {
        log.info("更新父订单状态: parentOrderId={}, status={}", parentOrderId, status.getName());
        try {
            LambdaUpdateWrapper<SysOrderGroup> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(SysOrderGroup::getOrderId, parentOrderId)
                    .set(SysOrderGroup::getStatus, status);
            boolean updated = this.update(null, updateWrapper);
            log.info("父订单状态更新结果: parentOrderId={}, status={}, result={}", parentOrderId, status.getName(), updated);
            return updated;
        } catch (Exception e) {
            log.error("更新父订单状态失败: parentOrderId={}, status={}, error={}", parentOrderId, status.getName(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查订单是否已经取消
     *
     * @param prentOrderId 母订单号
     * @return 是否已经取消
     */
    @Override
    public boolean checkOrderIsCancelled(String prentOrderId) {
        SysOrderGroup orderGroup = this.getOrderGroupByOrderId(prentOrderId);
        if (BeanUtil.isEmpty(orderGroup)) {
            throw new ServiceException("订单不存在: " + prentOrderId);
        }
        if (KnetOrderGroupStatus.CANCELLED.equals(orderGroup.getStatus()) ||
                KnetOrderGroupStatus.SYSTEM_CANCELLED.equals(orderGroup.getStatus())) {
            log.info("订单已经处于取消状态，无需重复取消: orderId={}, status={}", prentOrderId, orderGroup.getStatus());
            return true;
        }
        return false;
    }

    @Override
    public OrderDetailDtoResp getOrderDetailForApi(String orderId) {
        SysOrderGroup orderGroup = this.getOrderGroupByOrderId(orderId);
        if (orderGroup == null) {
            return null;
        }
        List<SysOrderItem> orderItems = orderItemService.getOrderItemsByPrentOrderId(orderId);
        List<OrderItemDtoResp> orderItemDtos = orderItems.stream()
                .map(item -> OrderItemDtoResp.builder()
                        .productName(item.getName())
                        .sku(item.getSku())
                        .size(item.getSize())
                        .quantity(item.getCount())
                        .amount(item.getPrice())
                        .build())
                .toList();
        int totalQuantity = orderItems.stream().mapToInt(SysOrderItem::getCount).sum();
        String invoicePdfUrl = null;
        try {
            OrderInvoiceSnapshotDtoResp invoiceSnapshot = orderInvoiceSnapshotService.getInvoiceSnapshotByOrderId(orderId);
            if (invoiceSnapshot != null) {
                invoicePdfUrl = invoiceSnapshot.getInvoicePdfUrl();
            }
        } catch (Exception e) {
            log.warn("获取发票PDF地址失败: orderId={}, error={}", orderId, e.getMessage());
        }
        return OrderDetailDtoResp.builder()
                .orderId(orderGroup.getOrderId())
                .userId(orderGroup.getUserId())
                .orderStatus(orderGroup.getStatus().getCode())
                .totalQuantity(totalQuantity)
                .totalAmount(orderGroup.getTotalAmount())
                .addressId(orderGroup.getAddressId())
                .createTime(orderGroup.getCreateTime())
                .orderItems(orderItemDtos)
                .invoicePdfUrl(invoicePdfUrl)
                .build();
    }

    /**
     * 解析快递地址快照
     *
     * @param shippingAddressSnapshot 快递地址快照JSON字符串
     * @return 快递地址信息
     */
    @Override
    public UserAddressDtoResp parseShippingAddressSnapshot(String shippingAddressSnapshot) {
        try {
            if (shippingAddressSnapshot == null || shippingAddressSnapshot.trim().isEmpty()) {
                log.warn("快递地址快照为空");
                return null;
            }
            return JSONUtil.toBean(shippingAddressSnapshot, UserAddressDtoResp.class);
        } catch (Exception e) {
            log.error("解析快递地址快照失败: snapshot={}, error={}", shippingAddressSnapshot, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析账单地址快照
     *
     * @param billingAddressSnapshot 账单地址快照JSON字符串
     * @return 账单地址信息
     */
    @Override
    public UserInvoiceAddressDtoResp parseBillingAddressSnapshot(String billingAddressSnapshot) {
        try {
            if (billingAddressSnapshot == null || billingAddressSnapshot.trim().isEmpty()) {
                log.warn("账单地址快照为空");
                return null;
            }
            return JSONUtil.toBean(billingAddressSnapshot, UserInvoiceAddressDtoResp.class);
        } catch (Exception e) {
            log.error("解析账单地址快照失败: snapshot={}, error={}", billingAddressSnapshot, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证地址快照的有效性
     *
     * @param addressSnapshot 地址快照JSON字符串
     * @return 是否有效
     */
    private boolean isValidAddressSnapshot(String addressSnapshot) {
        if (addressSnapshot == null || addressSnapshot.trim().isEmpty()) {
            return false;
        }
        try {
            JSONUtil.parseObj(addressSnapshot);
            return true;
        } catch (Exception e) {
            log.warn("地址快照格式无效: snapshot={}, error={}", addressSnapshot, e.getMessage());
            return false;
        }
    }

    /**
     * 获取订单的完整地址信息（包含快照）
     *
     * @param orderId 订单ID
     * @return 订单地址信息
     */
    @Override
    public OrderAddressInfoDtoResp getOrderAddressInfo(String orderId) {
        SysOrderGroup orderGroup = this.getOrderGroupByOrderId(orderId);
        if (orderGroup == null) {
            throw new ServiceException("订单不存在: " + orderId);
        }

        OrderAddressInfoDtoResp.OrderAddressInfoDtoRespBuilder builder = OrderAddressInfoDtoResp.builder()
                .orderId(orderId)
                .addressId(orderGroup.getAddressId())
                .billAddressId(orderGroup.getBillAddressId());

        // 解析快递地址快照
        if (orderGroup.getShippingAddressSnapshot() != null) {
            UserAddressDtoResp shippingAddress = parseShippingAddressSnapshot(orderGroup.getShippingAddressSnapshot());
            builder.shippingAddress(shippingAddress);
        }

        // 解析账单地址快照
        if (orderGroup.getBillingAddressSnapshot() != null) {
            UserInvoiceAddressDtoResp billingAddress = parseBillingAddressSnapshot(orderGroup.getBillingAddressSnapshot());
            builder.billingAddress(billingAddress);
        }

        return builder.build();
    }
}
