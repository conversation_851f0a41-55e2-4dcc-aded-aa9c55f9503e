package com.knet.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpStatus;
import com.knet.common.base.HttpResult;
import com.knet.order.model.dto.third.req.KnetCreateShipment;
import com.knet.order.model.dto.third.resp.KnetShopLabelCenterVo;
import com.knet.order.openfeign.ApiKnetGroupService;
import com.knet.order.service.IThirdApiService;
import com.knet.order.system.retry.RetryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/16 14:47
 * @description:
 */
@Slf4j
@Service
public class ThirdApiServiceImpl implements IThirdApiService {
    @Resource
    private ApiKnetGroupService apiKnetGroupService;
    @Resource
    private RetryWrapper retryWrapper;

    @Override
    public KnetShopLabelCenterVo getShippingLabel(KnetCreateShipment request) {
        log.info("从KG 获取物流面单 request: {}", request);
        HttpResult<List<KnetShopLabelCenterVo>> response;
        try {
            response = retryWrapper.executeWithRetry(
                    () -> apiKnetGroupService.getShippingLabel(request),
                    "从KG 获取物流面单");
            log.info("从KG 获取物流面单 success response: {}", response);
        } catch (Exception e) {
            log.error("调用apiKnetGroupService.getShippingLabel时发生异常", e);
            return null;
        }
        if (BeanUtil.isEmpty(response) || BeanUtil.isEmpty(response.getData())) {
            log.error("从KG 获取物流面单失败(服务异常) response: {}", response);
            return null;
        }
        if (HttpStatus.HTTP_OK != response.getCode()) {
            log.error("从KG 获取物流面单失败 response: {}", response.getMsg());
            return null;
        }
        return response.getData().get(0);
    }
}
