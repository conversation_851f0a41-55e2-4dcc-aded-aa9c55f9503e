package com.knet.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.PaymentResultMessage;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.common.enums.KnetPaymentFlowStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.InvoiceNumberUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.openfeign.ApiUserServiceProvider;
import com.knet.order.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/17 13:56
 * @description: 支付结果 消息处理服务
 */
@Service
@Slf4j
public class PaymentResultServiceImpl implements IPaymentResultService {
    @Resource
    private ISysOrderGroupService orderGroupService;
    @Resource
    private ISysOrderService orderService;
    @Resource
    private ISysOrderItemService orderItemService;
    @Resource
    private ISysOrderInvoiceSnapshotService orderInvoiceSnapshotService;
    @Resource
    private InvoiceNumberUtil invoiceNumberUtil;
    @Resource
    private ApiUserServiceProvider apiUserServiceProvider;

    /**
     * 处理支付结果
     *
     * @param messageBody 消息体
     */
    @Override
    public void processPaymentResult(String messageBody) {
        PaymentResultMessage message = JSON.parseObject(messageBody, PaymentResultMessage.class);
        log.info("订单服务处理支付结果消息: {}", messageBody);
        try {
            String parentOrderId = message.getOrderId();
            String paymentStatus = message.getStatus();
            // 根据支付状态更新订单状态
            if (KnetPaymentFlowStatus.SUCCESS.getName().equals(paymentStatus)) {
                // 支付成功，更新订单状态为已支付
                updateOrderStatus(parentOrderId, KnetOrderGroupStatus.PAID);
                cleanShopCart(message.getUserId());
                log.info("订单支付成功，订单状态已更新: parentOrderId={}", parentOrderId);
                createInvoiceSnapshotAsync(parentOrderId, message.getUserId());
            }
            if (KnetPaymentFlowStatus.FAILED.getName().equals(paymentStatus)) {
                // 支付失败，更新订单状态为支付失败
                updateOrderStatus(parentOrderId, KnetOrderGroupStatus.PAY_FAILED);
                log.info("订单支付失败，订单状态已更新为支付失败: parentOrderId={}", parentOrderId);
            }
            //清除订单缓存
            clearUserOrderListCache(message.getUserId());
        } catch (Exception e) {
            log.error("处理支付结果失败: orderId={}, error={}", message.getOrderId(), e.getMessage());
            throw new ServiceException("处理支付结果失败: " + e.getMessage());
        }
    }

    /**
     * 异步创建发票快照
     *
     * @param parentOrderId 订单ID
     * @param userId        用户ID
     */
    private void createInvoiceSnapshotAsync(String parentOrderId, Long userId) {
        try {
            String invoiceNumber = invoiceNumberUtil.generateInvoiceNumber();
            SysOrderGroup orderGroup = orderGroupService.getOrderGroupByOrderId(parentOrderId);
            if (BeanUtil.isEmpty(orderGroup)) {
                throw new ServiceException("订单不存在: " + parentOrderId);
            }
            List<SysOrderItem> orderItems = orderItemService.getOrderItemsByPrentOrderId(parentOrderId).stream()
                    .filter(vo -> !vo.getStatus().invoiceFilter())
                    .toList();
            if (BeanUtil.isEmpty(orderItems)) {
                throw new ServiceException("订单商品明细不存在: " + parentOrderId);
            }
            orderInvoiceSnapshotService.createInvoiceSnapshot(parentOrderId, userId, invoiceNumber, orderGroup, orderItems);
            log.info("发票快照创建成功: parentOrderId={}, invoiceNumber={}", parentOrderId, invoiceNumber);
        } catch (Exception e) {
            // 发票快照创建失败不影响主流程，只记录日志
            log.error("发票快照创建失败，但不影响主流程: parentOrderId={}, userId={}, error={}", parentOrderId, userId, e.getMessage(), e);
        }
    }

    /**
     * 更新订单状态
     *
     * @param orderId 订单ID
     * @param status  新状态
     */
    private void updateOrderStatus(String orderId, KnetOrderGroupStatus status) {
        // 更新父订单状态
        boolean groupUpdated = orderGroupService.updateOrderStatus(orderId, status);
        if (!groupUpdated) {
            throw new ServiceException("更新父订单状态失败: " + orderId);
        }
        // 更新子订单状态
        boolean orderUpdated = orderService.updateOrderStatusByParentId(orderId, status);
        if (!orderUpdated) {
            throw new ServiceException("更新子订单状态失败: " + orderId);
        }
        // 更新item订单状态
        switch (status) {
            case PAID -> orderItemService.updateOrderStatusByParentId(orderId, KnetOrderItemStatus.PAID);
            case PAY_FAILED -> orderItemService.updateOrderStatusByParentId(orderId, KnetOrderItemStatus.PAY_FAILED);
            default -> log.info("更新item订单状态: orderId={}, status={}", orderId, status.getName());
        }
        log.info("订单状态更新成功: orderId={}, status={}", orderId, status.getName());
    }

    /**
     * 清除用户订单列表缓存
     * 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
     */
    private void clearUserOrderListCache(Long userId) {
        try {
            // 构建缓存key模式，匹配该用户的所有订单列表缓存
            String cacheKeyPattern = "order-service:orderList:" + userId + ":*";
            // 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
            RedisCacheUtil.deleteByPattern(cacheKeyPattern);
            log.info("已清除用户订单列表缓存，用户ID: {}, 缓存key模式: {}", userId, cacheKeyPattern);
        } catch (Exception e) {
            log.warn("清除用户订单列表缓存失败，用户ID: {}, 错误: {}", userId, e.getMessage());
        }
    }


    /**
     * 清除购物车
     *
     * @param userId 用户ID
     */
    private void cleanShopCart(@NotNull(message = "用户ID不能为空") Long userId) {
        apiUserServiceProvider.removeFromCart(userId);
    }
}
