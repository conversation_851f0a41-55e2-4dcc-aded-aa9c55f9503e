package com.knet.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.order.model.dto.req.AdminOrderListQueryRequest;
import com.knet.order.model.dto.resp.ExportAdminSaleOrderResponse;

/**
 * <AUTHOR>
 * @date 2025/9/26 11:16
 * @description: 订单服务数据导出查询实现
 */
public interface IOrderExportQueryService {
    /**
     * 管理员导出订单数据
     *
     * @param request 查询条件
     * @return 导出数据列表
     */
    IPage<ExportAdminSaleOrderResponse> queryAllOrderList(AdminOrderListQueryRequest request);
}
