package com.knet.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.knet.common.base.HttpResult;
import com.knet.common.dto.message.NotificationMessage;
import com.knet.order.model.dto.third.req.KnetCreateShipment;
import com.knet.order.model.dto.third.resp.KnetShopLabelCenterVo;
import com.knet.order.model.dto.third.resp.UserAddressDtoResp;
import com.knet.order.model.dto.third.resp.UserInfoDtoResp;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.entity.SysShippingLabel;
import com.knet.order.mq.producer.OrderProducer;
import com.knet.order.openfeign.ApiUserServiceProvider;
import com.knet.order.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/16 10:30
 * @description: 分配物流标签服务实现
 */
@Slf4j
@Service
public class DistributionShippingLabelServiceImpl implements IDistributionShippingLabelService {

    @Resource
    private ISysOrderItemService iSysOrderItemService;
    @Resource
    private ISysOrderGroupService iSysOrderGroupService;
    @Resource
    private ApiUserServiceProvider apiUserServiceProvider;
    @Resource
    private ISysShippingLabelService iSysShippingLabelService;
    @Resource
    private ISysShippingItemRelService iSysShippingItemRelService;
    @Resource
    private IThirdApiService thirdApiService;
    @Resource
    private ISysOrderProcessService iSysOrderProcessService;
    @Resource
    private OrderProducer orderProducer;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void distributionShippingLabel(String prentOrderId) {
        SysOrderGroup orderGroup = iSysOrderGroupService.getOrderGroupByOrderId(prentOrderId);
        if (orderGroup == null) {
            log.error("父订单不存在，无法分配物流标签: prentOrderId={}", prentOrderId);
            return;
        }
        //从快照中获取收货地址信息
        UserAddressDtoResp addressInfo = iSysOrderGroupService.parseShippingAddressSnapshot(orderGroup.getShippingAddressSnapshot());
        if (BeanUtil.isEmpty(addressInfo)) {
            //todo 异常订单处理机制 没有物流单号的，人工介入处置
            log.error("订单缺少地址信息，无法分配物流标签: prentOrderId={}", prentOrderId);
            return;
        }
        // 从KG获取物流标签 根据仓库信息，同一个仓库10件商品一个shipping_label
        List<SysOrderItem> items = iSysOrderItemService.getOrderItemsByPrentOrderId(prentOrderId);
        log.info("根据父订单ID获取订单明细: prentOrderId={}, count={}", prentOrderId, items.size());
        if (items.isEmpty()) {
            log.warn("订单明细为空，无法分配物流标签: prentOrderId={}", prentOrderId);
            return;
        }
        // 按仓库分组
        Map<String, List<SysOrderItem>> warehouseGroups = new HashMap<>(12);
        for (SysOrderItem item : items) {
            String warehouse = item.getWarehouse();
            if (warehouse == null || warehouse.isEmpty()) {
                log.warn("订单项缺少仓库信息，无法分配物流标签: itemId={}", item.getItemId());
                continue;
            }
            warehouseGroups.computeIfAbsent(warehouse, k -> new ArrayList<>()).add(item);
        }
        // 每个仓库每10件商品分配一个物流标签
        for (Map.Entry<String, List<SysOrderItem>> entry : warehouseGroups.entrySet()) {
            String warehouse = entry.getKey();
            List<SysOrderItem> warehouseItems = entry.getValue();
            // 处理同一仓库下的商品
            processWarehouseItems(prentOrderId, warehouse, warehouseItems, addressInfo);
        }
        // 智能更新订单状态（根据物流标签分配情况自动判断）
        try {
            boolean result = iSysOrderProcessService.smartUpdateOrderStatus(prentOrderId);
            if (result) {
                // 发送订单通知消息到notification-service
                sendOrderNotificationMessages(prentOrderId);
                log.info("订单状态已根据物流标签分配情况自动更新: prentOrderId={}", prentOrderId);
            } else {
                log.warn("订单状态更新失败: prentOrderId={}", prentOrderId);
            }
        } catch (Exception e) {
            log.error("更新订单状态时发生异常: prentOrderId={}, error={}", prentOrderId, e.getMessage(), e);
        }
        log.info("物流标签分配完成: prentOrderId={}", prentOrderId);
    }

    /**
     * 处理同一仓库下的商品分配物流标签
     *
     * @param parentOrderId  父订单ID
     * @param warehouse      仓库
     * @param warehouseItems 仓库商品
     * @param addressInfo    地址信息
     */
    private void processWarehouseItems(String parentOrderId, String warehouse, List<SysOrderItem> warehouseItems,
                                       UserAddressDtoResp addressInfo) {
        int currentItemCount = 0;
        List<SysOrderItem> currentBatch = new ArrayList<>();
        for (SysOrderItem item : warehouseItems) {
            int count = item.getCount();
            currentBatch.add(item);
            currentItemCount += count;
            // 如果当前批次已满或者是最后一个商品，获取物流标签并保存数据
            if (currentItemCount >= 10 || isLastItem(warehouseItems, item)) {
                // 从KG系统获取一个物流标签
                KnetShopLabelCenterVo shippingLabelFromKg = getShippingLabelFromKg(addressInfo, warehouse, (long) currentItemCount);
                if (BeanUtil.isNotEmpty(shippingLabelFromKg)) {
                    SysShippingLabel shippingLabel = iSysShippingLabelService.createShippingLabel(SysShippingLabel.create(shippingLabelFromKg));
                    for (SysOrderItem batchItem : currentBatch) {
                        iSysShippingItemRelService.saveShippingItemRel(shippingLabel.getId(), batchItem.getItemId());
                        log.info("为商品分配物流标签: itemId={}, sku={}", batchItem.getItemId(), batchItem.getSku());
                    }
                    // 重置当前批次和计数
                    currentBatch = new ArrayList<>();
                    currentItemCount = 0;
                } else {
                    log.error("获取物流标签失败: warehouse={}", warehouse);
                    //todo 归属异常订单 订单增加异常状态
                    break;
                }
            }
        }
    }

    /**
     * 判断是否是最后一个商品
     *
     * @param items       商品列表
     * @param currentItem 当前商品
     * @return 是否是最后一个商品
     */
    private boolean isLastItem(List<SysOrderItem> items, SysOrderItem currentItem) {
        return items.indexOf(currentItem) == items.size() - 1;
    }

    /**
     * 从KG系统获取物流标签
     *
     * @param addressInfo 收件地址信息
     * @param warehouse   发货仓库
     * @param count       包裹数
     * @return 物流标签编码
     */
    private KnetShopLabelCenterVo getShippingLabelFromKg(UserAddressDtoResp addressInfo, String warehouse, Long count) {
        try {
            log.info("向KG系统请求物流标签");
            // 调用KG系统API获取物流标签
            KnetCreateShipment shipment = KnetCreateShipment.create(addressInfo, warehouse, count);
            KnetShopLabelCenterVo shippingLabel = thirdApiService.getShippingLabel(shipment);
            log.info("成功从KG系统获取物流标签: label={}", shippingLabel);
            return shippingLabel;
        } catch (Exception e) {
            log.error("从KG系统获取物流标签异常: error={}", e.getMessage(), e);
            // 异常情况下，返回null表示获取失败
            return null;
        }
    }

    /**
     * 发送订单通知消息到notification-service
     * 每个父订单只发送一次消息，包含订单汇总信息
     *
     * @param parentOrderId 父订单ID
     */
    private void sendOrderNotificationMessages(String parentOrderId) {
        try {
            // 获取父订单信息
            SysOrderGroup orderGroup = iSysOrderGroupService.getOrderGroupByOrderId(parentOrderId);
            if (orderGroup == null) {
                log.warn("未找到父订单信息，无法发送订单通知: parentOrderId={}", parentOrderId);
                return;
            }
            // 获取所有子订单项
            List<SysOrderItem> orderItems = iSysOrderItemService.getOrderItemsByPrentOrderId(parentOrderId);
            if (orderItems.isEmpty()) {
                log.warn("未找到子订单项，无法发送订单通知: parentOrderId={}", parentOrderId);
                return;
            }
            String userAccount = getUserAccount(orderGroup.getUserId());
            Integer purchaseQty = orderItems.size();
            String totalPrice = String.format("$%.2f", orderGroup.getTotalAmount());
            String purchaseTime = formatPurchaseTime(orderItems.get(0).getPaidTime());
            NotificationMessage notificationMessage = NotificationMessage.createOrderNotice(
                    parentOrderId,
                    userAccount,
                    purchaseQty,
                    totalPrice,
                    purchaseTime
            );
            orderProducer.sendOrderNotificationMessage(notificationMessage);
            log.info("已发送订单通知消息: parentOrderId={}, userAccount={}, purchaseQty={}, totalPrice={}, purchaseTime={}",
                    parentOrderId, userAccount, purchaseQty, totalPrice, purchaseTime);
        } catch (Exception e) {
            log.error("发送订单通知消息异常: parentOrderId={}, error={}", parentOrderId, e.getMessage(), e);
        }
    }

    /**
     * 获取用户账号
     *
     * @param userId 用户ID
     * @return 用户账号
     */
    private String getUserAccount(Long userId) {
        try {
            HttpResult<List<UserInfoDtoResp>> result = apiUserServiceProvider.getUsersByIds(Collections.singletonList(userId));
            if (result == null || result.getCode() != 200 || CollUtil.isEmpty(result.getData())) {
                log.warn("获取用户账号信息失败或为空, userIds: {}", userId);
                return "User" + userId;
            }
            Map<Long, String> userAccountMap = result.getData().stream()
                    .collect(Collectors.toMap(
                            UserInfoDtoResp::getId,
                            UserInfoDtoResp::getAccount,
                            (existing, replacement) -> existing
                    ));
            return userAccountMap.getOrDefault(userId, "User" + userId);
        } catch (Exception e) {
            log.error("获取用户账号失败: userId={}, error={}", userId, e.getMessage(), e);
            return "Unknown";
        }
    }

    /**
     * 格式化购买时间
     *
     * @param createTime 创建时间
     * @return 格式化后的时间字符串
     */
    private String formatPurchaseTime(Date createTime) {
        try {
            TimeZone easternTimeZone = TimeZone.getTimeZone("America/New_York");
            return DateUtil.format(
                    DateUtil.date(createTime).setTimeZone(easternTimeZone),
                    "yyyy/MM/dd HH:mm:ss"
            );
        } catch (Exception e) {
            log.error("格式化购买时间失败: createTime={}, error={}", createTime, e.getMessage(), e);
            return "Unknown";
        }
    }
}
