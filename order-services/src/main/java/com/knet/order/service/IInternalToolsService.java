package com.knet.order.service;

import com.knet.common.base.HttpResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/9/4 10:51
 * @description: 内部工具服务接口
 */
public interface IInternalToolsService {

    /**
     * 获取用户账号映射
     *
     * @param userIds 用户ID列表
     * @return 用户账号映射
     */
    Map<Long, String> getUserAccountMap(List<Long> userIds);

    /**
     * 根据账户名模糊搜索获取用户ID列表
     *
     * @param account 账户名（模糊搜索）
     * @return 用户ID列表
     */
    HttpResult<List<Long>> getUserIdsByAccount(String account);
}
