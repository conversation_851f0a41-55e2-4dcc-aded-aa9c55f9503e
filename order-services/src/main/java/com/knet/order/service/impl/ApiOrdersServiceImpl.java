package com.knet.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.base.HttpResult;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.common.exception.ServiceException;
import com.knet.order.mapper.SysOrderItemMapper;
import com.knet.order.model.dto.req.ReplaceProductRequest;
import com.knet.order.model.dto.resp.ProductOnSaleInfoResp;
import com.knet.order.model.dto.resp.ReplaceProductResp;
import com.knet.order.model.dto.third.req.KnetB2bOrderQueryRequest;
import com.knet.order.model.dto.third.req.UpdatedOrderRequest;
import com.knet.order.model.dto.third.resp.KnetB2bOrderQueryVo;
import com.knet.order.model.dto.third.resp.OrderAndLabelVo;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.vo.SubOrderGroupVo;
import com.knet.order.model.vo.SubOrderItemVo;
import com.knet.order.openfeign.ApiGoodsServiceProvider;
import com.knet.order.service.IApiOrdersService;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderItemService;
import com.knet.order.service.ISysOrderProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:28
 * @description: 订单对外提供服务实现类
 */
@Slf4j
@Service
public class ApiOrdersServiceImpl implements IApiOrdersService {

    @Resource
    private ISysOrderItemService orderItemService;
    @Resource
    private ISysOrderGroupService orderGroupService;
    @Resource
    private ISysOrderProcessService iSysOrderProcessService;
    @Resource
    private SysOrderItemMapper sysOrderItemMapper;
    @Resource
    private ApiGoodsServiceProvider apiGoodsServiceProvider;

    @Override
    public List<SubOrderItemVo> getOrderItemsByPrentOrderId(String prentOrderId) {
        List<SysOrderItem> items = orderItemService.getOrderItemsByPrentOrderId(prentOrderId);
        Map<String, SubOrderItemVo> groupedItems = new HashMap<>(12);
        for (SysOrderItem item : items) {
            String groupKey = item.getSku() + "_" + item.getSize() + "_" + item.getPrice();
            if (groupedItems.containsKey(groupKey)) {
                SubOrderItemVo existingDto = groupedItems.get(groupKey);
                existingDto.setCount(existingDto.getCount() + item.getCount());
            } else {
                SubOrderItemVo newDto = SubOrderItemVo.create(item);
                groupedItems.put(groupKey, newDto);
            }
        }
        return new ArrayList<>(groupedItems.values());
    }

    @Override
    public SubOrderGroupVo getOrderGroupByPrentOrderId(String prentOrderId) {
        log.info("根据父订单ID获取订单信息: prentOrderId={}", prentOrderId);
        try {
            SysOrderGroup orderGroup = orderGroupService.getOrderGroupByOrderId(prentOrderId);
            if (orderGroup == null) {
                log.warn("未找到订单信息: prentOrderId={}", prentOrderId);
                return null;
            }
            // 手动创建SubOrderGroupVo对象
            SubOrderGroupVo vo = new SubOrderGroupVo();
            vo.setParentOrderId(orderGroup.getOrderId());
            vo.setTotalPrice(orderGroup.getTotalAmount());
            vo.setStatus(orderGroup.getStatus());
            return vo;
        } catch (Exception e) {
            log.error("获取订单信息失败: prentOrderId={}, error={}", prentOrderId, e.getMessage(), e);
            throw new ServiceException("获取订单信息失败: " + e.getMessage());
        }
    }

    @Override
    public IPage<KnetB2bOrderQueryVo> getOrderItemsByPage(KnetB2bOrderQueryRequest request) {
        log.info("分页查询订单明细: request={}", request);
        Page<OrderAndLabelVo> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<OrderAndLabelVo> db = sysOrderItemMapper.getOrderItemsByPage(page, request);
        log.info("分页查询订单明细: request={}", request);
        return db.convert(KnetB2bOrderQueryVo::create);
    }


    @DistributedLock(key = "'KG_UPDATE_ORDER_STATUS:' + #request.hashCode()", expire = 1)
    @Override
    public boolean updateOrderStatus(UpdatedOrderRequest request) {
        return iSysOrderProcessService.smartUpdateOrderStatus(request);
    }

    @Override
    public SysOrderItem getOrderItemDetails(String itemId) {
        log.info("根据订单项ID获取订单项详情: itemId={}", itemId);
        try {
            if (StrUtil.isBlank(itemId)) {
                throw new ServiceException("订单项ID不能为空");
            }
            return orderItemService.getById(itemId);
        } catch (Exception e) {
            log.error("获取订单项详情失败: itemId={}, error={}", itemId, e.getMessage(), e);
            throw new ServiceException("获取订单项详情失败: " + e.getMessage());
        }
    }

    @Override
    public List<SysOrderItem> queryOrderItemsByPrentOrderId(String prentOrderId) {
        log.info("根据父订单号获取订单item详情列表: prentOrderId={}", prentOrderId);
        try {
            if (StrUtil.isBlank(prentOrderId)) {
                throw new ServiceException("父订单ID不能为空");
            }
            return orderItemService.getOrderItemsByPrentOrderId(prentOrderId);
        } catch (Exception e) {
            log.error("获取订单项详情列表失败: prentOrderId={}, error={}", prentOrderId, e.getMessage(), e);
            throw new ServiceException("获取订单项详情列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReplaceProductResp replaceOrderProduct(ReplaceProductRequest request) {
        log.info("替换订单商品信息: request={}", request);
        log.info("开始替换订单商品: itemNo={}, newOneId={}",
                request.getItemNo(), request.getNewOneId());
        SysOrderItem orderItem = orderItemService.getOrderItemByItemNo(request.getItemNo());
        if (BeanUtil.isEmpty(orderItem)) {
            throw new ServiceException("订单项不存在: " + request.getItemNo());
        }
        String oldOneId = orderItem.getOneId();
        String oldListingId = orderItem.getKnetListingId();
        HttpResult<ProductOnSaleInfoResp> onSaleInfoResult = apiGoodsServiceProvider.getProductOnSaleInfo(request.getNewOneId());
        if (!onSaleInfoResult.success()) {
            throw new ServiceException("获取商品上架状态信息失败: " + onSaleInfoResult.getMsg());
        }
        ProductOnSaleInfoResp onSaleInfo = onSaleInfoResult.getData();
        String newListingId;
        if (Boolean.TRUE.equals(onSaleInfo.getOnSale())) {
            // 如果已上架，直接使用返回的listingId并锁定商品，标记为超卖替换
            newListingId = onSaleInfo.getListingId();
            // 锁定商品并标记为超卖替换
            HttpResult<Void> lockResult = apiGoodsServiceProvider.lockProductForOversellReplacement(request.getNewOneId());
            if (!lockResult.success()) {
                throw new ServiceException("锁定商品失败: " + lockResult.getMsg());
            }
            log.info("商品已上架，获取到listingId: {}，已锁定并标记为超卖替换", newListingId);
        } else {
            log.error("替换商品未上架，请检查商品状态 oneId:{}", request.getNewOneId());
            throw new ServiceException("替换商品未上架，请检查商品状态 oneId: " + request.getNewOneId());
        }
        // 更新订单项信息
        orderItemService.updateOrderItem(request.getItemNo(), request.getNewOneId(), newListingId);
        log.info("订单商品替换成功: itemNo={}, {}→{}, {}→{}",
                request.getItemNo(), oldOneId, request.getNewOneId(), oldListingId, newListingId);
        return ReplaceProductResp.builder()
                .itemNo(request.getItemNo())
                .oldOneId(oldOneId)
                .oldListingId(oldListingId)
                .newOneId(request.getNewOneId())
                .newListingId(newListingId)
                .success(true)
                .message("超卖替换成功")
                .build();
    }

    @Override
    public List<String> checkOneIdConflicts(List<String> oneIds, String excludeOrderId) {
        if (oneIds == null || oneIds.isEmpty()) {
            return new ArrayList<>();
        }
        log.info("检查oneId冲突: oneIds={}, excludeOrderId={}", oneIds, excludeOrderId);
        LambdaQueryWrapper<SysOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .in(SysOrderItem::getOneId, oneIds)
                .notIn(SysOrderItem::getStatus, KnetOrderItemStatus.CANCELLED, KnetOrderItemStatus.SYSTEM_CANCELLED, KnetOrderItemStatus.PAY_FAILED)
                .ne(SysOrderItem::getParentOrderId, excludeOrderId);
        List<SysOrderItem> conflictItems = orderItemService.list(queryWrapper);
        if (conflictItems.isEmpty()) {
            log.info("未发现oneId冲突: oneIds={}", oneIds);
            return new ArrayList<>();
        }
        // 提取冲突的oneId
        List<String> conflictOneIds = conflictItems.stream()
                .map(SysOrderItem::getOneId)
                .distinct()
                .toList();
        log.warn("发现oneId冲突: conflictOneIds={}, 冲突订单详情: {}",
                conflictOneIds,
                conflictItems.stream()
                        .collect(Collectors.groupingBy(SysOrderItem::getOneId,
                                Collectors.mapping(item -> item.getParentOrderId() + "(" + item.getStatus() + ")",
                                        Collectors.toList()))));
        return conflictOneIds;
    }
}
