package com.knet.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knet.common.base.HttpResult;
import com.knet.common.context.UserContext;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.service.PricingStrategyService;
import com.knet.common.utils.InvoiceNumberUtil;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.mapper.SysOrderGroupMapper;
import com.knet.order.model.dto.req.AdminOrderListQueryRequest;
import com.knet.order.model.dto.req.CreateOrderRequest;
import com.knet.order.model.dto.req.InventoryCheckRequest;
import com.knet.order.model.dto.req.OrderListQueryRequest;
import com.knet.order.model.dto.resp.*;
import com.knet.order.model.dto.third.req.UpdatedOrderRequest;
import com.knet.order.model.dto.third.resp.*;
import com.knet.order.model.entity.SysOrder;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.vo.OrderItemDataVo;
import com.knet.order.model.vo.SubOrderDataVo;
import com.knet.order.model.vo.SubOrderSummaryVo;
import com.knet.order.openfeign.ApiGoodsServiceProvider;
import com.knet.order.openfeign.ApiNotificationServiceProvider;
import com.knet.order.openfeign.ApiPaymentServiceProvider;
import com.knet.order.service.*;
import com.knet.order.system.event.ApiOrderCancelledEvent;
import com.knet.order.system.event.OrderCancelledEvent;
import com.knet.order.system.event.OrderCreatedEvent;
import com.knet.order.system.event.OrderFailedEvent;
import com.knet.order.system.handler.OrderStatusUpdateStrategy;
import com.knet.order.system.handler.impl.OrderStatusStrategyManager;
import com.knet.order.system.retry.RetryWrapper;
import com.knet.order.system.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/4 14:17
 * @description: 订单聚合服务接口实现
 */
@Slf4j
@Service
public class SysOrderProcessServiceImpl implements ISysOrderProcessService {
    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private ISysOrderService orderService;
    @Resource
    private ISysOrderItemService orderItemService;
    @Resource
    private ISysOrderGroupService orderGroupService;
    @Resource
    private SysOrderGroupMapper orderGroupMapper;
    @Resource
    private ApiPaymentServiceProvider apiPaymentServiceProvider;
    @Resource
    private ApiGoodsServiceProvider apiGoodsServiceProvider;
    @Resource
    private ApiNotificationServiceProvider apiNotificationServiceProvider;
    @Resource
    private ISysShippingItemRelService iSysShippingItemRelService;
    @Resource
    private ISysShippingLabelService iSysShippingLabelService;
    @Resource
    private OrderStatusStrategyManager strategyManager;
    @Resource(name = "orderThreadPoolExecutor")
    private Executor orderThreadPoolExecutor;
    @Resource
    private PricingStrategyService pricingStrategyService;
    @Resource
    private IInternalToolsService iInternalToolsService;
    @Resource
    private ISysOrderInvoiceSnapshotService orderInvoiceSnapshotService;
    @Resource
    private InvoiceNumberUtil invoiceNumberUtil;
    @Resource
    private RetryWrapper retryWrapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderResponse createOrderFromCart(CreateOrderRequest request) {
        log.info("开始创建子母订单, userId: {}, items: {}", request.getUserId(), request.getItems().size());
        SysOrderGroup orderGroup = null;
        List<SubOrderDataVo> subOrderDataList = null;
        String account = jwtUtil.getAccountFromToken(UserContext.getContext());
        // 1. 验证请求数据并按商品分组
        Map<String, List<OrderItemDataVo>> productGroupMap = validateAndGroupOrderItemsFromRequest(request);
        if (productGroupMap.isEmpty()) {
            throw new ServiceException("订单商品数据为空");
        }
        // 2. 库存校验 - 在创建订单前检查库存是否充足
        checkInventoryBeforeCreateOrder(productGroupMap, account);
        try {
            // 3. 创建母订单记录
            orderGroup = orderGroupService.createOrderGroup(request.getUserId(), productGroupMap, request.getAddressId(), request.getBillAddressId());
            // 4. 为每个商品创建子订单和明细
            subOrderDataList = createSubOrdersAndItems(orderGroup.getOrderId(), request.getUserId(), productGroupMap);
            OrderCreatedEvent event = new OrderCreatedEvent(this, orderGroup);
            eventPublisher.publishEvent(event);
        } catch (Exception e) {
            log.error("订单创建失败: {}", e.getMessage(), e);
            OrderFailedEvent event = new OrderFailedEvent(this, request);
            pushOrderCreateFailedEvent(event);
            throw new ServiceException("订单创建失败: " + e.getMessage());
        }
        clearUserOrderListCache(request.getUserId());
        CreateOrderResponse response = CreateOrderResponse.buildCreateOrderResponse(orderGroup, subOrderDataList);
        log.info("子母订单创建成功, parentOrderId: {}, subOrders: {}, totalAmount: {}",
                orderGroup.getOrderId(), subOrderDataList.size(), orderGroup.getTotalAmount());
        return response;
    }

    /**
     * 订单创建前的库存校验
     *
     * @param productGroupMap 已处理的商品分组数据（包含原始价格）
     * @param account         用户账号
     */
    private void checkInventoryBeforeCreateOrder(Map<String, List<OrderItemDataVo>> productGroupMap, String account) {
        log.info("开始库存校验，商品种类数: {}", productGroupMap.size());
        InventoryCheckRequest inventoryCheckRequest = InventoryCheckRequest.buildFromOrderItemData(productGroupMap, account);
        HttpResult<InventoryCheckResponse> result = apiGoodsServiceProvider.checkInventory(inventoryCheckRequest);
        if (result == null || !result.success()) {
            log.error("库存检查服务调用失败: {}", result != null ? result.getMsg() : "返回结果为空");
            throw new ServiceException("Inventory has Changed.", 400);
        }
        InventoryCheckResponse response = result.getData();
        if (response == null) {
            log.error("库存检查响应数据为空");
            throw new ServiceException("Inventory has Changed.", 400);
        }
        if (!response.getSuccess()) {
            String errorMessage = "库存不足：" + response.getInsufficientItemsDetail();
            log.error("库存校验失败 库存不足: {}", errorMessage);
            throw new ServiceException("Inventory has Changed.", 400);
        }
        log.info("库存校验通过");
    }

    /**
     * 清除用户订单列表缓存
     * 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
     */
    private void clearUserOrderListCache(Long userId) {
        try {
            // 构建缓存key模式，匹配该用户的所有订单列表缓存
            String cacheKeyPattern = "order-service:orderList:" + userId + ":*";
            // 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
            RedisCacheUtil.deleteByPattern(cacheKeyPattern);
            log.info("已清除用户订单列表缓存，用户ID: {}, 缓存key模式: {}", userId, cacheKeyPattern);
        } catch (Exception e) {
            log.warn("清除用户订单列表缓存失败，用户ID: {}, 错误: {}", userId, e.getMessage());
        }
    }

    /**
     * 发送订单创建失败事件
     *
     * @param orderFailedEvent 订单创建事件
     */
    @Async
    public void pushOrderCreateFailedEvent(OrderFailedEvent orderFailedEvent) {
        eventPublisher.publishEvent(orderFailedEvent);
    }

    /**
     * 验证请求数据并按商品分组（支持相同SKU相同尺码不同定价）
     */
    private Map<String, List<OrderItemDataVo>> validateAndGroupOrderItemsFromRequest(CreateOrderRequest request) {
        Map<String, List<OrderItemDataVo>> productGroupMap = new HashMap<>(12);
        for (CreateOrderRequest.OrderItemRequest requestItem : request.getItems()) {
            List<OrderItemDataVo> itemDataList = new ArrayList<>();
            // 处理每个尺码明细（支持相同尺码不同定价）
            for (CreateOrderRequest.SizeDetailRequest sizeDetailRequest : requestItem.getSizeDetails()) {
                // 数量验证
                if (sizeDetailRequest.getQuantity() <= 0) {
                    throw new ServiceException("商品 " + requestItem.getSku() + " 尺码 " + sizeDetailRequest.getSize() + " 数量必须大于0");
                }
                OrderItemDataVo orderItemData = createOrderItem(requestItem, sizeDetailRequest);
                itemDataList.add(orderItemData);
            }
            // 合并同一SKU的商品数据
            if (productGroupMap.containsKey(requestItem.getSku())) {
                productGroupMap.get(requestItem.getSku()).addAll(itemDataList);
            } else {
                productGroupMap.put(requestItem.getSku(), itemDataList);
            }
        }
        return productGroupMap;
    }

    /**
     * 创建订单商品数据
     *
     * @param requestItem       requestItem
     * @param sizeDetailRequest sizeDetailRequest
     * @return OrderItemData
     */
    private OrderItemDataVo createOrderItem(CreateOrderRequest.OrderItemRequest requestItem, CreateOrderRequest.SizeDetailRequest sizeDetailRequest) {
        BigDecimal strategyPrice = getBigDecimal(requestItem, sizeDetailRequest);
        // 将策略价格转换为原始价格
        Long strategyPriceCents = PriceFormatUtil.formatYuanToCents(strategyPrice.toString());
        Long originalPriceCents = pricingStrategyService.removePricingStrategy(strategyPriceCents);
        String originalPriceStr = PriceFormatUtil.formatCentsToYuan(originalPriceCents);
        BigDecimal originalPrice = new BigDecimal(originalPriceStr);
        log.debug("订单价格转换: SKU={}, Size={}, 策略价格={}美元, 原始价格={}美元",
                requestItem.getSku(), sizeDetailRequest.getSize(), strategyPrice, originalPrice);
        // 创建订单商品数据（每个尺码明细对应一条记录，支持相同尺码不同定价）
        return OrderItemDataVo.createOrderItemDataDto(requestItem, sizeDetailRequest, strategyPrice, originalPrice);
    }

    private static BigDecimal getBigDecimal(CreateOrderRequest.OrderItemRequest requestItem, CreateOrderRequest.SizeDetailRequest sizeDetailRequest) {
        BigDecimal strategyPrice;
        try {
            // 前端传入的是策略价格
            strategyPrice = new BigDecimal(sizeDetailRequest.getUnitPrice());
            if (strategyPrice.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("商品 " + requestItem.getSku() + " 尺码 " + sizeDetailRequest.getSize() + " 单价必须大于0");
            }
        } catch (NumberFormatException e) {
            throw new ServiceException("商品 " + requestItem.getSku() + " 尺码 " + sizeDetailRequest.getSize() + " 单价格式错误");
        }
        return strategyPrice;
    }

    /**
     * 为每个商品创建子订单和明细
     */
    private List<SubOrderDataVo> createSubOrdersAndItems(String parentOrderId, Long userId,
                                                         Map<String, List<OrderItemDataVo>> productGroupMap) {
        List<SubOrderDataVo> subOrderDataList = new ArrayList<>();
        for (Map.Entry<String, List<OrderItemDataVo>> entry : productGroupMap.entrySet()) {
            String sku = entry.getKey();
            List<OrderItemDataVo> itemDataList = entry.getValue();
            SysOrder subOrder = orderService.createSysOrder(parentOrderId, userId, sku, itemDataList);
            List<SysOrderItem> orderItems = orderItemService.createOrderItems(subOrder.getOrderId(), itemDataList, subOrder.getParentOrderId());
            SubOrderDataVo subOrderData = new SubOrderDataVo(subOrder, orderItems);
            subOrderDataList.add(subOrderData);
        }
        return subOrderDataList;
    }

    @Override
    public IPage<OrderListResponse.ParentOrderResponse> queryOrderList(OrderListQueryRequest request) {
        log.info("查询订单列表: {}", request);
        if (StrUtil.isNotBlank(request.getOrderId())) {
            request.setOrderId(request.getOrderId().trim());
        }
        Page<OrderListResponse.ParentOrderResponse> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<OrderListResponse.ParentOrderResponse> parentOrderPage = orderGroupMapper.queryOrderList(page, request);
        if (CollUtil.isEmpty(parentOrderPage.getRecords())) {
            return parentOrderPage;
        }
        List<String> parentOrderIds = parentOrderPage.getRecords().stream()
                .map(OrderListResponse.ParentOrderResponse::getParentOrderId)
                .toList();
        List<SubOrderSummaryVo> subOrderSummaryDos = orderGroupMapper.querySubOrderSummary(parentOrderIds);
        //  转换DTO并按父订单ID分组子订单汇总
        Map<String, List<OrderListResponse.SubOrderSummaryResponse>> subOrderMap = subOrderSummaryDos.stream()
                .map(SubOrderSummaryVo::convertToSubOrderSummaryResponse)
                .collect(Collectors.groupingBy(OrderListResponse.SubOrderSummaryResponse::getParentOrderId));
        //  为每个父订单设置子订单汇总信息并格式化价格
        parentOrderPage.getRecords().forEach(parentOrder -> {
            parentOrder.setTotalAmount(PriceFormatUtil.formatPrice(parentOrder.getTotalAmount()));
            parentOrder.setStatus(KnetOrderGroupStatus.displayStatus(parentOrder.getStatus()));
            // 设置子订单汇总信息
            List<OrderListResponse.SubOrderSummaryResponse> subOrders = subOrderMap.get(parentOrder.getParentOrderId());
            if (CollUtil.isNotEmpty(subOrders)) {
                parentOrder.setSubOrders(subOrders);
            } else {
                parentOrder.setSubOrders(new ArrayList<>());
            }
        });
        log.info("查询订单列表完成，共查询到 {} 条父订单", parentOrderPage.getRecords().size());
        return parentOrderPage;
    }

    @Override
    public OrderDetailResponse getOrderDetail(String orderId) {
        log.info("获取订单详情: orderId={}", orderId);
        SysOrderGroup orderGroup = orderGroupService.getOrderGroupByOrderId(orderId);
        if (BeanUtil.isEmpty(orderGroup)) {
            throw new ServiceException("订单不存在: " + orderId);
        }
        List<SysOrderItem> items = orderItemService.getOrderItemsByPrentOrderId(orderGroup.getOrderId());
        List<OrderDetailResponse.OrderItemDetailResponse> subOrderItems = items.stream()
                .map(item -> {
                    String strategyPriceStr = item.getKgOwningPrice().toString();
                    return OrderDetailResponse.OrderItemDetailResponse.createDto(item, strategyPriceStr);
                })
                .toList();
        List<OrderShipInfo> shippingLabelsByPrentOrderId = retryWrapper.executeWithFastRetry(
                () -> iSysShippingLabelService.getShippingLabelsByPrentOrderId(orderId),
                "根据父订单号查询物流运单");
        // 构建物流编号
        if (CollUtil.isNotEmpty(shippingLabelsByPrentOrderId)) {
            // 构建 itemId -> trackingNumber 的映射
            Map<Long, String> itemTrackingMap = shippingLabelsByPrentOrderId.stream()
                    .collect(Collectors.toMap(OrderShipInfo::getItemId,
                            OrderShipInfo::getTrackingNumber,
                            (existing, replacement) -> existing,
                            () -> new HashMap<>(shippingLabelsByPrentOrderId.size())
                    ));
            subOrderItems.forEach(item -> {
                String trackingNo = itemTrackingMap.get(item.getItemId());
                if (trackingNo != null) {
                    item.setTrackingNo(trackingNo);
                }
            });
        }
        CompletableFuture<UserAddressDtoResp> addressFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return orderGroupService.parseShippingAddressSnapshot(orderGroup.getShippingAddressSnapshot());
            } catch (Exception e) {
                log.warn("解析收货地址快照失败: orderId={}, error={}", orderId, e.getMessage());
                return null;
            }
        }, orderThreadPoolExecutor);
        CompletableFuture<OrderPaymentInfoResponse> paymentFuture = CompletableFuture.supplyAsync(() -> {
            try {
                HttpResult<OrderPaymentInfoResponse> result = retryWrapper.executeWithFastRetry(
                        () -> apiPaymentServiceProvider.getPaymentInfoByOrderId(orderId),
                        "根据订单ID获取支付信息");
                return result != null && result.success() ? result.getData() : null;
            } catch (Exception e) {
                log.warn("获取支付信息失败: orderId={}, error={}", orderId, e.getMessage());
                return null;
            }
        }, orderThreadPoolExecutor);
        CompletableFuture<UserInvoiceAddressDtoResp> billAddressFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return orderGroupService.parseBillingAddressSnapshot(orderGroup.getBillingAddressSnapshot());
            } catch (Exception e) {
                log.warn("解析账单地址快照失败: orderId={}, error={}", orderId, e.getMessage());
                return null;
            }
        }, orderThreadPoolExecutor);
        CompletableFuture<OrderInvoiceSnapshotDtoResp> invoiceFuture = CompletableFuture.supplyAsync(() ->
                        orderInvoiceSnapshotService.getInvoiceSnapshotByOrderId(orderId), orderThreadPoolExecutor)
                .exceptionally(e -> {
                    log.warn("获取发票信息失败: orderId={}, error={}", orderId, e.getMessage());
                    return null;
                });
        CompletableFuture<OrderDetailResponse> resultFuture = addressFuture.thenCombine(
                        paymentFuture, (address, payment) -> OrderDetailResponse.builder()
                                .parentOrderId(orderGroup.getOrderId())
                                .userId(orderGroup.getUserId())
                                .status(KnetOrderGroupStatus.displayStatus(orderGroup.getStatus()))
                                .totalAmount(PriceFormatUtil.formatPrice(orderGroup.getTotalAmount()))
                                .totalQuantity(items.size())
                                .createTime(orderGroup.getCreateTime())
                                .shippingAddress(address)
                                .paymentInfo(payment)
                                .items(subOrderItems)
                                .build())
                .thenCombine(invoiceFuture, (response, invoice) -> {
                    if (BeanUtil.isNotEmpty(invoice)) {
                        response.setInvoicePdfUrl(invoice.getInvoicePdfUrl());
                    }
                    return response;
                }).thenCombine(billAddressFuture, (response, billAddress) -> {
                    response.setBillAddress(billAddress);
                    return response;
                });
        try {
            OrderDetailResponse response = resultFuture.get(5, TimeUnit.SECONDS);
            log.info("获取订单详情成功: orderId={}, subOrders={}", orderId, subOrderItems.size());
            return response;
        } catch (TimeoutException e) {
            log.error("订单详情组装超时: orderId={}, timeout={}秒", orderId, 5);
            throw new ServiceException("订单详情组装超时", e);
        } catch (ExecutionException e) {
            Throwable rootCause = e.getCause();
            log.error("订单详情组装失败: orderId={}, cause={}", orderId, rootCause.getMessage(), rootCause);
            throw new ServiceException("订单详情获取异常: " + rootCause.getMessage(), rootCause);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("订单详情组装被中断: orderId={}", orderId);
            throw new ServiceException("订单详情获取被中断", e);
        }
    }

    @Override
    public void cancelOrder(String prentOrderId) {
        if (orderGroupService.checkOrderIsCancelled(prentOrderId)) {
            return;
        }
        boolean groupUpdated = orderGroupService.updateOrderStatus(prentOrderId, KnetOrderGroupStatus.CANCELLED);
        if (!groupUpdated) {
            throw new ServiceException("更新父订单状态失败: " + prentOrderId);
        }
        boolean orderUpdated = orderService.updateOrderStatusByParentId(prentOrderId, KnetOrderGroupStatus.CANCELLED);
        if (!orderUpdated) {
            throw new ServiceException("更新子订单状态失败: " + prentOrderId);
        }
        boolean orderItemUpdated = orderItemService.updateOrderStatusByParentId(prentOrderId, KnetOrderItemStatus.CANCELLED);
        if (!orderItemUpdated) {
            throw new ServiceException("更新 item 订单状态失败: " + prentOrderId);
        }
        // 订单取消时，同步调用notification服务删除对应的发票
        orderInvoiceSnapshotService.deleteInvoiceSnapshotByOrderId(prentOrderId);
    }

    @Override
    public void systemCancelOrder(String prentOrderId) {
        log.info("系统取消订单: orderId={}", prentOrderId);
        if (orderGroupService.checkOrderIsCancelled(prentOrderId)) {
            return;
        }
        boolean groupUpdated = orderGroupService.updateOrderStatus(prentOrderId, KnetOrderGroupStatus.SYSTEM_CANCELLED);
        if (!groupUpdated) {
            throw new ServiceException("更新父订单状态为系统取消失败: " + prentOrderId);
        }
        boolean orderUpdated = orderService.updateOrderStatusByParentId(prentOrderId, KnetOrderGroupStatus.SYSTEM_CANCELLED);
        if (!orderUpdated) {
            throw new ServiceException("更新子订单状态为系统取消失败: " + prentOrderId);
        }
        boolean orderItemUpdated = orderItemService.updateOrderStatusByParentId(prentOrderId, KnetOrderItemStatus.SYSTEM_CANCELLED);
        if (!orderItemUpdated) {
            throw new ServiceException("更新 item 订单状态为系统取消失败: " + prentOrderId);
        }
        // 系统取消订单时，同步调用notification服务删除对应的发票
        orderInvoiceSnapshotService.deleteInvoiceSnapshotByOrderId(prentOrderId);
        log.info("系统取消订单完成: orderId={}", prentOrderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean smartUpdateOrderStatus(String parentOrderId) {
        SysOrderGroup orderGroup = orderGroupService.getOrderGroupByOrderId(parentOrderId);
        if (orderGroup == null) {
            log.error("订单组不存在，无法更新状态: parentOrderId={}", parentOrderId);
            return false;
        }
        log.info("开始智能更新订单状态, 订单组ID: {}", orderGroup.getId());
        try {
            // 获取订单下所有订单项
            List<SysOrderItem> allItems = orderItemService.getOrderItemsByPrentOrderId(parentOrderId);
            if (allItems.isEmpty()) {
                log.warn("订单下没有订单项，无法判断物流标签分配状态: parentOrderId={}", parentOrderId);
                return false;
            }
            // 检查已分配物流标签的订单项ID列表
            List<Long> shippedItemIds = iSysShippingItemRelService.getShippedItemIdsByParentOrderId(parentOrderId);
            if (shippedItemIds.isEmpty()) {
                log.info("订单下没有订单项分配物流标签，不更新状态: parentOrderId={}", parentOrderId);
                return false;
            }
            // 判断是全部分配还是部分分配
            boolean allShipped = allItems.size() == shippedItemIds.size();
            KnetOrderGroupStatus newStatus = allShipped ? KnetOrderGroupStatus.PENDING_SHIPMENT : KnetOrderGroupStatus.MULTIPLE_STATES;
            log.info("订单状态智能判断结果: parentOrderId={}, 总订单项={}, 已分配物流标签={}, 更新状态={}",
                    parentOrderId, allItems.size(), shippedItemIds.size(), newStatus.getDesc());
            // 1. 更新订单组状态
            boolean updateGroupResult = orderGroupService.updateOrderStatus(orderGroup.getOrderId(), newStatus);
            if (!updateGroupResult) {
                log.warn("订单组 {} 状态更新失败，可能状态已变更", orderGroup.getId());
                return false;
            }
            log.info("订单组 {} 状态已更新为 {}", orderGroup.getId(), newStatus.getDesc());
            // 2. 批量更新关联的子订单(SysOrder)状态
            boolean updateOrderResult = orderService.updateOrderStatusByParentId(parentOrderId, newStatus);
            if (updateOrderResult) {
                log.info("订单组 {} 下的子订单状态已批量更新为 {}", orderGroup.getId(), newStatus.getDesc());
                // 3. 更新订单项状态
                boolean updateItemResult;
                if (allShipped) {
                    // 如果所有订单项都已分配物流标签，则将所有订单项状态更新为待发货
                    updateItemResult = orderItemService.updateOrderStatusByParentId(parentOrderId, KnetOrderItemStatus.PENDING_SHIPMENT);
                    log.info("订单组 {} 下的所有订单项状态已批量更新为待发货", orderGroup.getId());
                } else {
                    // 如果只有部分订单项分配了物流标签，则只更新那些已分配物流标签的订单项状态为待发货
                    updateItemResult = orderItemService.updateOrderStatusByItemIds(shippedItemIds, KnetOrderItemStatus.PENDING_SHIPMENT);
                    log.info("订单组 {} 下的已分配物流标签的订单项({}个)状态已更新为待发货", orderGroup.getId(), shippedItemIds.size());
                }
                if (!updateItemResult) {
                    log.warn("订单组 {} 下的订单项状态更新失败", orderGroup.getId());
                }
            }
            // 清除用户订单缓存
            clearUserOrderListCache(orderGroup.getUserId());
            return true;
        } catch (Exception e) {
            log.error("智能更新订单状态失败: {}", e.getMessage(), e);
            throw new ServiceException("更新订单状态失败: " + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void cancelItemOrder(String itemNo) {
        log.info("取消订单项: itemNo={}", itemNo);
        long userId = Long.parseLong(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        //1.订单状态判断是否满足取消
        LambdaQueryWrapper<SysOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOrderItem::getItemNo, itemNo);
        SysOrderItem orderItem = orderItemService.getOne(queryWrapper);
        if (orderItem == null) {
            throw new ServiceException("订单项不存在: " + itemNo);
        }
        if (!KnetOrderItemStatus.isCancelable(orderItem.getStatus())) {
            throw new ServiceException("订单项状态不满足取消: " + orderItem.getStatus().getDesc());
        }
        SysOrderGroup orderGroup = orderGroupService.getOrderGroupByOrderId(orderItem.getParentOrderId());
        if (orderGroup == null) {
            throw new ServiceException("订单取消，父订单不存在: " + orderItem.getParentOrderId());
        }
        Long userIdFromToken = Long.valueOf(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        if (!userIdFromToken.equals(orderGroup.getUserId())) {
            throw new ServiceException("Can't close other people's orders");
        }
        //2.更新订单项状态
        orderItemService.updateOrderStatusByItemIds(Collections.singletonList(orderItem.getItemId()), KnetOrderItemStatus.CANCELLED);
        //3.检查所有关联的item订单是否都处于已取消状态
        boolean allItemsCancelled = orderService.checkAllItemsStatus(orderItem.getParentOrderId(), KnetOrderItemStatus.CANCELLED);
        KnetOrderGroupStatus targetStatus;
        if (allItemsCancelled) {
            // 如果所有item都已取消，则设置为完全取消状态
            targetStatus = KnetOrderGroupStatus.CANCELLED;
        } else {
            // 否则设置为部分取消状态
            targetStatus = KnetOrderGroupStatus.MULTIPLE_STATES;
        }
        orderService.updateOrderStatusByParentId(orderItem.getParentOrderId(), targetStatus);
        orderGroupService.updateOrderStatus(orderItem.getParentOrderId(), targetStatus);
        // 订单取消，删除订单对应的发票，准备重新开票
        orderInvoiceSnapshotService.deleteInvoiceSnapshotByOrderId(orderItem.getParentOrderId());
        //5.发送订单取消需要退款消息
        OrderCancelledEvent event = new OrderCancelledEvent(this, orderItem.getItemId(), orderItem.getParentOrderId(), orderItem.getKgOwningPrice(), userId, orderItem.getItemNo());
        eventPublisher.publishEvent(event);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean smartUpdateOrderStatus(UpdatedOrderRequest request) {
        LambdaQueryWrapper<SysOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOrderItem::getItemNo, request.getOrderNo());
        List<SysOrderItem> sysOrderItems = orderItemService.list(queryWrapper);
        if (1 != sysOrderItems.size()) {
            log.warn("订单状态不满足更新: item订单号 {}", request.getOrderNo());
            return false;
        }
        SysOrderItem sysOrderItem = sysOrderItems.get(0);
        try {
            // 检查订单项当前状态，如果要更新为IN_TRANSIT状态，需要验证订单不能处于已取消状态
            if (KnetOrderItemStatus.IN_TRANSIT.equals(request.getStatus())) {
                if (KnetOrderItemStatus.CANCELLED.equals(sysOrderItem.getStatus()) ||
                        KnetOrderItemStatus.SYSTEM_CANCELLED.equals(sysOrderItem.getStatus())) {
                    log.warn("订单项已取消，不能更新为IN_TRANSIT状态: itemNo={}, currentStatus={}",
                            request.getOrderNo(), sysOrderItem.getStatus());
                    return false;
                }
                // 如果订单项已经是IN_TRANSIT状态，避免重复更新
                if (KnetOrderItemStatus.IN_TRANSIT.equals(sysOrderItem.getStatus())) {
                    log.info("订单项已经是IN_TRANSIT状态，跳过更新: itemNo={}", request.getOrderNo());
                    return true;
                }
            }
            //1.更新订单项状态
            LambdaUpdateWrapper<SysOrderItem> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(SysOrderItem::getItemNo, request.getOrderNo())
                    .set(BeanUtil.isNotEmpty(request.getStatus()), SysOrderItem::getStatus, request.getStatus())
                    .set(KnetOrderItemStatus.IN_TRANSIT.equals(request.getStatus()), SysOrderItem::getInTransitTime, new Date())
                    .set(KnetOrderItemStatus.CANCELLED.equals(request.getStatus()), SysOrderItem::getCancelledTime, new Date());
            boolean updateItemResult = orderItemService.update(null, updateWrapper);
            if (!updateItemResult) {
                log.warn("订单项状态更新失败: {}", request.getOrderNo());
                return false;
            }
            if (KnetOrderItemStatus.CANCELLED.equals(request.getStatus())) {
                LambdaQueryWrapper<SysOrderGroup> orderGroupLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orderGroupLambdaQueryWrapper.eq(SysOrderGroup::getOrderId, sysOrderItem.getParentOrderId());
                SysOrderGroup sysOrderGroup = orderGroupService.getOne(orderGroupLambdaQueryWrapper);
                long userId = sysOrderGroup.getUserId();
                // API取消订单发送ApiOrderCancelledEvent，只退款不释放库存
                ApiOrderCancelledEvent event = new ApiOrderCancelledEvent(this
                        , sysOrderItem.getItemId()
                        , sysOrderItem.getParentOrderId()
                        , sysOrderItem.getKgOwningPrice()
                        , userId
                        , sysOrderItem.getItemNo());
                eventPublisher.publishEvent(event);
            }
            // 2.使用策略模式更新父子订单状态
            OrderStatusUpdateStrategy strategy = strategyManager.getStrategy(request.getStatus());
            return strategy.handleStatusUpdate(sysOrderItem.getParentOrderId(), request.getOrderNo());
        } catch (Exception e) {
            log.error("智能更新订单状态失败: {}", e.getMessage(), e);
            throw new ServiceException("更新订单状态失败: " + e.getMessage());
        }
    }

    @Override
    public PaymentDetailInfoResponse queryPaymentInfo(String parentOrderId) {
        OrderDetailResponse orderDetail = getOrderDetail(parentOrderId);
        if (BeanUtil.isNotEmpty(orderDetail)) {
            String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
            InnerUserBalanceResponse userBalance;
            PaymentDetailInfoResponse response = PaymentDetailInfoResponse.create(orderDetail);
            try {
                userBalance = apiPaymentServiceProvider.getUserBalance(Long.valueOf(userId)).getData();
                if (BeanUtil.isNotEmpty(userBalance)) {
                    response.setBalance(userBalance.getBalance());
                }
            } catch (Exception e) {
                log.warn("获取用户余额信息失败: orderId={}, error={}", userId, e.getMessage());
            }
            return response;
        }
        return null;
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public boolean freezeOrderItem(List<SysOrderItem> orderItems) {
        try {
            List<Long> itemIds = orderItems.stream().map(SysOrderItem::getItemId).toList();
            String parentOrderId = orderItems.get(0).getParentOrderId();
            orderItemService.updateOrderStatusByItemIds(itemIds, KnetOrderItemStatus.FROZEN);
            orderService.updateOrderStatusByParentId(parentOrderId, KnetOrderGroupStatus.FROZEN);
            orderGroupService.updateOrderStatus(parentOrderId, KnetOrderGroupStatus.FROZEN);
            return true;
        } catch (Exception e) {
            log.error("更新订单状态为冻结失败: {}", e.getMessage(), e);
            throw new ServiceException("更新订单状态失败: " + e.getMessage());
        }
    }

    @Override
    public IPage<AdminOrderListResponse.AdminParentOrderResponse> queryAllOrderList(AdminOrderListQueryRequest request) {
        log.info("管理员查询订单列表: {}", request);
        if (StrUtil.isNotBlank(request.getOrderId())) {
            request.setOrderId(request.getOrderId().trim());
        }
        Page<OrderListResponse.ParentOrderResponse> page = new Page<>(request.getPageNo(), request.getPageSize());
        // 支持account 模糊搜索
        if (StrUtil.isNotBlank(request.getAccount())) {
            HttpResult<List<Long>> userIdsResult = iInternalToolsService.getUserIdsByAccount(request.getAccount());
            if (userIdsResult != null && userIdsResult.success() && userIdsResult.getData() != null && !userIdsResult.getData().isEmpty()) {
                List<Long> userIdsByAccount = userIdsResult.getData();
                request.setUserIds(userIdsByAccount);
            }
        }
        // 1. 查询订单基础信息（不包含用户账号）
        IPage<OrderListResponse.ParentOrderResponse> parentOrderPage = orderGroupMapper.queryOrderListForAdmin(page, request);
        if (CollUtil.isEmpty(parentOrderPage.getRecords())) {
            IPage<AdminOrderListResponse.AdminParentOrderResponse> emptyResult = new Page<>(request.getPageNo(), request.getPageSize());
            emptyResult.setTotal(0);
            return emptyResult;
        }
        // 2. 获取所有父订单ID和用户ID
        List<String> parentOrderIds = parentOrderPage.getRecords().stream()
                .map(OrderListResponse.ParentOrderResponse::getParentOrderId)
                .toList();
        Set<Long> userIds = parentOrderPage.getRecords().stream()
                .map(OrderListResponse.ParentOrderResponse::getUserId)
                .collect(Collectors.toSet());
        // 3. 并行执行子订单汇总查询和用户信息查询
        CompletableFuture<List<SubOrderSummaryVo>> subOrderSummaryFuture = CompletableFuture.supplyAsync(
                () -> orderGroupMapper.querySubOrderSummary(parentOrderIds),
                orderThreadPoolExecutor
        );
        CompletableFuture<Map<Long, String>> userAccountMapFuture = CompletableFuture.supplyAsync(
                () -> iInternalToolsService.getUserAccountMap(new ArrayList<>(userIds)),
                orderThreadPoolExecutor
        );
        try {
            // 4. 等待并获取结果
            List<SubOrderSummaryVo> subOrderSummaryDos = subOrderSummaryFuture.get(5, TimeUnit.SECONDS);
            Map<Long, String> userAccountMap = userAccountMapFuture.get(5, TimeUnit.SECONDS);
            // 5. 转换DTO并按父订单ID分组子订单汇总
            Map<String, List<OrderListResponse.SubOrderSummaryResponse>> subOrderMap = subOrderSummaryDos.stream()
                    .map(SubOrderSummaryVo::convertToSubOrderSummaryResponse)
                    .collect(Collectors.groupingBy(OrderListResponse.SubOrderSummaryResponse::getParentOrderId));
            // 6. 构建管理员订单响应,构建分页结果
            List<AdminOrderListResponse.AdminParentOrderResponse> adminOrders = parentOrderPage.getRecords().stream()
                    .map(parentOrder -> convertToAdminParentOrderResponse(parentOrder, userAccountMap, subOrderMap))
                    .toList();
            IPage<AdminOrderListResponse.AdminParentOrderResponse> result = new Page<>(request.getPageNo(), request.getPageSize());
            result.setRecords(adminOrders);
            result.setTotal(parentOrderPage.getTotal());
            result.setCurrent(parentOrderPage.getCurrent());
            result.setSize(parentOrderPage.getSize());
            log.info("管理员查询订单列表完成，共查询到 {} 条父订单", adminOrders.size());
            return result;
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            log.error("管理员查询订单列表异常: {}", e.getMessage(), e);
            throw new ServiceException("查询订单列表失败: " + e.getMessage());
        }
    }


    /**
     * 转换为管理员父订单响应
     *
     * @param parentOrder    父订单信息
     * @param userAccountMap 用户账号映射
     * @param subOrderMap    子订单映射
     * @return 管理员父订单响应
     */
    private AdminOrderListResponse.AdminParentOrderResponse convertToAdminParentOrderResponse(
            OrderListResponse.ParentOrderResponse parentOrder,
            Map<Long, String> userAccountMap,
            Map<String, List<OrderListResponse.SubOrderSummaryResponse>> subOrderMap) {
        return AdminOrderListResponse.AdminParentOrderResponse.builder()
                .parentOrderId(parentOrder.getParentOrderId())
                .userId(parentOrder.getUserId())
                .userAccount(userAccountMap.getOrDefault(parentOrder.getUserId(), "N/A"))
                .status(KnetOrderGroupStatus.displayStatus(parentOrder.getStatus()))
                .totalQuantity(parentOrder.getTotalQuantity())
                .totalAmount(PriceFormatUtil.formatPrice(parentOrder.getTotalAmount()))
                .createTime(parentOrder.getCreateTime())
                .subOrders(subOrderMap.getOrDefault(parentOrder.getParentOrderId(), new ArrayList<>()))
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeOrder(String parentOrderId) {
        log.info("用户主动关闭订单: parentOrderId={}", parentOrderId);
        SysOrderGroup orderGroup = orderGroupService.getOrderGroupByOrderId(parentOrderId);
        if (orderGroup == null) {
            log.error("订单不存在，无法关闭: parentOrderId={}", parentOrderId);
            throw new ServiceException("The order does not exist: " + parentOrderId);
        }
        Long userIdFromToken = Long.valueOf(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        if (!userIdFromToken.equals(orderGroup.getUserId())) {
            throw new ServiceException("Can't close other people's orders");
        }
        if (!KnetOrderGroupStatus.isClosable(orderGroup.getStatus())) {
            log.error("订单状态不允许关闭: parentOrderId={}, currentStatus={}", parentOrderId, orderGroup.getStatus());
            throw new ServiceException("The order status is not allowed to be closed");
        }
        if (orderGroupService.checkOrderIsCancelled(parentOrderId)) {
            log.info("订单已经处于取消状态，无需重复关闭: parentOrderId={}", parentOrderId);
            return;
        }
        try {
            boolean groupUpdated = orderGroupService.updateOrderStatus(parentOrderId, KnetOrderGroupStatus.SYSTEM_CANCELLED);
            if (!groupUpdated) {
                throw new ServiceException("更新父订单状态为系统取消失败: " + parentOrderId);
            }
            boolean orderUpdated = orderService.updateOrderStatusByParentId(parentOrderId, KnetOrderGroupStatus.SYSTEM_CANCELLED);
            if (!orderUpdated) {
                throw new ServiceException("更新子订单状态为系统取消失败: " + parentOrderId);
            }
            boolean orderItemUpdated = orderItemService.updateOrderStatusByParentId(parentOrderId, KnetOrderItemStatus.SYSTEM_CANCELLED);
            if (!orderItemUpdated) {
                throw new ServiceException("更新订单项状态为系统取消失败: " + parentOrderId);
            }
            // 用户主动关闭订单时，删除对应的发票快照
            orderInvoiceSnapshotService.deleteInvoiceSnapshotByOrderId(parentOrderId);
            // 发送订单关闭事件，释放库存
            OrderCancelledEvent event = new OrderCancelledEvent(this, null, parentOrderId, orderGroup.getTotalAmount(), orderGroup.getUserId(), null);
            eventPublisher.publishEvent(event);
            log.info("用户主动关闭订单完成: parentOrderId={}", parentOrderId);
        } catch (Exception e) {
            log.error("用户主动关闭订单失败: parentOrderId={}, error={}", parentOrderId, e.getMessage(), e);
            throw new ServiceException("The order has failed to close, please contact the account manager ");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String regenerateInvoice(String parentOrderId) {
        log.info("重新生成订单发票: parentOrderId={}", parentOrderId);
        SysOrderGroup orderGroup = orderGroupService.getOrderGroupByOrderId(parentOrderId);
        if (orderGroup == null) {
            log.error("订单不存在: parentOrderId={}", parentOrderId);
            throw new ServiceException("The order does not exist: " + parentOrderId);
        }
        long userIdFromToken = Long.parseLong(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        if (userIdFromToken != orderGroup.getUserId()) {
            log.error("不能操作其他人的订单-重新生成订单发票: parentOrderId={}, userIdFromToken={}, orderGroupUserId={}", parentOrderId, userIdFromToken, orderGroup.getUserId());
            throw new ServiceException("Cannot generate an order invoice for others");
        }
        // 检查订单状态 - 订单取消、系统取消的不能调用
        if (KnetOrderGroupStatus.isRetryInvoice(orderGroup.getStatus())) {
            log.error("订单已取消，无法重新生成发票: parentOrderId={}, status={}", parentOrderId, orderGroup.getStatus());
            throw new ServiceException("Order cancelled, invoice cannot be regenerated");
        }
        // 重新生成发票快照
        try {
            orderInvoiceSnapshotService.deleteInvoiceSnapshotByOrderId(parentOrderId);
            String invoiceNumber = invoiceNumberUtil.generateInvoiceNumber();
            // 获取订单项（过滤掉已取消的订单项）
            List<SysOrderItem> orderItems = orderItemService.getOrderItemsByPrentOrderId(parentOrderId).stream()
                    .filter(vo -> !vo.getStatus().invoiceFilter())
                    .toList();
            if (BeanUtil.isEmpty(orderItems)) {
                log.error("订单商品明细不存在，无法重新生成发票: parentOrderId={}", parentOrderId);
                throw new ServiceException("Data abnormality, please contact the account manager: " + parentOrderId);
            }
            orderInvoiceSnapshotService.createInvoiceSnapshot(parentOrderId, orderGroup.getUserId(), invoiceNumber, orderGroup, orderItems);
            log.info("发票快照重新创建成功: parentOrderId={}, invoiceNumber={}", parentOrderId, invoiceNumber);
            // 调用通知服务生成PDF并上传到S3
            HttpResult<String> pdfResult = retryWrapper.executeWithRetry(
                    () -> apiNotificationServiceProvider.generateInvoicePdf(parentOrderId),
                    "通知服务生成PDF并上传到S3");
            if (pdfResult == null || !pdfResult.success() || StrUtil.isBlank(pdfResult.getData())) {
                log.error("调用通知服务生成PDF失败: parentOrderId={}, result={}", parentOrderId, pdfResult);
                throw new ServiceException("The PDF generation failed, please try again later");
            }
            String pdfUrl = pdfResult.getData();
            orderInvoiceSnapshotService.updateInvoicePdfUrl(invoiceNumber, pdfUrl);
            log.info("发票PDF重新生成成功: parentOrderId={}, invoiceNumber={}, pdfUrl={}", parentOrderId, invoiceNumber, pdfUrl);
            return pdfUrl;
        } catch (Exception e) {
            log.error("重新生成订单发票失败: parentOrderId={}, error={}", parentOrderId, e.getMessage(), e);
            throw new ServiceException("Regenerating the invoice failed, please contact the account manager: " + e.getMessage());
        }
    }
}
