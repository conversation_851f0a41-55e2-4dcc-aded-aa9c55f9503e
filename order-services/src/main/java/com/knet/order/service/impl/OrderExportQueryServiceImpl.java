package com.knet.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knet.common.base.HttpResult;
import com.knet.order.mapper.SysOrderGroupMapper;
import com.knet.order.model.dto.req.AdminOrderListQueryRequest;
import com.knet.order.model.dto.resp.ExportAdminSaleOrderResponse;
import com.knet.order.service.IInternalToolsService;
import com.knet.order.service.IOrderExportQueryService;
import com.knet.order.system.retry.RetryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/9/26 11:16
 * @description:
 */
@Slf4j
@Service
public class OrderExportQueryServiceImpl implements IOrderExportQueryService {
    @Resource
    private SysOrderGroupMapper orderGroupMapper;
    @Resource
    private IInternalToolsService iInternalToolsService;
    @Resource
    private RetryWrapper retryWrapper;

    @Override
    public IPage<ExportAdminSaleOrderResponse> queryAllOrderList(AdminOrderListQueryRequest request) {
        log.info("管理员导出订单列表详情查询: {}", request);
        if (StrUtil.isNotBlank(request.getOrderId())) {
            request.setOrderId(request.getOrderId().trim());
        }
        Page<ExportAdminSaleOrderResponse> page = new Page<>(request.getPageNo(), request.getPageSize());
        // 支持account 模糊搜索
        if (StrUtil.isNotBlank(request.getAccount())) {
            HttpResult<List<Long>> userIdsResult = retryWrapper.executeWithFastRetry(
                    () -> iInternalToolsService.getUserIdsByAccount(request.getAccount()),
                    "根据账户名获取用户ID列表");
            if (userIdsResult != null && userIdsResult.success() && userIdsResult.getData() != null && !userIdsResult.getData().isEmpty()) {
                List<Long> userIdsByAccount = userIdsResult.getData();
                request.setUserIds(userIdsByAccount);
            }
        }
        // 1. 查询订单基础信息（不包含用户账号）
        IPage<ExportAdminSaleOrderResponse> parentOrderPage = orderGroupMapper.queryOrderListForAdminExport(page, request);
        if (CollUtil.isEmpty(parentOrderPage.getRecords())) {
            IPage<ExportAdminSaleOrderResponse> emptyResult = new Page<>(request.getPageNo(), request.getPageSize());
            emptyResult.setTotal(0);
            return emptyResult;
        }
        Set<Long> userIds = parentOrderPage.getRecords().stream()
                .map(ExportAdminSaleOrderResponse::getUserId)
                .collect(Collectors.toSet());
        Map<Long, String> userAccountMap = retryWrapper.executeWithFastRetry(
                () -> iInternalToolsService.getUserAccountMap(new ArrayList<>(userIds)),
                "批量获取用户账号信息");
        List<ExportAdminSaleOrderResponse> adminOrders = parentOrderPage.getRecords().stream()
                .peek(parentOrder ->
                        parentOrder.setUserAccount(userAccountMap.getOrDefault(parentOrder.getUserId(), "N/A")))
                .toList();
        IPage<ExportAdminSaleOrderResponse> result = new Page<>(request.getPageNo(), request.getPageSize());
        result.setRecords(adminOrders);
        result.setTotal(parentOrderPage.getTotal());
        result.setCurrent(parentOrderPage.getCurrent());
        result.setSize(parentOrderPage.getSize());
        log.info("管理员查询订单列表完成，共查询到 {} 条父订单", adminOrders.size());
        return result;
    }
}
