package com.knet.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.knet.common.base.HttpResult;
import com.knet.order.model.dto.third.resp.UserInfoDtoResp;
import com.knet.order.openfeign.ApiUserServiceProvider;
import com.knet.order.service.IInternalToolsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/9/4 10:52
 * @description:
 */
@Slf4j
@Service
public class IInternalToolsServiceImpl implements IInternalToolsService {
    @Resource
    private ApiUserServiceProvider apiUserServiceProvider;

    /**
     * 批量获取用户账号信息
     *
     * @param userIds 用户ID列表
     * @return 用户ID到账号的映射
     */
    @Override
    public Map<Long, String> getUserAccountMap(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        try {
            log.info("批量获取用户账号信息，用户数量: {}", userIds.size());
            HttpResult<List<UserInfoDtoResp>> result = apiUserServiceProvider.getUsersByIds(userIds);
            if (result == null || !result.success() || CollUtil.isEmpty(result.getData())) {
                log.warn("获取用户账号信息失败或为空, userIds: {}", userIds);
                return Collections.emptyMap();
            }
            Map<Long, String> userAccountMap = result.getData().stream()
                    .collect(Collectors.toMap(
                            UserInfoDtoResp::getId,
                            UserInfoDtoResp::getAccount,
                            (existing, replacement) -> existing
                    ));
            log.info("成功获取用户账号信息，映射数量: {}", userAccountMap.size());
            return userAccountMap;
        } catch (Exception e) {
            log.error("获取用户账号信息异常: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    @Override
    public HttpResult<List<Long>> getUserIdsByAccount(String account) {
        try {
            log.info("根据账户名模糊搜索获取用户ID列表: account={}", account);
            HttpResult<List<Long>> result = apiUserServiceProvider.getUserIdsByAccount(account);
            if (result == null || !result.success()) {
                log.warn("根据账户名获取用户ID列表失败: account={}", account);
                return HttpResult.error("获取用户ID列表失败");
            }
            log.info("成功根据账户名获取用户ID列表: account={}, 用户数量={}", account,
                    result.getData() != null ? result.getData().size() : 0);
            return result;
        } catch (Exception e) {
            log.error("根据账户名获取用户ID列表异常: account={}, error={}", account, e.getMessage(), e);
            return HttpResult.error("获取用户ID列表异常: " + e.getMessage());
        }
    }
}
