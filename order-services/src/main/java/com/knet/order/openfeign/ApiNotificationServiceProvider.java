package com.knet.order.openfeign;

import com.knet.common.base.HttpResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 通知服务提供者
 */
@FeignClient(value = "notification-services", path = "/notificationServices/api")
public interface ApiNotificationServiceProvider {

    /**
     * 生成发票PDF
     *
     * @param prentOrderId 订单ID
     * @return PDF文件S3地址
     */
    @PostMapping("/invoice/generate-pdf/{prentOrderId}")
    HttpResult<String> generateInvoicePdf(@PathVariable("prentOrderId") String prentOrderId);
}
