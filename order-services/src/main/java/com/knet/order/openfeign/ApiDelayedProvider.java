package com.knet.order.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.common.dto.message.DelayedMessage;
import com.knet.order.system.handler.impl.ApiDelayedServiceFallbackImpl;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2025/6/26 10:37
 * @description: 延迟消息服务
 */
@FeignClient(name = "delayed-services", path = "delayedServices/api", fallback = ApiDelayedServiceFallbackImpl.class)
public interface ApiDelayedProvider {

    /**
     * 添加延迟消息
     *
     * @param delayedMessage 延迟消息
     * @return 添加结果
     */
    @Schema(description = "添加延迟消息")
    @PostMapping("/add")
    HttpResult<String> addDelayedMessage(@RequestBody DelayedMessage delayedMessage);

    /**
     * 取消延迟消息
     *
     * @param prentOrderId 订单ID
     * @return 取消结果
     */
    @Schema(description = "取消延迟消息")
    @PostMapping("/cancel/{prentOrderId}")
    HttpResult<String> cancelDelayedMessage(@PathVariable("prentOrderId") String prentOrderId);
}
