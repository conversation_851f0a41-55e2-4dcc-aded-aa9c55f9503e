package com.knet.order.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.order.model.dto.third.req.ExportRequestDto;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 导出服务接口
 *
 * <AUTHOR>
 * @since 2025-09-25
 */
@FeignClient(name = "exports-services", path = "/exportServices/api")
public interface ApiExportServiceProvider {
    /**
     * 创建导出任务
     *
     * @param exportRequest 导出请求参数
     * @return 任务ID
     */
    @Operation(summary = "创建导出任务", description = "创建异步导出任务")
    @PostMapping("/tasks/create")
    HttpResult<String> createTask(@Valid @RequestBody ExportRequestDto exportRequest);
}
