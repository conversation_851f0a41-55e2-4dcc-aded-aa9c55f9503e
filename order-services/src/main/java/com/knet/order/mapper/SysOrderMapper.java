package com.knet.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knet.order.model.entity.SysOrder;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2025/6/3 17:35
 * @description: 针对表【sys_order(B2B子订单表)】的数据库操作Mapper
 */
@Mapper
public interface SysOrderMapper extends BaseMapper<SysOrder> {
    // 导出相关方法已删除，现在使用现有的orderProcessService.queryAllOrderList方法
}
