package com.knet.order.system.listener;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.OrderRefundMessage;
import com.knet.order.mq.producer.OrderProducer;
import com.knet.order.system.event.ApiOrderCancelledEvent;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/8/1 10:00
 * @description: API订单取消消息监听器 - 只退款不释放库存
 */
@Component
public class ApiOrderCancelledEventMessageListener {

    @Resource
    private OrderProducer orderProducer;

    @TransactionalEventListener(
            classes = ApiOrderCancelledEvent.class,
            // 事务提交后执行
            phase = TransactionPhase.AFTER_COMMIT
    )
    public void handleApiOrderCancelledEvent(ApiOrderCancelledEvent event) {
        OrderRefundMessage cancelledMessage = OrderRefundMessage.builder()
                .prentOrderId(event.getPrentOrderId())
                .orderItemId(event.getOrderItemId())
                .orderItemNo(event.getOrderItemNo())
                .userId(event.getUserId())
                .amount(event.getAmount())
                .timestamp(event.getTimestamp())
                .releaseInventory(false)
                //todo 关闭退款补偿2块
                .isCompensation(false)
                .build();
        //todo kg 订单取消，删除订单发票快照
        orderProducer.sendOrderRefundEvent(JSON.toJSONString(cancelledMessage));
    }
}
