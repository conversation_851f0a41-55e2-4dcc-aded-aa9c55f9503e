package com.knet.order.system.retry;

import com.knet.common.exception.ServiceException;
import feign.FeignException;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.net.ConnectException;
import java.net.SocketException;
import java.util.concurrent.TimeoutException;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2025/9/23 13:48
 * @description: 服务间重试包装类
 */
@Component
@Slf4j
public class RetryWrapper {

    /**
     * 默认重试
     *
     * @param operation     操作
     * @param operationName 操作名称
     * @param <T>           参数
     * @return 返回结果
     */
    @Retryable(
            backoff = @Backoff(delay = 1000, multiplier = 2.0),
            include = {
                    ConnectException.class,
                    SocketException.class,
                    FeignException.class,
                    TimeoutException.class,
                    RetryableException.class,
                    RuntimeException.class
            },
            exclude = {IllegalArgumentException.class}
    )
    public <T> T executeWithRetry(Supplier<T> operation, String operationName) {
        log.info("执行重试操作: {}", operationName);
        return operation.get();
    }

    /**
     * 快速重试
     *
     * @param operation     操作
     * @param operationName 操作名称
     * @param <T>           参数
     * @return 返回结果
     */
    @Retryable(
            backoff = @Backoff(delay = 500, multiplier = 1.5),
            maxAttempts = 5,
            include = {ConnectException.class, TimeoutException.class}
    )
    public <T> T executeWithFastRetry(Supplier<T> operation, String operationName) {
        log.info("执行快速重试操作: {}", operationName);
        return operation.get();
    }

    @Recover
    public <T> T recover(Exception ex, Supplier<T> operation, String operationName) {
        log.error("重试失败，操作: {}，异常: {}", operationName, ex.getMessage(), ex);
        throw new ServiceException("Service call failed: " + operationName, ex);
    }
}
