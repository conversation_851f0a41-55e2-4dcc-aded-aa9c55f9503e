package com.knet.order.system.config;

import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/9/25 13:45
 * @description: ??????
 */
@Getter
@Data
public class ExportTemplateConfig {
    /**
     * 管理员导出销售订单模版
     */
    public final static String ADMIN_SALE_ORDER_EXPORT_TEMPLATE =
            """
                    {
                      "columns": [
                        {
                          "field": "parentOrderId",
                          "title": "Parent Order No"
                        },
                        {
                          "field": "itemNo",
                          "title": "Item No"
                        },
                        {
                          "field": "userAccount",
                          "title": "User Account"
                        },
                        {
                          "field": "sku",
                          "title": "SKU"
                        },
                        {
                          "field": "productName",
                          "title": "Product Name"
                        },
                        {
                          "field": "size",
                          "title": "Size"
                        },
                        {
                          "field": "trackingNo",
                          "title": "Tracking No"
                        },
                        {
                          "field": "price",
                          "title": "Unit Price"
                        },
                        {
                          "field": "count",
                          "title": "Count"
                        },
                        {
                          "field": "itemStatus",
                          "title": "Item Status"
                        },
                        {
                          "field": "status",
                          "title": "Order Status"
                        },
                        {
                          "field": "totalQuantity",
                          "title": "Total Quantity"
                        },
                        {
                          "field": "totalAmount",
                          "title": "Total Amount"
                        },
                        {
                          "field": "createTime",
                          "title": "Order Create Time"
                        },
                        {
                          "field": "paidTime",
                          "title": "Order Paid Time"
                        }
                      ]
                    }
                    """;

}
