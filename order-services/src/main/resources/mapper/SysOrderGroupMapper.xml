<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.order.mapper.SysOrderGroupMapper">

    <!-- 分页查询订单列表（父订单信息） -->
    <select id="queryOrderList" parameterType="com.knet.order.model.dto.req.OrderListQueryRequest"
            resultType="com.knet.order.model.dto.resp.OrderListResponse$ParentOrderResponse">
        SELECT
        sog.order_id as parentOrderId,
        sog.user_id as userId,
        sog.status as status,
        sog.total_amount as totalAmount,
        sog.create_time as createTime,
        COALESCE(sub_summary.totalQuantity, 0) as totalQuantity
        FROM sys_order_group sog
        LEFT JOIN (
        SELECT
        parent_order_id,
        SUM(total_quantity) as totalQuantity
        FROM sys_order
        WHERE del_flag = 0
        GROUP BY parent_order_id
        ) sub_summary ON sog.order_id = sub_summary.parent_order_id
        WHERE sog.del_flag = 0
        AND sog.status NOT IN ( 1, 8, 9)
        <if test="request.userId != null">
            AND sog.user_id = #{request.userId}
        </if>
        <if test="request.orderId != null and request.orderId != ''">
            AND (sog.order_id LIKE CONCAT( #{request.orderId}, '%')
            OR EXISTS (
            SELECT 1 FROM sys_order_item so
            WHERE so.parent_order_id = sog.order_id
            AND so.item_no LIKE CONCAT( #{request.orderId}, '%')
            AND so.del_flag = 0
            ))
        </if>
        <choose>
            <when test="request.searchType != null">
                <choose>
                    <when test="request.searchType.name() == 'CLOSED'">
                        AND sog.status IN (12)
                    </when>
                    <when test="request.searchType.name() == 'CANCELLED'">
                        AND sog.status IN (6)
                    </when>
                    <when test="request.searchType.name() == 'COMPLETED'">
                        AND sog.status = 5
                    </when>
                    <when test="request.searchType.name() == 'PENDING'">
                        AND sog.status IN (3, 4, 7, 10)
                    </when>
                </choose>
            </when>
        </choose>
        ORDER BY sog.create_time DESC
    </select>

    <!-- 查询指定父订单的子订单汇总信息 -->
    <select id="querySubOrderSummary" resultType="com.knet.order.model.vo.SubOrderSummaryVo">
        SELECT
        so.parent_order_id as parentOrderId,
        so.sku as sku,
        so.product_name as productName,
        so.image_url as imageUrl,
        SUM(so.total_quantity) as totalQuantity,
        AVG(so.total_amount / so.total_quantity) as avgPrice,
        SUM(so.total_amount) as totalPrice,
        so.status as status
        FROM sys_order so
        WHERE so.del_flag = 0
        AND so.parent_order_id IN
        <foreach collection="parentOrderIds" item="parentOrderId" open="(" separator="," close=")">
            #{parentOrderId}
        </foreach>
        GROUP BY so.parent_order_id, so.sku, so.product_name, so.image_url, so.status
        ORDER BY so.parent_order_id, so.sku
    </select>

    <!-- 管理员分页查询所有用户的订单列表（父订单信息） -->
    <select id="queryOrderListForAdmin" parameterType="com.knet.order.model.dto.req.AdminOrderListQueryRequest"
            resultType="com.knet.order.model.dto.resp.OrderListResponse$ParentOrderResponse">
        SELECT
        sog.order_id as parentOrderId,
        sog.user_id as userId,
        sog.status as status,
        sog.total_amount as totalAmount,
        sog.create_time as createTime,
        COALESCE(sub_summary.totalQuantity, 0) as totalQuantity
        FROM sys_order_group sog
        LEFT JOIN (
        SELECT
        parent_order_id,
        SUM(total_quantity) as totalQuantity
        FROM sys_order
        WHERE del_flag = 0
        GROUP BY parent_order_id
        ) sub_summary ON sog.order_id = sub_summary.parent_order_id
        WHERE sog.del_flag = 0
        AND sog.status NOT IN (1, 8, 9)
        <!-- 动态添加 userIds 条件 -->
        <if test="request.userIds != null and request.userIds.size() > 0">
            AND sog.user_id IN
            <foreach collection="request.userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="request.parentOrderId != null and request.parentOrderId != ''">
            AND sog.order_id LIKE CONCAT( #{request.parentOrderId}, '%')
        </if>
        <if test="request.orderId != null and request.orderId != ''">
            AND EXISTS (
            SELECT 1 FROM sys_order_item item
            WHERE item.parent_order_id = sog.order_id
            AND item.item_no LIKE CONCAT( #{request.orderId}, '%')
            AND item.del_flag = 0
            )
        </if>
        <!-- 时间范围过滤 -->
        <if test="request.startTime != null and request.endTime != null">
            AND sog.create_time BETWEEN #{request.startTime} AND #{request.endTime}
        </if>
        <choose>
            <when test="request.searchType != null">
                <choose>
                    <when test="request.searchType.name() == 'CLOSED'">
                        AND sog.status IN (12)
                    </when>
                    <when test="request.searchType.name() == 'CANCELLED'">
                        AND sog.status IN (6)
                    </when>
                    <when test="request.searchType.name() == 'COMPLETED'">
                        AND sog.status = 5
                    </when>
                    <when test="request.searchType.name() == 'PENDING'">
                        AND sog.status IN (3, 4, 7, 10)
                    </when>
                </choose>
            </when>
        </choose>
        ORDER BY sog.create_time DESC
    </select>


    <!-- 管理员分页查询所有用户的订单列表详情展开导出用 -->
    <select id="queryOrderListForAdminExport" parameterType="com.knet.order.model.dto.req.AdminOrderListQueryRequest"
            resultType="com.knet.order.model.dto.resp.ExportAdminSaleOrderResponse">
        SELECT
        sog.order_id AS parentOrderId,
        item_no,
        sog.user_id AS userId,
        item.sku,
        item.name AS productName,
        item.size,
        item.count,
        lable.tracking_number AS trackingNo,
        item.`status` as itemStatus,
        sog.STATUS AS `status`,
        item.kg_owning_price as price,
        COALESCE( sub_summary.totalQuantity, 0 ) AS totalQuantity,
        sog.total_amount AS totalAmount,
        sog.create_time AS createTime,
        item.paid_time
        FROM
        sys_order_group sog
        LEFT JOIN
        ( SELECT parent_order_id, SUM( total_quantity ) AS totalQuantity FROM sys_order WHERE del_flag = 0 GROUP BY
        parent_order_id )
        sub_summary
        ON sog.order_id = sub_summary.parent_order_id
        LEFT JOIN sys_order_item item ON sog.order_id=item.parent_order_id
        LEFT JOIN sys_shipping_item_rel rel ON item.item_id=rel.item_id
        LEFT JOIN sys_shipping_label lable ON lable.id=rel.label_id
        WHERE
        sog.del_flag = 0
        AND sog.STATUS NOT IN ( 1, 8, 9 )
        <!-- 动态添加 userIds 条件 -->
        <if test="request.userIds != null and request.userIds.size() > 0">
            AND sog.user_id IN
            <foreach collection="request.userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="request.parentOrderId != null and request.parentOrderId != ''">
            AND sog.order_id LIKE CONCAT( #{request.parentOrderId}, '%')
        </if>
        <if test="request.orderId != null and request.orderId != ''">
            AND item.item_no LIKE CONCAT( #{request.orderId}, '%')
        </if>
        <!-- 时间范围过滤 -->
        <if test="request.startTime != null and request.endTime != null">
            AND sog.create_time BETWEEN #{request.startTime} AND #{request.endTime}
        </if>
        <choose>
            <when test="request.searchType != null">
                <choose>
                    <when test="request.searchType.name() == 'CLOSED'">
                        AND sog.status IN (12)
                    </when>
                    <when test="request.searchType.name() == 'CANCELLED'">
                        AND sog.status IN (6)
                    </when>
                    <when test="request.searchType.name() == 'COMPLETED'">
                        AND sog.status = 5
                    </when>
                    <when test="request.searchType.name() == 'PENDING'">
                        AND sog.status IN (3, 4, 7, 10)
                    </when>
                </choose>
            </when>
        </choose>
        ORDER BY sog.create_time DESC
    </select>
</mapper>
