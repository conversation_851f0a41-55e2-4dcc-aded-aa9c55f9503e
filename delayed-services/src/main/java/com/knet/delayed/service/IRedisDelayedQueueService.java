package com.knet.delayed.service;


import com.knet.common.dto.message.DelayedMessage;

/**
 * <AUTHOR>
 * @date 2025/6/25 16:47
 * @description:
 */
public interface IRedisDelayedQueueService {
    /**
     * 添加延迟消息
     *
     * @param message 延迟消息
     */
    void addDelayMessage(DelayedMessage message);

    /**
     * 取消延迟消息
     * 根据订单ID取消对应的延迟消息，防止已取消订单的延迟消息被触发
     *
     * @param orderId 订单ID
     * @return 是否取消成功
     */
    boolean cancelDelayedMessage(String orderId);

    /**
     * 根据消息ID取消延迟消息
     *
     * @param messageId 消息ID
     * @return 是否取消成功
     */
    boolean cancelDelayedMessageById(String messageId);
}
