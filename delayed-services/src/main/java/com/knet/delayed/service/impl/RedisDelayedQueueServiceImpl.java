package com.knet.delayed.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.knet.common.dto.message.DelayedMessage;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.delayed.service.IRedisDelayedQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/6/25 16:47
 * @description:
 */
@Slf4j
@Service
public class RedisDelayedQueueServiceImpl implements IRedisDelayedQueueService {

    private static final String DELAYED_QUEUE_KEY = "delayed_queue:zset";

    /**
     * 添加延迟消息到队列
     *
     * @param message 延迟消息
     */
    @Override
    public void addDelayMessage(DelayedMessage message) {
        String messageJson = JSON.toJSONString(message);
        RedisCacheUtil.addZSet(DELAYED_QUEUE_KEY, messageJson, message.getTriggerTime());
        log.info("添加延迟消息到队列: messageId={}, orderId={}, triggerTime={}",
                message.getId(), extractOrderIdFromPayload(message.getPayloadJson()), message.getTriggerTime());
    }

    /**
     * 取消延迟消息
     * 根据父订单ID取消对应的延迟消息，防止已取消订单的延迟消息被触发
     *
     * @param prentOrderId 父订单ID
     * @return 是否取消成功
     */
    @Override
    public boolean cancelDelayedMessage(String prentOrderId) {
        if (StrUtil.isBlank(prentOrderId)) {
            log.warn("订单ID为空，无法取消延迟消息");
            return false;
        }
        try {
            Set<Object> allMessages = RedisCacheUtil.getZSetAll(DELAYED_QUEUE_KEY);
            if (allMessages == null || allMessages.isEmpty()) {
                log.info("延迟队列为空，无需取消: prentOrderId={}", prentOrderId);
                return false;
            }
            int cancelledCount = 0;
            int totalMessages = allMessages.size();
            log.debug("开始扫描延迟消息队列: prentOrderId={}, 总消息数={}", prentOrderId, totalMessages);
            for (Object obj : allMessages) {
                try {
                    String jsonStr = (String) obj;
                    if (StrUtil.isBlank(jsonStr)) {
                        log.warn("发现空的延迟消息，跳过处理");
                        continue;
                    }
                    DelayedMessage msg = JSON.parseObject(jsonStr, DelayedMessage.class);
                    if (msg == null) {
                        log.warn("延迟消息解析失败，跳过处理: jsonStr={}", jsonStr);
                        continue;
                    }
                    // 从消息体中提取订单ID进行匹配
                    String messageOrderId = extractOrderIdFromPayload(msg.getPayloadJson());
                    if (prentOrderId.equals(messageOrderId)) {
                        // 从ZSet中移除该消息
                        RedisCacheUtil.removeZSet(DELAYED_QUEUE_KEY, obj);
                        cancelledCount++;
                        log.info("成功取消延迟消息: messageId={}, prentOrderId={}, triggerTime={}",
                                msg.getId(), prentOrderId, msg.getTriggerTime());
                    }
                } catch (Exception e) {
                    log.error("处理单个延迟消息时发生异常: obj={}, error={}", obj, e.getMessage(), e);
                }
            }
            if (cancelledCount > 0) {
                log.info("延迟消息取消完成: prentOrderId={}, 取消数量={}, 扫描总数={}", prentOrderId, cancelledCount, totalMessages);
                return true;
            } else {
                log.info("未找到需要取消的延迟消息: prentOrderId={}, 扫描总数={}", prentOrderId, totalMessages);
                return false;
            }
        } catch (Exception e) {
            log.error("取消延迟消息失败: prentOrderId={}, error={}", prentOrderId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据消息ID取消延迟消息
     *
     * @param messageId 消息ID
     * @return 是否取消成功
     */
    @Override
    public boolean cancelDelayedMessageById(String messageId) {
        if (StrUtil.isBlank(messageId)) {
            log.warn("消息ID为空，无法取消延迟消息");
            return false;
        }
        try {
            Set<Object> allMessages = RedisCacheUtil.getZSetAll(DELAYED_QUEUE_KEY);
            if (allMessages == null || allMessages.isEmpty()) {
                log.info("延迟队列为空，无需取消: messageId={}", messageId);
                return false;
            }
            int totalMessages = allMessages.size();
            log.debug("开始扫描延迟消息队列: messageId={}, 总消息数={}", messageId, totalMessages);
            for (Object obj : allMessages) {
                try {
                    String jsonStr = (String) obj;
                    if (StrUtil.isBlank(jsonStr)) {
                        log.warn("发现空的延迟消息，跳过处理");
                        continue;
                    }
                    DelayedMessage msg = JSON.parseObject(jsonStr, DelayedMessage.class);
                    if (msg == null) {
                        log.warn("延迟消息解析失败，跳过处理: jsonStr={}", jsonStr);
                        continue;
                    }
                    if (messageId.equals(msg.getId())) {
                        // 从ZSet中移除该消息
                        RedisCacheUtil.removeZSet(DELAYED_QUEUE_KEY, obj);
                        log.info("成功取消延迟消息: messageId={}, triggerTime={}", messageId, msg.getTriggerTime());
                        return true;
                    }
                } catch (Exception e) {
                    log.error("处理单个延迟消息时发生异常: obj={}, error={}", obj, e.getMessage(), e);
                }
            }
            log.info("未找到需要取消的延迟消息: messageId={}, 扫描总数={}", messageId, totalMessages);
            return false;
        } catch (Exception e) {
            log.error("取消延迟消息失败: messageId={}, error={}", messageId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从消息体中提取订单ID
     *
     * @param payloadJson 消息体JSON
     * @return 订单ID
     */
    private String extractOrderIdFromPayload(String payloadJson) {
        try {
            if (StrUtil.isBlank(payloadJson)) {
                log.warn("消息体为空，无法提取订单ID");
                return null;
            }
            // 解析OrderMessage格式的JSON: {"orderId":"xxx","userId":123,"totalAmount":100,"eventType":"ORDER_CREATED","timestamp":1640995200000}
            JSONObject jsonObject = JSON.parseObject(payloadJson);
            String orderId = jsonObject.getString("orderId");
            if (StrUtil.isBlank(orderId)) {
                log.warn("消息体中未找到orderId字段: payloadJson={}", payloadJson);
                return null;
            }
            return orderId;
        } catch (Exception e) {
            log.error("提取订单ID失败: payloadJson={}, error={}", payloadJson, e.getMessage(), e);
            return null;
        }
    }
}
