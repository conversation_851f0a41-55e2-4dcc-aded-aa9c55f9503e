package com.knet.goods.strategy.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.dto.message.InventoryLockSuccessMessage;
import com.knet.common.enums.ProductStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.goods.mapper.KnetProductMapper;
import com.knet.goods.model.dto.third.resp.SubOrderItemResp;
import com.knet.goods.model.entity.KnetProduct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/6 16:00
 * @description: 默认库存锁定策略实现
 */
@Slf4j
@Component
public class DefaultInventoryLockStrategy extends AbstractInventoryLockStrategy {

    @Resource
    private KnetProductMapper knetProductMapper;


    /**
     * 锁定单个商品项
     * 使用分布式锁确保多实例并发场景下的数据一致性
     *
     * @param item    商品项
     * @param account 用户账号
     * @return 锁定的商品信息
     */
    @Override
    @DistributedLock(key = "'inventory:lock:' + #item.sku + ':' + #item.size + ':' + #item.price", expire = 30)
    protected InventoryLockSuccessMessage.LockedProductInfo lockSingleItem(SubOrderItemResp item, String account) {
        // 将策略价格转换为原始价格进行库存匹配
        Long strategyPriceCents = PriceFormatUtil.formatYuanToCents(item.getPrice());
        Long originalPriceCents = PriceFormatUtil.formatYuanToCents(item.getPrice());
        log.info("库存锁定价格转换: SKU={}, 策略价格={}美分, 原始价格={}美分",
                item.getSku(), strategyPriceCents, originalPriceCents);
        // 查询可锁定的商品,排除用户自己的商品
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(KnetProduct::getSku, item.getSku())
                .eq(KnetProduct::getPrice, originalPriceCents)
                .eq(KnetProduct::getSpec, item.getSize())
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .ne(StrUtil.isNotBlank(account), KnetProduct::getSource, account)
                .orderByDesc(KnetProduct::getCreateTime)
                .last("LIMIT " + item.getCount());
        List<KnetProduct> productsToLock = knetProductMapper.selectList(queryWrapper);
        if (productsToLock.size() < item.getCount()) {
            log.error("商品: {} 尺码: {} 原始价格: {} 库存不足，需要{}个，实际只有{}个",
                    item.getSku(), item.getSize(), originalPriceCents, item.getCount(), productsToLock.size());
            throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 库存不足");
        }
        List<Long> idsToLock = productsToLock.stream().map(KnetProduct::getId).toList();
        // 执行原子性锁定操作，使用乐观锁确保状态一致性
        int updatedCount = 0;
        for (Long productId : idsToLock) {
            // 先查询当前商品状态和版本号
            KnetProduct currentProduct = knetProductMapper.selectById(productId);
            if (currentProduct == null || !ProductStatus.ON_SALE.equals(currentProduct.getStatus())) {
                log.warn("商品状态已变更，跳过锁定: productId={}, currentStatus={}",
                        productId, currentProduct != null ? currentProduct.getStatus() : "NOT_FOUND");
                continue;
            }
            // 使用乐观锁更新，确保状态和版本号都匹配
            LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(KnetProduct::getId, productId)
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                    .eq(KnetProduct::getVersion, currentProduct.getVersion())
                    .set(KnetProduct::getStatus, ProductStatus.LOCKED)
                    .set(KnetProduct::getVersion, currentProduct.getVersion() + 1);
            int singleUpdated = knetProductMapper.update(null, updateWrapper);
            if (singleUpdated > 0) {
                updatedCount++;
                log.debug("商品锁定成功: productId={}, version={}->{}",
                        productId, currentProduct.getVersion(), currentProduct.getVersion() + 1);
            } else {
                log.warn("商品锁定失败，可能被其他线程修改: productId={}, version={}",
                        productId, currentProduct.getVersion());
            }
        }
        if (updatedCount != idsToLock.size()) {
            log.error("商品锁定失败，期望锁定{}个，实际锁定{}个。可能原因：商品状态已变更或被其他实例锁定",
                    idsToLock.size(), updatedCount);
            throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 锁定失败，商品状态已变更");
        }
        // 重新查询已锁定的商品以获取完整信息
        List<KnetProduct> lockedProducts = knetProductMapper.selectByIds(idsToLock);
        List<String> oneIds = lockedProducts.stream().map(KnetProduct::getOneId).toList();
        List<String> listingIds = lockedProducts.stream().map(KnetProduct::getListingId).toList();
        log.info("商品锁定成功 - SKU: {}, 尺码: {}, 原始价格: {}, 锁定数量: {}, 商品ID列表: {},商品oneId: {},商品listingId: {}",
                item.getSku(), item.getSize(), originalPriceCents, updatedCount, idsToLock, oneIds, listingIds);
        // 构建锁定成功的商品详情
        List<InventoryLockSuccessMessage.ProductDetail> productDetails = lockedProducts
                .stream()
                .map(KnetProduct::createProductDetail)
                .toList();
        return InventoryLockSuccessMessage.LockedProductInfo.builder()
                .sku(item.getSku())
                .size(item.getSize())
                .price(item.getPrice())
                .productDetails(productDetails)
                .build();
    }

    @Override
    public String getStrategyName() {
        return "DEFAULT";
    }
}
