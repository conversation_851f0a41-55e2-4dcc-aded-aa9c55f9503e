package com.knet.goods.strategy;

import com.knet.common.dto.message.InventoryLockSuccessMessage;
import com.knet.goods.model.dto.req.CheckAndLockInvRequest;

/**
 * <AUTHOR>
 * @date 2025/9/6 16:00
 * @description: 库存锁定策略接口
 */
public interface InventoryLockStrategy {

    /**
     * 锁定库存并返回锁定的商品信息
     *
     * @param request 锁定请求
     * @param orderId 订单ID
     * @param userId  用户ID
     * @return 锁定成功的商品信息
     */
    InventoryLockSuccessMessage lockInventoryWithDetails(CheckAndLockInvRequest request, String orderId, Long userId);

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();
}
