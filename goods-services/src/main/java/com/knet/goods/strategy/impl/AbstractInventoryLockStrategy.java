package com.knet.goods.strategy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.knet.common.base.HttpResult;
import com.knet.common.dto.message.InventoryLockSuccessMessage;
import com.knet.common.exception.ServiceException;
import com.knet.goods.model.dto.req.CheckAndLockInvRequest;
import com.knet.goods.model.dto.third.resp.KnetUserInfoDtoResp;
import com.knet.goods.model.dto.third.resp.SubOrderItemResp;
import com.knet.goods.openfeign.ApiOrderServiceProvider;
import com.knet.goods.openfeign.ApiUserServiceProvider;
import com.knet.goods.strategy.InventoryLockStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/6 16:00
 * @description: 库存锁定策略抽象基类
 * 提供通用的库存锁定流程，子类只需实现具体的锁定逻辑
 */
@Slf4j
public abstract class AbstractInventoryLockStrategy implements InventoryLockStrategy {

    @Resource
    protected ApiUserServiceProvider userServiceProvider;
    @Resource
    protected ApiOrderServiceProvider apiOrderServiceProvider;

    /**
     * 验证oneId没有被重复分配给不同订单
     * 在多实例并发场景下，作为最终的安全检查
     *
     * @param lockedProducts 已锁定的商品列表
     * @param currentOrderId 当前订单ID
     * @throws ServiceException 如果发现重复分配
     */
    protected void validateOneIdNotDuplicatelyAssigned(List<InventoryLockSuccessMessage.LockedProductInfo> lockedProducts, String currentOrderId) {
        if (CollUtil.isEmpty(lockedProducts)) {
            return;
        }
        // 提取所有oneId
        List<String> oneIds = lockedProducts.stream()
                .flatMap(product -> product.getProductDetails().stream())
                .map(InventoryLockSuccessMessage.ProductDetail::getOneId)
                .filter(oneId -> oneId != null && !oneId.isEmpty())
                .distinct()
                .toList();
        if (oneIds.isEmpty()) {
            return;
        }
        log.info("{}策略检查oneId重复分配: orderId={}, oneIds={}", getStrategyName(), currentOrderId, oneIds);
        try {
            // 调用订单服务检查这些oneId是否已被其他订单使用
            HttpResult<List<String>> conflictResult = apiOrderServiceProvider.checkOneIdConflicts(oneIds, currentOrderId);
            if (conflictResult != null && conflictResult.success() && CollUtil.isNotEmpty(conflictResult.getData())) {
                List<String> conflictOneIds = conflictResult.getData();
                log.error("{}策略发现oneId重复分配: orderId={}, 冲突的oneIds={}", getStrategyName(), currentOrderId, conflictOneIds);
                throw new ServiceException("商品重复分配检测到冲突，oneIds: " + String.join(", ", conflictOneIds) + "。请重新下单或联系客服。");
            }
            log.info("{}策略oneId重复分配检查通过: orderId={}", getStrategyName(), currentOrderId);
        } catch (Exception e) {
            log.warn("{}策略oneId重复分配检查失败，但继续执行: orderId={}, error={}", getStrategyName(), currentOrderId, e.getMessage());
            throw new ServiceException("Insufficient inventory, please place an order again or contact customer service");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InventoryLockSuccessMessage lockInventoryWithDetails(CheckAndLockInvRequest request, String orderId, Long userId) {
        if (CollUtil.isEmpty(request.getItems())) {
            throw new ServiceException("锁定请求商品列表为空");
        }
        // 获取用户信息
        HttpResult<KnetUserInfoDtoResp> userInfo = userServiceProvider.getUserById(userId);
        KnetUserInfoDtoResp user = userInfo.getData();
        if (BeanUtil.isEmpty(user)) {
            log.error("创建订单的用户信息不存在 userId:{}", userId);
        }
        // 锁定每个商品项
        List<InventoryLockSuccessMessage.LockedProductInfo> lockedProducts = new ArrayList<>();
        for (SubOrderItemResp item : request.getItems()) {
            InventoryLockSuccessMessage.LockedProductInfo lockedProductInfo = lockSingleItem(item, user.getAccount());
            lockedProducts.add(lockedProductInfo);
        }
        // 检查是否有oneId被分配给了不同的订单（多实例并发场景下的最终检查）
        validateOneIdNotDuplicatelyAssigned(lockedProducts, orderId);
        log.info("{}策略库存锁定完成: orderId={}, userId={}", getStrategyName(), orderId, userId);
        return InventoryLockSuccessMessage.builder()
                .orderId(orderId)
                .userId(userId)
                .lockedProducts(lockedProducts)
                .build();
    }

    /**
     * 锁定单个商品项的具体实现
     * 子类需要实现具体的锁定逻辑
     *
     * @param item    商品项
     * @param account 用户账号
     * @return 锁定的商品信息
     */
    protected abstract InventoryLockSuccessMessage.LockedProductInfo lockSingleItem(SubOrderItemResp item, String account);
}
