package com.knet.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.goods.service.ISkuCacheService;
import com.knet.goods.service.ISysSkuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.knet.common.constants.GoodsServicesConstants.*;

/**
 * SKU缓存服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Slf4j
@Service
public class SkuCacheServiceImpl implements ISkuCacheService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private ISysSkuService sysSkuService;

    @Override
    public void initSkuCache() {
        try {
            RedisCacheUtil.hmdel(SKU_CACHE_KEY);
            // 从数据库加载所有SKU_INDEXED和SKU的映射数据
            Map<String, Map<String, Object>> skuIndexedMapping = sysSkuService.selectAllDistinctSkuIndexedMapping();
            if (MapUtil.isNotEmpty(skuIndexedMapping)) {
                // 只构建一个缓存：键为sku_indexed（已经是大写且去空格），值为sku
                Map<String, Object> skuMap = new HashMap<>();
                skuIndexedMapping.forEach((skuIndexed, value) -> {
                    String sku = (String) value.get("sku");
                    skuMap.put(skuIndexed, sku);
                });
                // 批量写入Redis Hash
                RedisCacheUtil.hmset(SKU_CACHE_KEY, skuMap, SKU_CACHE_KEY_EXPIRED_TIME);
                log.info("成功加载{}条SKU数据到缓存", skuMap.size());
            }
        } catch (Exception e) {
            log.error("初始化SKU缓存失败", e);
            throw new RuntimeException("初始化SKU缓存失败", e);
        }
    }

    @Override
    public void refreshSkuCache() {
        log.info("开始刷新SKU缓存...");
        initSkuCache();
        log.info("SKU缓存刷新完成");
    }

    @Override
    public Set<String> matchSkus(String keyword, Integer maxResults) {
        if (StrUtil.isBlank(keyword)) {
            return new HashSet<>();
        }
        //清洗数据 去掉空格，去掉-符号，转换为大写用于Redis pattern匹配
        final String formattedKeyword = keyword.replaceAll(" ", "").replaceAll("-", "").toUpperCase();
        try {
            // 检查缓存是否存在
            if (!RedisCacheUtil.hasKey(SKU_CACHE_KEY)) {
                log.warn("SKU缓存为空，尝试重新初始化缓存");
                initSkuCache();
                if (!RedisCacheUtil.hasKey(SKU_CACHE_KEY)) {
                    log.error("初始化SKU缓存失败，返回空结果");
                    return new HashSet<>();
                }
            }
            // 使用线程安全的Set来收集匹配结果，预设最大容量
            Set<String> matchedSkus = Collections.synchronizedSet(new LinkedHashSet<>(maxResults));
            // 构建Redis模式匹配字符串，使用大写进行精确匹配
            String pattern = "*" + formattedKeyword + "*";
            // 使用hscanWithPattern直接在Redis层面进行模式匹配，每批1000条
            RedisCacheUtil.hscanWithPattern(SKU_CACHE_KEY, pattern, 1000, batch -> {
                // 如果已经找到足够的结果，则停止处理
                if (matchedSkus.size() >= maxResults) {
                    return;
                }
                // 获取匹配的key（sku_indexed的大写）而不是值
                Set<String> batchMatched = batch.keySet().parallelStream()
                        .map(Object::toString)
                        .filter(StrUtil::isNotBlank)
                        .limit(maxResults - matchedSkus.size()) // 限制结果数量
                        .sorted(String.CASE_INSENSITIVE_ORDER)
                        .collect(Collectors.toCollection(LinkedHashSet::new));
                // 将匹配结果添加到总结果集中
                synchronized (matchedSkus) {
                    if (matchedSkus.size() < maxResults) {
                        int remainingSpace = maxResults - matchedSkus.size();
                        batchMatched.stream()
                                .limit(remainingSpace)
                                .forEach(matchedSkus::add);
                    }
                }
            });
            log.debug("关键词'{}'匹配到{}个SKU", keyword, matchedSkus.size());
            return matchedSkus;
        } catch (Exception e) {
            log.error("SKU模糊匹配失败，关键词: {}", keyword, e);
            return new HashSet<>();
        }
    }

    @Override
    public void initKnetProductRemarkCache() {
        try {
            RedisCacheUtil.hmdel(KNET_PRODUCT_REMARK_CACHE_KEY);
            // 从数据库加载所有条knet_product和remark数据
            List<String> remarks = sysSkuService.selectAllDistinctRemarks();
            if (CollUtil.isNotEmpty(remarks)) {
                // 批量写入Redis Hash，键转换为大写用于匹配，值保持原始大小写
                Map<String, Object> remarksMap = remarks.stream()
                        .collect(Collectors.toMap(
                                String::toUpperCase,
                                remark -> remark,
                                (existing, replacement) -> existing
                        ));
                RedisCacheUtil.hmset(KNET_PRODUCT_REMARK_CACHE_KEY, remarksMap, KNET_PRODUCT_REMARK_CACHE_KEY_EXPIRED_TIME);
                log.info("成功加载{}条knet_product remark数据到缓存", remarksMap.size());
            }
        } catch (Exception e) {
            log.error("初始化条knet_product remark缓存失败", e);
            throw new RuntimeException("初始化条knet_product remark缓存失败", e);
        }
    }

    @Override
    public void initSkuRemarkCache() {
        try {
            RedisCacheUtil.hmdel(SKU_REMARK_CACHE_KEY);
            // 从数据库加载所有条knet_product和remark数据
            List<String> remarks = sysSkuService.selectAllDistinctSysSkuRemarks();
            if (CollUtil.isNotEmpty(remarks)) {
                // 批量写入Redis Hash，键转换为大写用于匹配，值保持原始大小写
                Map<String, Object> remarksMap = remarks.stream()
                        .collect(Collectors.toMap(
                                String::toUpperCase,
                                remark -> remark,
                                (existing, replacement) -> existing
                        ));
                RedisCacheUtil.hmset(SKU_REMARK_CACHE_KEY, remarksMap, SKU_REMARK_CACHE_KEY_EXPIRED_TIME);
                log.info("成功加载{}条sys_sku remark数据到缓存", remarksMap.size());
            }
        } catch (Exception e) {
            log.error("初始化条sys_sku remark缓存失败", e);
            throw new RuntimeException("初始化条sys_sku remark缓存失败", e);
        }
    }

    @Override
    public void refreshProductRemarkCache() {
        log.info("开始刷新knet_product remark缓存...");
        initKnetProductRemarkCache();
        log.info("knet_product remark缓存刷新完成");
    }

    @Override
    public void refreshSysSkuRemarkCache() {
        log.info("开始刷新sys_sku remark缓存...");
        initSkuRemarkCache();
        log.info("开始刷新sys_sku remark缓存刷新完成");
    }

    @Override
    public Set<String> matchProductsByRemark(String keyword, Integer maxResults) {
        if (StrUtil.isBlank(keyword)) {
            return new HashSet<>();
        }
        // 将关键词按空格分割为多个词，去除空白词，转换为大写
        String[] keywords = keyword.trim().split("\\s+");
        List<String> keywordList = Arrays.stream(keywords)
                .filter(StrUtil::isNotBlank)
                .map(String::toUpperCase)
                .toList();
        if (keywordList.isEmpty()) {
            return new HashSet<>();
        }
        try {
            // 检查缓存是否存在
            if (!RedisCacheUtil.hasKey(KNET_PRODUCT_REMARK_CACHE_KEY)) {
                log.warn("Knet Product remark缓存为空，尝试重新初始化缓存");
                initKnetProductRemarkCache();
                if (!RedisCacheUtil.hasKey(KNET_PRODUCT_REMARK_CACHE_KEY)) {
                    log.error("初始化knet product remark缓存失败，返回空结果");
                    return new HashSet<>();
                }
            }
            // 使用线程安全的Set来收集匹配结果，设置最大容量为10000
            Set<String> matchedRemarks = Collections.synchronizedSet(new LinkedHashSet<>(maxResults));
            // 如果只有一个关键词，使用原来的模式匹配方式
            if (keywordList.size() == 1) {
                String pattern = "*" + keywordList.get(0) + "*";
                RedisCacheUtil.hscanWithPattern(KNET_PRODUCT_REMARK_CACHE_KEY, pattern, 1000, batch -> {
                    if (matchedRemarks.size() >= maxResults) {
                        return;
                    }
                    Set<String> batchMatched = batch.values().parallelStream()
                            .map(Object::toString)
                            .filter(StrUtil::isNotBlank)
                            .limit(maxResults - matchedRemarks.size())
                            .sorted(String.CASE_INSENSITIVE_ORDER)
                            .collect(Collectors.toCollection(LinkedHashSet::new));
                    synchronized (matchedRemarks) {
                        if (matchedRemarks.size() < maxResults) {
                            int remainingSpace = maxResults - matchedRemarks.size();
                            batchMatched.stream()
                                    .limit(remainingSpace)
                                    .forEach(matchedRemarks::add);
                        }
                    }
                });
            }
            if (keywordList.size() > 1) {
                // 多关键词匹配：获取所有缓存数据，然后进行多关键词匹配
                Map<Object, Object> allEntries = redisTemplate.opsForHash().entries(KNET_PRODUCT_REMARK_CACHE_KEY);
                Set<String> batchMatched = allEntries.values().parallelStream()
                        .map(Object::toString)
                        .filter(StrUtil::isNotBlank)
                        .filter(remark -> {
                            // 检查remark是否包含所有关键词（忽略大小写）
                            String upperRemark = remark.toUpperCase().replaceAll("\\s+", " ");
                            return keywordList.stream().allMatch(upperRemark::contains);
                        })
                        .limit(maxResults)
                        .sorted(String.CASE_INSENSITIVE_ORDER)
                        .collect(Collectors.toCollection(LinkedHashSet::new));
                matchedRemarks.addAll(batchMatched);
            }
            log.debug("关键词'{}'在remark中匹配到{}个", keyword, matchedRemarks.size());
            return matchedRemarks;
        } catch (Exception e) {
            log.error("knet product remark模糊匹配失败，关键词: {}", keyword, e);
            return new HashSet<>();
        }
    }

    @Override
    public Set<String> matchSysSkuByRemark(String keyword, Integer maxResults) {
        if (StrUtil.isBlank(keyword)) {
            return new HashSet<>();
        }
        // 将关键词按空格分割为多个词，去除空白词，转换为大写
        String[] keywords = keyword.trim().split("\\s+");
        List<String> keywordList = Arrays.stream(keywords)
                .filter(StrUtil::isNotBlank)
                .map(String::toUpperCase)
                .toList();
        if (keywordList.isEmpty()) {
            return new HashSet<>();
        }
        try {
            // 检查缓存是否存在
            if (!RedisCacheUtil.hasKey(SKU_REMARK_CACHE_KEY)) {
                log.warn("Knet sys_sku remark缓存为空，尝试重新初始化缓存");
                initSkuRemarkCache();
                if (!RedisCacheUtil.hasKey(SKU_REMARK_CACHE_KEY)) {
                    log.error("初始化 sys_sku remark缓存失败，返回空结果");
                    return new HashSet<>();
                }
            }
            // 使用线程安全的Set来收集匹配结果，设置最大容量为10000
            Set<String> matchedRemarks = Collections.synchronizedSet(new LinkedHashSet<>(maxResults));
            // 如果只有一个关键词，使用原来的模式匹配方式
            if (keywordList.size() == 1) {
                String pattern = "*" + keywordList.get(0) + "*";
                RedisCacheUtil.hscanWithPattern(SKU_REMARK_CACHE_KEY, pattern, 1000, batch -> {
                    if (matchedRemarks.size() >= maxResults) {
                        return;
                    }
                    Set<String> batchMatched = batch.values().parallelStream()
                            .map(Object::toString)
                            .filter(StrUtil::isNotBlank)
                            .limit(maxResults - matchedRemarks.size())
                            .sorted(String.CASE_INSENSITIVE_ORDER)
                            .collect(Collectors.toCollection(LinkedHashSet::new));
                    synchronized (matchedRemarks) {
                        if (matchedRemarks.size() < maxResults) {
                            int remainingSpace = maxResults - matchedRemarks.size();
                            batchMatched.stream()
                                    .limit(remainingSpace)
                                    .forEach(matchedRemarks::add);
                        }
                    }
                });
            }
            if (keywordList.size() > 1) {
                // 多关键词匹配：获取所有缓存数据，然后进行多关键词匹配
                Map<Object, Object> allEntries = redisTemplate.opsForHash().entries(SKU_REMARK_CACHE_KEY);
                Set<String> batchMatched = allEntries.values().parallelStream()
                        .map(Object::toString)
                        .filter(StrUtil::isNotBlank)
                        .filter(remark -> {
                            // 检查remark是否包含所有关键词（忽略大小写）
                            String upperRemark = remark.toUpperCase().replaceAll("\\s+", " ");
                            return keywordList.stream().allMatch(upperRemark::contains);
                        })
                        .limit(maxResults)
                        .sorted(String.CASE_INSENSITIVE_ORDER)
                        .collect(Collectors.toCollection(LinkedHashSet::new));
                matchedRemarks.addAll(batchMatched);
            }
            log.debug("关键词'{}'在remark中匹配到{}个", keyword, matchedRemarks.size());
            return matchedRemarks;
        } catch (Exception e) {
            log.error("knet sys_sku remark模糊匹配失败，关键词: {}", keyword, e);
            return new HashSet<>();
        }
    }
}
