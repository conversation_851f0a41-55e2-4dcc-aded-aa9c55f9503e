package com.knet.goods.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.goods.model.dto.third.req.KnetGroupGetInventoryReq;
import com.knet.goods.model.dto.third.req.KnetGroupGetNotOnSaleProductsReq;
import com.knet.goods.model.dto.third.req.KnetGroupGetPlatformListingReq;
import com.knet.goods.model.dto.third.req.KnetGroupGetSkuSizePlatformPriceReq;
import com.knet.goods.model.dto.third.resp.KnetInventoryDataVo;
import com.knet.goods.model.dto.third.resp.KnetNotOnSaleProductVo;
import com.knet.goods.model.dto.third.resp.KnetPlatformListingQueryVo;
import com.knet.goods.model.dto.third.resp.KnetProductMarketDataVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 11:11
 * @description: 第三方API调用服务 服务定义
 */
public interface IThirdApiService {

    /**
     * 从kg 获取商品市场数据
     *
     * @param request 请求体
     * @return 商品市场数据
     */
    List<KnetProductMarketDataVo> getProductMarketData(KnetGroupGetSkuSizePlatformPriceReq request);


    /**
     * 获取 kg 热门 sku 排行
     *
     * @return kg 热门 sku 排行
     */
    List<KnetProductMarketDataVo> getHotSkuRankData();

    /**
     * 从kg 获取商品库存数据
     *
     * @param request 请求体
     * @return 商品库存数据
     */
    List<KnetInventoryDataVo> getInventoryData(KnetGroupGetInventoryReq request);

    /**
     * 从kg 获取平台寄售单列表
     *
     * @param request 请求体
     * @return 平台寄售单列表
     */
    IPage<KnetPlatformListingQueryVo> queryPlatformListings(KnetGroupGetPlatformListingReq request);

    /**
     * 获取已下架商品列表
     *
     * @param request 请求体
     * @return 已下架商品列表
     */
    IPage<KnetNotOnSaleProductVo> getNotOnSaleProducts(KnetGroupGetNotOnSaleProductsReq request);
}
