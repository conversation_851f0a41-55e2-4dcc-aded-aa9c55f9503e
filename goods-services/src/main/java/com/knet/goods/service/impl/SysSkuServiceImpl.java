package com.knet.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.goods.mapper.SysSkuMapper;
import com.knet.goods.model.dto.req.SkuQueryRequest;
import com.knet.goods.model.entity.SysSku;
import com.knet.goods.service.ISysSkuService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.knet.common.constants.UserServicesConstants.KNET_SYS_SKU_CACHE_KEY_PREFIX;
import static com.knet.common.constants.UserServicesConstants.KNET_SYS_SKU_EXPIRED_TIME;

/**
 * <AUTHOR>
 * @date 2025/2/19 13:48
 * @description: SysSku service 实现类
 */
@Service
public class SysSkuServiceImpl extends ServiceImpl<SysSkuMapper, SysSku> implements ISysSkuService {

    @Resource
    private SysSkuMapper baseMapper;

    @Override
    public IPage<SysSku> listSku(SkuQueryRequest request) {
        QueryWrapper<SysSku> queryWrapper = new QueryWrapper<>();
        Page<SysSku> page = new Page<>(request.getQueryStartPage(), request.getPageSize());
        page = baseMapper.selectPage(page, queryWrapper);
        IPage<SysSku> userPage = baseMapper.selectPage(page, queryWrapper);
//        return userPage.convert(SysSku::mapToUserInfoDtoResp);
        return userPage;
    }

    @Override
    public List<String> queryBrands() {
        LambdaQueryWrapper<SysSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .select(SysSku::getBrand)
                .isNotNull(SysSku::getBrand)
                .groupBy(SysSku::getBrand);
        List<SysSku> sysSkus = baseMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(sysSkus)) {
            return sysSkus.stream()
                    .map(SysSku::getBrand)
                    .filter(StrUtil::isNotBlank)
                    .toList();
        }
        return Collections.emptyList();
    }

    @Override
    public Map<String, String> getSkuRemarksMap(String sku) {
        if (StrUtil.isBlank(sku)) {
            return Collections.emptyMap();
        }
        String redisSysSkuKey = String.format(KNET_SYS_SKU_CACHE_KEY_PREFIX, sku);
        Map<Object, Object> skuMap = RedisCacheUtil.hmget(redisSysSkuKey);
        if (MapUtil.isEmpty(skuMap)) {
            LambdaQueryWrapper<SysSku> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    .select(SysSku::getImg, SysSku::getRemarks)
                    .eq(SysSku::getSku, sku)
                    .isNotNull(SysSku::getImg)
                    .isNotNull(SysSku::getRemarks)
                    .orderByDesc(SysSku::getGmtModify)
                    .last("LIMIT 1");
            SysSku sysSku = baseMapper.selectOne(queryWrapper);
            if (BeanUtil.isEmpty(sysSku)) {
                return Collections.emptyMap();
            }
            Map<String, Object> cacheMap = new HashMap<>(4);
            String skuImgUrl = StrUtil.emptyToDefault(sysSku.getImg(), "");
            String skuRemarks = StrUtil.emptyToDefault(sysSku.getRemarks(), "");
            cacheMap.put("imgUrl", skuImgUrl);
            cacheMap.put("remarks", skuRemarks);
            RedisCacheUtil.hmset(redisSysSkuKey, cacheMap, KNET_SYS_SKU_EXPIRED_TIME);
            Map<String, String> resultMap = new HashMap<>(2);
            resultMap.put("imgUrl", skuImgUrl);
            resultMap.put("remarks", skuRemarks);
            return resultMap;
        }
        Map<String, String> resultMap = new HashMap<>(2);
        resultMap.put("imgUrl", StrUtil.emptyToDefault((String) skuMap.get("imgUrl"), ""));
        resultMap.put("remarks", StrUtil.emptyToDefault((String) skuMap.get("remarks"), ""));
        return resultMap;
    }

    @Override
    public List<String> selectAllDistinctSkus() {
        return baseMapper.selectAllDistinctSkus();
    }

    @Override
    public Map<String, Map<String, Object>> selectAllDistinctSkuIndexedMapping() {
        return baseMapper.selectAllDistinctSkuIndexedMapping();
    }

    @Override
    public List<String> selectAllDistinctRemarks() {
        return baseMapper.selectAllDistinctRemarks();
    }

    @Override
    public List<String> selectAllDistinctSysSkuRemarks() {
        return baseMapper.selectAllDistinctSysSkuRemarks();
    }

    @Override
    public Map<String, Map<String, String>> getBatchSkuRemarksMap(List<String> skus) {
        if (CollUtil.isEmpty(skus)) {
            return Collections.emptyMap();
        }
        Map<String, Map<String, String>> result = new HashMap<>(skus.size());
        List<String> missedSkus = new ArrayList<>();
        // 1. 批量从Redis获取缓存数据
        for (String sku : skus) {
            if (StrUtil.isBlank(sku)) {
                continue;
            }
            String redisSysSkuKey = String.format(KNET_SYS_SKU_CACHE_KEY_PREFIX, sku);
            Map<Object, Object> skuMap = RedisCacheUtil.hmget(redisSysSkuKey);
            if (MapUtil.isNotEmpty(skuMap)) {
                Map<String, String> skuData = new HashMap<>(2);
                skuData.put("imgUrl", StrUtil.emptyToDefault((String) skuMap.get("imgUrl"), ""));
                skuData.put("remarks", StrUtil.emptyToDefault((String) skuMap.get("remarks"), ""));
                result.put(sku, skuData);
            } else {
                // 缓存未命中，记录需要查询数据库的SKU
                missedSkus.add(sku);
            }
        }
        // 2. 批量从数据库查询缓存未命中的SKU
        if (CollUtil.isNotEmpty(missedSkus)) {
            LambdaQueryWrapper<SysSku> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    .select(SysSku::getSku, SysSku::getImg, SysSku::getRemarks, SysSku::getGmtModify)
                    .in(SysSku::getSku, missedSkus)
                    .isNotNull(SysSku::getImg)
                    .isNotNull(SysSku::getRemarks)
                    .orderByDesc(SysSku::getGmtModify);
            List<SysSku> sysSkus = baseMapper.selectList(queryWrapper);
            // 3. 处理数据库查询结果，对于重复的sku只取最新的一条
            Map<String, SysSku> latestSkuMap = new LinkedHashMap<>();
            for (SysSku sysSku : sysSkus) {
                String sku = sysSku.getSku();
                // 由于已经按gmt_modify倒序排列，第一次遇到的就是最新的
                if (!latestSkuMap.containsKey(sku)) {
                    latestSkuMap.put(sku, sysSku);
                }
            }
            // 4. 构建返回结果并更新缓存
            for (Map.Entry<String, SysSku> entry : latestSkuMap.entrySet()) {
                String sku = entry.getKey();
                SysSku sysSku = entry.getValue();
                String skuImgUrl = StrUtil.emptyToDefault(sysSku.getImg(), "");
                String skuRemarks = StrUtil.emptyToDefault(sysSku.getRemarks(), "");
                Map<String, String> skuData = new HashMap<>(2);
                skuData.put("imgUrl", skuImgUrl);
                skuData.put("remarks", skuRemarks);
                result.put(sku, skuData);
                String redisSysSkuKey = String.format(KNET_SYS_SKU_CACHE_KEY_PREFIX, sku);
                Map<String, Object> cacheMap = new HashMap<>(4);
                cacheMap.put("imgUrl", skuImgUrl);
                cacheMap.put("remarks", skuRemarks);
                RedisCacheUtil.hmset(redisSysSkuKey, cacheMap, KNET_SYS_SKU_EXPIRED_TIME);
            }
            // 5. 对于数据库中也没有的SKU，设置空值避免重复查询
            for (String missedSku : missedSkus) {
                if (!result.containsKey(missedSku)) {
                    result.put(missedSku, Collections.emptyMap());
                }
            }
        }
        return result;
    }
}
