package com.knet.goods.system.schedule;

import com.knet.goods.service.ISkuCacheService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025/7/7 11:24
 * @description: sku缓存任务
 */
@Slf4j
@Component
public class BaseCacheJob {

    @Resource
    private ISkuCacheService skuCacheService;

    /**
     * 定时刷新SKU缓存
     * 每12小时执行一次，刷新Redis中的SKU缓存数据
     */
    @XxlJob("refreshSkuCache")
    public ReturnT<String> refreshSkuCache(String param) {
        log.info("任务 refreshSkuCache 开始执行");
        String time = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        XxlJobHelper.log("XXL-JOB SKU缓存刷新任务触发时间: {}", time);
        StopWatch stopWatch = new StopWatch("refreshSkuCache");
        stopWatch.start();
        try {
            // 执行SKU缓存刷新
            skuCacheService.refreshSkuCache();
            stopWatch.stop();
            long totalTimeMillis = stopWatch.getTotalTimeMillis();
            log.info("SKU缓存刷新任务执行完成，耗时: {}ms", totalTimeMillis);
            XxlJobHelper.log("SKU缓存刷新任务执行完成，耗时: {}ms", totalTimeMillis);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            stopWatch.stop();
            log.error("SKU缓存刷新任务执行失败", e);
            XxlJobHelper.log("SKU缓存刷新任务执行失败: {}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, "SKU缓存刷新失败: " + e.getMessage());
        }
    }

    /**
     * 定时刷新knet product remarks缓存
     * 每12小时执行一次，刷新Redis中的remarks缓存数据
     */
    @XxlJob("refreshRemarksCache")
    public ReturnT<String> refreshRemarksCache(String param) {
        log.info("任务 refreshRemarksCache 开始执行");
        String time = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        XxlJobHelper.log("XXL-JOB remarks缓存刷新任务触发时间: {}", time);
        StopWatch stopWatch = new StopWatch("refreshRemarksCache");
        stopWatch.start();
        try {
            skuCacheService.refreshProductRemarkCache();
            stopWatch.stop();
            long totalTimeMillis = stopWatch.getTotalTimeMillis();
            log.info("REMARKS缓存刷新任务执行完成，耗时: {}ms", totalTimeMillis);
            XxlJobHelper.log("REMARKS缓存刷新任务执行完成，耗时: {}ms", totalTimeMillis);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            stopWatch.stop();
            log.error("REMARKS缓存刷新任务执行失败", e);
            XxlJobHelper.log("REMARKS缓存刷新任务执行失败: {}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, "REMARKS缓存刷新失败: " + e.getMessage());
        }
    }

    /**
     * 定时刷新knet product remarks缓存
     * 每12小时执行一次，刷新Redis中的remarks缓存数据
     */
    @XxlJob("refreshSkuRemarksCache")
    public ReturnT<String> refreshSkuRemarksCache(String param) {
        log.info("任务 refreshSkuRemarksCache 开始执行");
        String time = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        XxlJobHelper.log("XXL-JOB SYS_SKU remarks缓存刷新任务触发时间: {}", time);
        StopWatch stopWatch = new StopWatch("refreshSkuRemarksCache");
        stopWatch.start();
        try {
            skuCacheService.refreshSysSkuRemarkCache();
            stopWatch.stop();
            long totalTimeMillis = stopWatch.getTotalTimeMillis();
            log.info("SYS_SKU REMARKS缓存刷新任务执行完成，耗时: {}ms", totalTimeMillis);
            XxlJobHelper.log("SYS_SKU REMARKS缓存刷新任务执行完成，耗时: {}ms", totalTimeMillis);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            stopWatch.stop();
            log.error("SYS_SKU REMARKS缓存刷新任务执行失败", e);
            XxlJobHelper.log("SYS_SKU REMARKS缓存刷新任务执行失败: {}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, "SYS_SKU REMARKS缓存刷新失败: " + e.getMessage());
        }
    }
}
