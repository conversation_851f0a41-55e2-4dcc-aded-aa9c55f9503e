package com.knet.goods.system.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.annotation.DistributedLock;
import com.knet.goods.model.dto.third.req.KnetGroupGetNotOnSaleProductsReq;
import com.knet.goods.model.dto.third.resp.KnetNotOnSaleProductVo;
import com.knet.goods.service.IKnetProductOperationService;
import com.knet.goods.service.IKnetProductService;
import com.knet.goods.service.IThirdApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/9/8 10:30
 * @description: KG_SYS_PROD系统下架商品同步任务
 */
@Slf4j
@Component
public class KgSysProdDataOffSaleProductSyncJob {

    @Resource
    private IThirdApiService thirdApiService;
    @Resource
    private IKnetProductService knetProductService;
    @Resource
    private IKnetProductOperationService knetProductOperationService;

    /**
     * 同步KG系统下架商品状态
     * 通过调用KG接口获取已下架商品列表，将本地对应商品状态更新为下架
     * 处理大数据量（200万+）商品的下架同步
     * 使用分布式锁确保多实例环境下只有一个实例执行
     */
    @XxlJob("syncKgSysProdOffSaleProducts")
    @DistributedLock(key = "sync_kg_sys_prod_off_sale_products", expire = 7200)
    public ReturnT<String> syncKgSysProdOffSaleProducts() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("同步KG_SYS_PROD系统下架商品状态");
        try {
            JobParams params = new JobParams();
            log.info("开始执行KG_SYS_PROD下架商品同步任务，参数配置: pageSize={}, maxPages={}, batchSize={}",
                    params.pageSize, params.maxPages, params.batchSize);
            XxlJobHelper.log("开始执行KG_SYS_PROD下架商品同步任务，参数配置: pageSize={}, maxPages={}, batchSize={}",
                    params.pageSize, params.maxPages, params.batchSize);
            // 1. 查询本地所有上架状态的商品oneId
            Set<String> localOnSaleOneIds = knetProductService.getLocalOnSaleOneIds();
            log.info("本地上架状态商品共 {} 个", localOnSaleOneIds.size());
            XxlJobHelper.log("本地上架状态商品共 {} 个", localOnSaleOneIds.size());
            if (CollUtil.isEmpty(localOnSaleOneIds)) {
                String message = "本地没有上架状态的商品";
                log.info(message);
                XxlJobHelper.log(message);
                return ReturnT.SUCCESS;
            }
            // 2. 使用内存优化的分批处理方式，避免200万数据全部加载到内存
            int totalUpdated = processThirdPartyDataInBatches(localOnSaleOneIds, params);
            stopWatch.stop();
            String successMessage = String.format("KG_SYS_PROD下架商品同步任务执行完成，耗时: %d ms，成功下架 %d 个商品",
                    stopWatch.getTotalTimeMillis(), totalUpdated);
            log.info(successMessage);
            XxlJobHelper.log(successMessage);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            stopWatch.stop();
            String errorMessage = String.format("KG_SYS_PROD下架商品同步任务执行失败，耗时: %d ms，错误信息: %s",
                    stopWatch.getTotalTimeMillis(), e.getMessage());
            log.error(errorMessage, e);
            XxlJobHelper.log(errorMessage);
            return new ReturnT<>(ReturnT.FAIL_CODE, errorMessage);
        }
    }
    
    /**
     * 分批处理下架操作
     *
     * @param oneIdsToOffSale 需要下架的oneId集合
     * @param batchSize       批处理大小
     * @return 总更新数量
     */
    private int processBatchOffSale(Set<String> oneIdsToOffSale, int batchSize) {
        List<String> oneIdsList = new ArrayList<>(oneIdsToOffSale);
        int totalUpdated = 0;
        int totalBatches = (oneIdsList.size() + batchSize - 1) / batchSize;
        log.info("开始分批下架操作，总数量: {}，批大小: {}，总批次: {}", oneIdsList.size(), batchSize, totalBatches);
        XxlJobHelper.log("开始分批下架操作，总数量: {}，批大小: {}，总批次: {}", oneIdsList.size(), batchSize, totalBatches);
        for (int i = 0; i < oneIdsList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, oneIdsList.size());
            List<String> batchOneIds = oneIdsList.subList(i, endIndex);
            int currentBatch = (i / batchSize) + 1;
            try {
                log.info("执行第 {}/{} 批下架操作，本批数量: {}", currentBatch, totalBatches, batchOneIds.size());
                XxlJobHelper.log("执行第 {}/{} 批下架操作，本批数量: {}", currentBatch, totalBatches, batchOneIds.size());
                int batchUpdated = knetProductOperationService.updateExistingProductsToOffSale(batchOneIds);
                totalUpdated += batchUpdated;
                log.info("第 {}/{} 批下架完成，本批更新: {} 个，累计更新: {} 个",
                        currentBatch, totalBatches, batchUpdated, totalUpdated);
                XxlJobHelper.log("第 {}/{} 批下架完成，本批更新: {} 个，累计更新: {} 个",
                        currentBatch, totalBatches, batchUpdated, totalUpdated);
                // 批次间添加短暂延迟，避免数据库压力过大
                if (currentBatch < totalBatches) {
                    Thread.sleep(50);
                }
            } catch (Exception e) {
                log.error("第 {}/{} 批下架操作失败: {}", currentBatch, totalBatches, e.getMessage(), e);
                XxlJobHelper.log("第 {}/{} 批下架操作失败: {}", currentBatch, totalBatches, e.getMessage());
                // 继续处理下一批，不中断整个任务
            }
        }
        return totalUpdated;
    }

    /**
     * 内存优化的分批处理方法
     * 避免将200万数据全部加载到内存中
     *
     * @param localOnSaleOneIds 本地上架商品ID集合
     * @param params            任务参数
     * @return 总更新数量
     */
    private int processThirdPartyDataInBatches(Set<String> localOnSaleOneIds, JobParams params) {
        int totalUpdated = 0;
        Set<String> currentBatchOffSaleIds = new HashSet<>();
        int processedPages = 0;
        int currentPage = 1;
        log.info("开始内存优化的分批处理，本地上架商品数: {}, 内存批大小: {}",
                localOnSaleOneIds.size(), params.memoryBatchSize);
        XxlJobHelper.log("开始内存优化的分批处理，本地上架商品数: {}, 内存批大小: {}", localOnSaleOneIds.size(), params.memoryBatchSize);
        while (currentPage <= params.maxPages) {
            try {
                // 获取一页KG_SYS_PROD数据
                KnetGroupGetNotOnSaleProductsReq req = KnetGroupGetNotOnSaleProductsReq.createDefaultReq(currentPage, params.pageSize);
                IPage<KnetNotOnSaleProductVo> pageResult = thirdApiService.getNotOnSaleProducts(req);
                if (pageResult == null || CollUtil.isEmpty(pageResult.getRecords())) {
                    log.info("第 {} 页没有数据，结束处理", currentPage);
                    break;
                }
                // 提取当前页的oneId
                Set<String> pageOneIds = pageResult.getRecords()
                        .stream()
                        .map(KnetNotOnSaleProductVo::getOneId)
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.toSet());
                currentBatchOffSaleIds.addAll(pageOneIds);
                processedPages++;
                log.info("处理第 {} 页，获得 {} 个oneId，当前批累计: {} 个", currentPage, pageOneIds.size(), currentBatchOffSaleIds.size());
                // 当累积数据达到内存批大小或者是最后一页时，进行处理
                boolean isLastPage = currentPage >= pageResult.getPages() || currentPage >= params.maxPages;
                if (currentBatchOffSaleIds.size() >= params.memoryBatchSize || isLastPage) {
                    // 找出需要下架的商品
                    Set<String> batchToOffSale = new HashSet<>(localOnSaleOneIds);
                    batchToOffSale.retainAll(currentBatchOffSaleIds);
                    if (CollUtil.isNotEmpty(batchToOffSale)) {
                        log.info("内存批处理: 发现 {} 个商品需要下架", batchToOffSale.size());
                        XxlJobHelper.log("内存批处理: 发现 {} 个商品需要下架", batchToOffSale.size());
                        // 分批执行下架操作
                        int batchUpdated = processBatchOffSale(batchToOffSale, params.batchSize);
                        totalUpdated += batchUpdated;
                        log.info("内存批处理完成，本批更新: {} 个，累计更新: {} 个", batchUpdated, totalUpdated);
                        XxlJobHelper.log("内存批处理完成，本批更新: {} 个，累计更新: {} 个", batchUpdated, totalUpdated);
                    }
                    // 清空当前批数据，释放内存
                    currentBatchOffSaleIds.clear();
                    // 强制垃圾回收，释放内存
                    System.gc();
                }
                if (isLastPage) {
                    break;
                }
                currentPage++;
                Thread.sleep(params.requestDelay);
            } catch (Exception e) {
                log.error("处理第 {} 页数据失败: {}", currentPage, e.getMessage(), e);
                XxlJobHelper.log("处理第 {} 页数据失败: {}", currentPage, e.getMessage());
                break;
            }
        }

        log.info("内存优化分批处理完成，共处理 {} 页，总更新数量: {}", processedPages, totalUpdated);
        XxlJobHelper.log("内存优化分批处理完成，共处理 {} 页，总更新数量: {}", processedPages, totalUpdated);
        return totalUpdated;
    }

    /**
     * 任务参数配置类 - 针对200万数据优化
     */
    private static class JobParams {
        /**
         * 每页数据量，针对200万数据优化，设置为2000
         */
        int pageSize = 2000;

        /**
         * 最大处理页数，默认1500，防止无限循环（200万/2000=1000页）
         */
        int maxPages = 1500;

        /**
         * 批处理大小，下架操作分批执行，避免单次操作数据量过大
         */
        int batchSize = 1000;

        /**
         * 请求延迟（毫秒），避免对第三方系统造成压力
         */
        long requestDelay = 30L;

        /**
         * 内存分批处理大小，避免Set集合占用过多内存
         * 每批处理10万条数据，约占用50MB内存
         */
        int memoryBatchSize = 100000;
    }
}
