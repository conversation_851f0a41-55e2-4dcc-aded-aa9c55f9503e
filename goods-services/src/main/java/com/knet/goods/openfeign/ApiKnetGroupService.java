package com.knet.goods.openfeign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knet.common.base.HttpResult;
import com.knet.common.constants.SystemConstant;
import com.knet.goods.model.dto.third.req.KnetGroupGetInventoryReq;
import com.knet.goods.model.dto.third.req.KnetGroupGetNotOnSaleProductsReq;
import com.knet.goods.model.dto.third.req.KnetGroupGetPlatformListingReq;
import com.knet.goods.model.dto.third.req.KnetGroupGetSkuSizePlatformPriceReq;
import com.knet.goods.model.dto.third.resp.KnetInventoryDataVo;
import com.knet.goods.model.dto.third.resp.KnetNotOnSaleProductVo;
import com.knet.goods.model.dto.third.resp.KnetPlatformListingQueryVo;
import com.knet.goods.model.dto.third.resp.KnetProductMarketDataVo;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 11:02
 * @description: kj 服务提供API
 */
@RefreshScope
@FeignClient(name = "knetGroup-services", url = "${feign.client.config.knetGroupServices.url}")
public interface ApiKnetGroupService {

    /**
     * 从kg 获取商品市场数据
     *
     * @param token 自定义请求头 token
     * @param req   请求体
     * @return 商品市场数据
     * @see KnetGroupGetSkuSizePlatformPriceReq
     */
    @PostMapping("/api/system/get_prod_market_data")
    HttpResult<List<KnetProductMarketDataVo>> getProductMarketData(@RequestHeader(SystemConstant.TOKEN) String token, @RequestBody KnetGroupGetSkuSizePlatformPriceReq req);

    /**
     * 获取 kg 热门 sku 排行
     *
     * @param token 自定义请求头 token
     * @return kg 热门 sku 排行
     */
    @GetMapping(value = "/api/system/get_hot_sku", consumes = MediaType.APPLICATION_JSON_VALUE)
    HttpResult<List<KnetProductMarketDataVo>> getHotSkuRankData(@RequestHeader(value = SystemConstant.TOKEN, required = false) String token);

    /**
     * 从kg 获取商品库存数据
     *
     * @param token 自定义请求头 token
     * @param req   请求体
     * @return 商品库存数据
     */
    @PostMapping("/api/system/get_inventory_data")
    HttpResult<List<KnetInventoryDataVo>> getInventoryData(@RequestHeader(SystemConstant.TOKEN) String token, @RequestBody KnetGroupGetInventoryReq req);

    /**
     * 从kg 获取平台寄售单列表
     *
     * @param token token
     * @param req   请求
     * @return 平台寄售单列表
     * @see KnetGroupGetPlatformListingReq
     */
    @PostMapping("/api/system/query_platform_listings")
    HttpResult<Page<KnetPlatformListingQueryVo>> queryPlatformListings(@RequestHeader(SystemConstant.TOKEN) String token, @RequestBody KnetGroupGetPlatformListingReq req);

    /**
     * 获取已下架商品列表
     *
     * @param token token
     * @param req   请求
     * @return 已下架商品列表
     * @see KnetGroupGetNotOnSaleProductsReq
     */
    @PostMapping("/api/system/get_not_on_sale_products")
    HttpResult<Page<KnetNotOnSaleProductVo>> getNotOnSaleProducts(@RequestHeader(SystemConstant.TOKEN) String token, @RequestBody KnetGroupGetNotOnSaleProductsReq req);
}
