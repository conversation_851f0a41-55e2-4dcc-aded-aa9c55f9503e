package com.knet.goods.model.dto.third.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/9/8 10:00
 * @description: 已下架商品信息响应体
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnetNotOnSaleProductVo extends BaseResponse {
    
    @Schema(description = "商品oneId")
    private String oneId;
}
