package com.knet.goods.model.dto.third.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/9/8 10:00
 * @description: 获取已下架商品列表请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KnetGroupGetNotOnSaleProductsReq extends BaseRequest {
    
    @Schema(description = "页码，从1开始")
    private Integer pageNum = 1;

    @Schema(description = "每页数据量")
    private Integer pageSize = 20;

    /**
     * 创建默认请求对象
     *
     * @param pageNum  页码
     * @param pageSize 每页数据量
     * @return 请求对象
     */
    public static KnetGroupGetNotOnSaleProductsReq createDefaultReq(Integer pageNum, Integer pageSize) {
        KnetGroupGetNotOnSaleProductsReq req = new KnetGroupGetNotOnSaleProductsReq();
        req.setPageNum(pageNum);
        req.setPageSize(pageSize);
        return req;
    }
}
