# 第三方下架商品同步任务实现总结

## 实现概述

创建了一个全新的独立定时任务类 `ThirdPartyOffSaleProductSyncJob`，包含任务方法 `syncThirdPartyOffSaleProducts`，专门用于同步第三方系统的商品下架状态。该任务针对大数据量（100万+商品）进行了专门优化，通过调用第三方接口 `http://localhost:7570/api/system/get_not_on_sale_products` 获取已下架商品列表，并将本地对应的上架商品更新为下架状态。

## 文件修改清单

### 1. 新增文件

#### 定时任务类
- `goods-services/src/main/java/com/knet/goods/system/schedule/ThirdPartyOffSaleProductSyncJob.java`
  - 全新的独立定时任务类
  - 包含 `syncThirdPartyOffSaleProducts` 任务方法
  - 针对大数据量优化的参数配置
  - 完整的分页获取和分批处理逻辑

#### DTO 类
- `goods-services/src/main/java/com/knet/goods/model/dto/third/req/KnetGroupGetNotOnSaleProductsReq.java`
  - 第三方接口请求参数DTO
  - 包含 pageNum 和 pageSize 字段
  - 提供静态工厂方法 createDefaultReq

- `goods-services/src/main/java/com/knet/goods/model/dto/third/resp/KnetNotOnSaleProductVo.java`
  - 第三方接口响应数据DTO
  - 包含 oneId 字段

#### 文档
- `goods-services/docs/sync-offsale-products-job.md`
  - 详细的功能说明文档
- `goods-services/docs/implementation-summary.md`
  - 实现总结文档

### 2. 修改文件

#### OpenFeign 接口
- `goods-services/src/main/java/com/knet/goods/openfeign/ApiKnetGroupService.java`
  - 新增导入: `KnetGroupGetNotOnSaleProductsReq`, `KnetNotOnSaleProductVo`
  - 新增方法: `getNotOnSaleProducts(@RequestHeader String token, @RequestBody KnetGroupGetNotOnSaleProductsReq req)`

#### 服务接口
- `goods-services/src/main/java/com/knet/goods/service/IThirdApiService.java`
  - 新增导入: `KnetGroupGetNotOnSaleProductsReq`, `KnetNotOnSaleProductVo`
  - 新增方法: `IPage<KnetNotOnSaleProductVo> getNotOnSaleProducts(KnetGroupGetNotOnSaleProductsReq request)`

#### 服务实现
- `goods-services/src/main/java/com/knet/goods/service/impl/ThirdApiServiceImpl.java`
  - 新增导入: `KnetGroupGetNotOnSaleProductsReq`, `KnetNotOnSaleProductVo`
  - 新增方法实现: `getNotOnSaleProducts` 方法，包含完整的异常处理和日志记录

#### 定时任务
- 无修改现有定时任务类，创建了全新的独立任务类

## 核心实现逻辑

### 1. 数据获取流程
```
1. 分页调用第三方接口获取已下架商品列表
2. 提取所有有效的 oneId
3. 查询本地所有上架状态的商品 oneId
4. 计算交集，找出需要下架的商品
5. 批量执行下架操作
```

### 2. 大数据量性能优化措施
- **大分页处理**: 每页1000条，减少请求次数，避免内存溢出
- **集合运算**: 使用 Set.retainAll() 高效计算交集
- **分批更新**: 500条/批分批更新，避免单次操作数据量过大
- **请求限流**: 页面间添加50ms延迟，保护第三方系统
- **批次延迟**: 批次间50ms延迟，避免数据库压力过大
- **最大页数限制**: 防止无限循环，默认最大2000页
- **异常容错**: 单批失败不影响其他批次继续执行

### 3. 异常处理
- 网络异常时记录错误并终止任务
- 第三方服务不可用时返回失败状态
- 详细的日志记录，便于问题排查

## 配置参数

### JobParams 参数（针对大数据量优化）
- `pageSize`: 1000 (大分页，减少请求次数)
- `maxPages`: 2000 (防止无限循环)
- `batchSize`: 500 (分批下架，避免单次操作过大)
- `requestDelay`: 50ms (请求间延迟)

### 第三方接口
- URL: `http://localhost:7570/api/system/get_not_on_sale_products`
- Method: POST
- 请求延迟: 50ms
- 批次延迟: 50ms

## 使用方式

### XXL-Job 配置
1. 任务名称: `syncThirdPartyOffSaleProducts`
2. 执行器: 选择对应的执行器
3. 调度配置: 建议每2-4小时执行一次（考虑到大数据量处理时间）

### 监控指标
- 第三方系统已下架商品总数
- 本地上架商品数量
- 实际下架商品数量
- 任务执行耗时
- 成功/失败状态

## 代码特点

1. **高度复用**: 参考现有 `syncListingFromKg` 任务的实现模式
2. **完整日志**: 详细的执行过程日志，便于监控和调试
3. **异常安全**: 完善的异常处理机制
4. **性能优化**: 分页处理和批量操作
5. **配置灵活**: 通过内部参数类管理配置

## 注意事项

1. 确保第三方接口 `http://localhost:7570/api/system/get_not_on_sale_products` 可用
2. 任务执行频率不宜过高，避免对第三方系统造成压力
3. 监控任务执行日志，及时发现和处理异常
4. 大数据量处理时注意内存使用情况
