# KNET 微服务电商平台

## 项目简介

KNET是一个基于Spring Cloud微服务架构的现代化B2B电商平台，采用领域驱动设计(DDD)
思想，支持高并发、高可用的分布式商业场景。平台涵盖商品管理、用户管理、订单处理、支付结算、通知推送等完整的电商业务链路。

## 项目基础架构

| 层级      | 技术/框架                           | 说明                   |
|---------|:--------------------------------|----------------------|
| 微服务框架   | Spring Cloud 2021.0.5           | 微服务基础框架              |
| 微服务组件   | Spring Cloud Alibaba 2021.0.5.0 | 阿里巴巴微服务组件            |
| 应用框架    | Spring Boot 2.7.18              | 应用开发框架               |
| JDK     | JDK 17 （OpenJDK）                | Java开发环境             |
| 服务注册与配置 | Nacos 2.2.0                     | 服务注册、发现与配置中心         |
| 服务网关    | Spring Cloud Gateway            | API网关                |
| 服务调用    | OpenFeign                       | 声明式REST客户端           |
| 负载均衡    | Spring Cloud LoadBalancer       | 客户端负载均衡              |
| 数据访问    | MyBatis-Plus 3.5.12             | ORM框架                |
| 数据库连接池  | HikariCP                        | 数据库连接池               |
| 数据库组件   | mybatis.spring  2.3.0           | 数据库组件                |
| 数据库     | MySQL 8                         | 关系型数据库               |
| 缓存      | Redis   6.0                     | 分布式缓存                |
| 消息队列    | RabbitMQ 3.12.6                 | 消息中间件                |
| 任务调度    | XXL-Job 3.1.1                   | 分布式任务调度平台            |
| 分布式锁    | Redis + AOP                     | 基于Redis的分布式锁实现       |
| 限流控制    | Redis + AOP                     | 基于Redis的令牌桶算法实现      |
| 权限控制    | JWT + AOP                       | 基于JWT的权限控制           |
| 日志记录    | AOP                             | 基于AOP的日志记录           |
| 容器化     | Docker                          | 应用容器化                |
| 流量控制    | Spring Retry                    | 基于springRetry 应用内部重试 |


### 模块依赖关系图
```mermaid
flowchart TD
    subgraph app_layer ["应用层"]
        gateway["网关模块<br/>gateway"]
        user_svc["用户服务<br/>user-services"]
        goods_svc["商品服务<br/>goods-services"]
        order_svc["订单服务<br/>order-services"]
        payment_svc["支付服务<br/>payment-services"]
        oauth_svc["授权服务<br/>oauth-services"]
        notification_svc["通知服务<br/>notification-services"]
        delayed_svc["延迟服务<br/>delayed-services"]
        export-svc["导出服务<br/>export-services"]
    end
    subgraph base_layer ["基础层"]
        common["公共模块<br/>common<br/>(工具类、配置、AOP)"]
    end

%% 所有服务依赖common
    gateway --> common
    user_svc --> common
    goods_svc --> common
    order_svc --> common
    payment_svc --> common
    oauth_svc --> common
    notification_svc --> common
    delayed_svc --> common
    export-svc --> common
%% 服务间调用关系 (虚线表示业务调用)
    order_svc -.-> goods_svc
    order_svc -.-> payment_svc
    order_svc -.-> user_svc
    payment_svc -.-> notification_svc
    order_svc -.-> delayed_svc
    order_svc --> export-svc
    goods_svc --> export-svc
    user_svc --> export-svc
%% 网关路由关系
    gateway --> user_svc
    gateway --> goods_svc
    gateway --> order_svc
    gateway --> payment_svc
%% 样式定义
    classDef app_style fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef base_style fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class gateway,user_svc,goods_svc,order_svc,payment_svc,oauth_svc,notification_svc,delayed_svc,export-svc app_style
    class common base_style
```

### 模块详细说明

```lua
knet
├── gateway           -- 网关模块，负责路由转发和权限校验[7000]
│   ├── 统一入口管理
│   ├── JWT令牌验证
│   ├── 请求路由分发
│   └── 跨域处理
├── common            -- 项目公共模块[基础库]
│   ├── 通用工具类
│   ├── 统一返回格式
│   ├── 异常处理机制
│   ├── AOP切面实现
│   ├── 分布式锁组件
│   ├── 限流组件
│   └── 公共DTO定义
├── goods-services    -- 商品模块[7001]
│   ├── 商品信息管理
│   ├── 库存管理
│   ├── 价格策略管理
│   ├── 商品搜索
│   └── 库存锁定策略
├── user-services     -- 用户模块[7002]
│   ├── 用户信息管理
│   ├── 认证授权
│   ├── 购物车管理
│   ├── 用户权限控制
│   └── 收藏夹(PRD)
├── oauth-services    -- 授权服务[7003]
│   ├── 第三方登录集成
│   ├── OAuth2.0实现
│   ├── 令牌管理
│   └── 第三方API鉴权
├── order-services    -- 订单服务[7004]
│   ├── 订单管理(父子订单)
│   ├── 订单状态流转
│   ├── 物流信息管理
│   ├── 售后处理(PRD)
│   └── 分布式事务协调
├── payment-services  -- 支付服务[7005]
│   ├── 用户钱包管理
│   ├── 支付流水记录
│   ├── 第三方支付集成(PRD
│   ├── 退款处理
│   └── 资金对账(PRD)
├── notification-services  -- 通知中枢[7006]
│   ├── 消息推送统一入口
│   ├── 邮件通知
│   ├── 短信通知(PRD)
│   └── 推送模板管理
├── delayed-services  -- 延迟消息中枢[7007]
│    └── 延迟消息调度
├── exports-services  -- 导出服务[7008]
│   └── 异步文件导出系统，纯粹的执行引擎    
```

## 核心技术特性

### 分布式架构设计模式

#### 1. SAGA分布式事务模式

```java
// 基于消息队列实现最终一致性
@EventListener
public void handleOrderCreatedEvent(OrderCreatedEvent event) {
    try {
        // 处理业务逻辑
        processOrder(event.getOrderId());
    } catch (Exception e) {
        // 发送补偿事件
        publishCompensationEvent(event);
    }
}
```

#### 2. 策略模式实现

- **价格策略**: 支持多种定价策略动态切换
- **库存锁定策略**: 支持不同的库存管理策略
- **支付策略**: 支持多种支付方式

#### 3. 事件驱动架构

```java

@Component
public class OrderEventHandler {
    @RabbitListener(queues = "order.payment.success")
    public void handlePaymentSuccess(PaymentSuccessEvent event) {
        // 处理支付成功后的业务逻辑
    }
}
```

### AOP横切关注点

#### 分布式锁

```java

@DistributedLock(key = "'deleteUser:'+#id", expire = 2)
public void deleteUser(Long id) {
    // 业务逻辑
}
```

#### 限流控制

```java

@RateLimiter(key = "api:user:create", rate = 10, period = 60)
public ResultVo createUser(UserDto userDto) {
    // 业务逻辑
}
```

#### 权限控制

```java

@PermissionCheck(permission = "roler")
public ResultVo createUser(UserDto userDto) {
    // 业务逻辑
}
```

#### 日志记录

```java

@Loggable
public ResultVo getUserInfo(Long userId) {
    // 自动记录方法执行日志
}
```

## 技术栈详细说明

- **微服务框架**: 阿里巴巴微服务
    - **服务注册与配置中心**: Nacos 2.2.0
        - **地址**: [Nacos测试环境](http://192.168.6.5:8844/nacos/)
        - **账号**: `nacos`
        - **密码**: `nacos`
    - **服务间调用**: `OpenFeign`
    - **API网关**: `Spring Gateway`
        - 实现服务间的权限控制SSO（已实现），统一日志收集（暂时不需要）
    - **分布式锁**:  `@DistributedLock(key = "'deleteUser:'+#id", expire = 2)` 基于Redis setnx 实现
    - **流量控制**: `@RateLimiter` redis 实现令牌桶算法
    - **负载均衡**: 基于`loadbalancer`的实现,默认采用轮训策略，同一个服务的不同实例之间采用轮训策略
    - **事务控制**: 冗余设计，尽量将跨服务的事务控制在一个服务中，如果不可避免，采用MQ消息队列实现最终一致性（prd）
    - **权限控制**: `@PermissionCheck` 基于AOP实现
    - **日志记录**: `@Loggable` 基于AOP实现
    - **docker 支持**: 各个模块有dockerfile文件，可以直接构建镜像,`docker build -t xxx-services .`
    - **MQ消息队列**:  `rabbitMQ 3.12.6`（安装位置/app/rabbitmq-docker）`http://192.168.6.5:15672/#/ admin/admin`
    - **分布式事务控制**: 利用MQ消息队列实现最终一致性 SAGA+补偿机制
    - **定时任务框架**: `XXL-Job 3.1.1` `http://192.168.6.5:7777/xxl-job-admin/ admin/123456`
    - **接口文档** `SpringDoc API 3.0 + Apifox`
    - **请求重试** `Spring Retry` 应用内部重试，openFeign（应用实例层级 不重试、不熔断）
    - **延迟队列** `Redis Zset + xxl-job 定时任务` 支持自定义的延迟信息实现（背景aws上的rabbit_mq 不支持延迟信息插件，被迫实现）

## 预计实现的特性

- **多数据源**: `DataSource`实现主从读写分离
- **流量控制**: `Sentinel` 成熟可靠的方案，需要部署独立服务
- **链路追踪**: `SkyWalking` 零侵入，减少手动埋点的过程，负面效果比较耗费资源
- **分布式号段发布服务** 提取 RandomStrUtil中的方法实现
- **微服务管理** `k8s`
- **rocketMQ** `🚀当业务发展到一定数量时，启用🚀MQ`


### 服务间通信方式

| 通信类型 | 实现方式           | 使用场景       |
|------|----------------|------------|
| 同步调用 | OpenFeign      | 实时查询、数据校验  |
| 异步消息 | RabbitMQ       | 事件通知、最终一致性 |
| 配置共享 | Nacos Config   | 动态配置管理     |
| 服务发现 | Nacos Registry | 服务注册与发现    |
| 缓存共享 | Redis          | 会话状态、热点数据  |

### 数据一致性保证

#### 1. 强一致性

- 单服务内事务: `@Transactional`
- 数据库约束: 外键、唯一索引

#### 2. 最终一致性

- SAGA事务模式
- 事件驱动补偿机制
- 消息队列可靠性投递

#### 3. 数据隔离

- 每个微服务独立数据库
- 通过API进行数据访问
- 避免跨服务数据库连接

## 开发规范

### 代码组织规范

```
src/main/java/com/knet/{service-name}/
├── controller/      # 控制器层
│   └── api/        # 对外API接口
├── service/         # 业务逻辑层
│   └── impl/       # 实现类
├── mapper/          # 数据访问层
├── entity/          # 实体类
├── dto/             # 数据传输对象
├── config/          # 配置类
├── listener/        # 事件监听器
└── strategy/        # 策略模式实现
```

### 命名规范

- **类名**: PascalCase (如: UserService)
- **方法名**: camelCase (如: getUserInfo)
- **常量**: UPPER_SNAKE_CASE (如: MAX_RETRY_COUNT)
- **包名**: 全小写 (如: com.knet.user)

<a href="https://apifox.com/apidoc/shared-b65313ef-211f-47c2-8f75-cb929d7a4b30">📖 接口文档</a>

## 环境配置

### 开发环境依赖

| 服务       | 地址                                     | 账号/密码        | 说明        |
|----------|----------------------------------------|--------------|-----------|
| Nacos    | http://192.168.6.5:8844/nacos/         | nacos/nacos  | 服务注册与配置中心 |
| RabbitMQ | http://192.168.6.5:15672/              | admin/admin  | 消息队列管理    |
| XXL-Job  | http://192.168.6.5:7777/xxl-job-admin/ | admin/123456 | 分布式任务调度   |

### 部署说明

- **容器化**: 每个模块包含Dockerfile，支持Docker部署
- **构建命令**: `docker build -t xxx-services .`
- **健康检查**: 各服务提供actuator健康检查端点
- **日志收集**: 统一日志格式，支持ELK集成（prd）

## 注意事项

- **配置**: 本地启动需要更改 6.5 nacos地址
- **外部请求**: 项目中的所有外部请求统一走gateway，gateway会对请求进行权限校验
- **内部请求**: 公司内部系统调用，直接走服务器Ip+服务port即可，不需要走gateway
- **约定**: 项目禁用循环依赖
- **文档**: `doc/` 目录存在部分关键设计的详细文档