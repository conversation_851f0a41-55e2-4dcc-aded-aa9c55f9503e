-- 为订单表添加地址快照字段
-- 执行时间: 2024-09-19
-- 说明: 添加shipping_address_snapshot和billing_address_snapshot字段，用于保存地址快照避免用户修改地址后影响已有订单

-- 添加快递地址快照字段
ALTER TABLE sys_order_group
    ADD COLUMN shipping_address_snapshot JSON COMMENT '快递地址快照(JSON格式)，保存创建订单时的快递地址信息，避免用户修改地址后影响已有订单';

-- 添加账单地址快照字段
ALTER TABLE sys_order_group
    ADD COLUMN billing_address_snapshot JSON COMMENT '账单地址快照(JSON格式)，保存创建订单时的账单地址信息，避免用户修改地址后影响已有订单';

-- ==========================================
-- 历史数据同步脚本 - 补充地址快照数据
-- ==========================================

-- 1. 更新收货地址快照数据
-- 基于 sys_order_group.address_id 关联 sys_user_address 表获取地址信息
UPDATE sys_order_group sog
    INNER JOIN sys_user_address sua ON sog.address_id = sua.id
SET sog.shipping_address_snapshot = JSON_OBJECT(
        'id', sua.id,
        'userId', sua.user_id,
        'fullName', sua.full_name,
        'companyName', sua.company_name,
        'country', sua.country,
        'addressLine1', sua.address_line1,
        'addressLine2', sua.address_line2,
        'city', sua.city,
        'state', sua.state,
        'zipCode', sua.zip_code,
        'phonePrefix', sua.phone_prefix,
        'phoneNumber', sua.phone_number,
        'createTime', DATE_FORMAT(sua.create_time, '%Y-%m-%d %H:%i:%s'),
        'updateTime', DATE_FORMAT(sua.update_time, '%Y-%m-%d %H:%i:%s')
                                    )
WHERE sog.shipping_address_snapshot IS NULL
  AND sog.address_id IS NOT NULL
  AND sua.deleted = 0;

-- 2. 更新账单地址快照数据
-- 基于 sys_order_group.bill_address_id 关联 sys_user_invoice_address 表获取账单地址信息
UPDATE sys_order_group sog
    INNER JOIN sys_user_invoice_address suia ON sog.bill_address_id = suia.id
SET sog.billing_address_snapshot = JSON_OBJECT(
        'id', suia.id,
        'userId', suia.user_id,
        'fullName', suia.full_name,
        'country', suia.country,
        'addressLine1', suia.address_line1,
        'addressLine2', suia.address_line2,
        'city', suia.city,
        'state', suia.state,
        'zipCode', suia.zip_code,
        'phonePrefix', suia.phone_prefix,
        'phoneNumber', suia.phone_number,
        'email', suia.email,
        'isDefault', suia.is_default,
        'createTime', DATE_FORMAT(suia.create_time, '%Y-%m-%d %H:%i:%s'),
        'updateTime', DATE_FORMAT(suia.update_time, '%Y-%m-%d %H:%i:%s')
                                   )
WHERE sog.billing_address_snapshot IS NULL
  AND sog.bill_address_id IS NOT NULL
  AND suia.deleted = 0;

-- ==========================================
-- 数据检查和验证脚本
-- ==========================================

-- 检查收货地址快照更新情况
SELECT COUNT(*)                                                                                     as total_orders,
       COUNT(shipping_address_snapshot)                                                             as orders_with_shipping_snapshot,
       COUNT(billing_address_snapshot)                                                              as orders_with_billing_snapshot,
       COUNT(CASE
                 WHEN address_id IS NOT NULL AND shipping_address_snapshot IS NULL
                     THEN 1 END)                                                                    as missing_shipping_snapshot,
       COUNT(CASE
                 WHEN bill_address_id IS NOT NULL AND billing_address_snapshot IS NULL
                     THEN 1 END)                                                                    as missing_billing_snapshot
FROM sys_order_group
WHERE deleted = 0;

-- 检查地址快照数据格式正确性（抽样检查前10条）
SELECT order_id,
       address_id,
       JSON_EXTRACT(shipping_address_snapshot, '$.fullName') as shipping_full_name,
       bill_address_id,
       JSON_EXTRACT(billing_address_snapshot, '$.fullName')  as billing_full_name,
       create_time
FROM sys_order_group
WHERE shipping_address_snapshot IS NOT NULL
   OR billing_address_snapshot IS NOT NULL
ORDER BY create_time DESC
LIMIT 10;

-- ==========================================
-- 异常处理和补救脚本
-- ==========================================

-- 查找存在地址ID但无法找到对应地址记录的订单（可能是地址已被删除）
SELECT sog.order_id,
       sog.address_id,
       sog.bill_address_id,
       sog.create_time,
       CASE WHEN sua.id IS NULL THEN '收货地址已删除' ELSE '收货地址正常' END as shipping_status,
       CASE
           WHEN suia.id IS NULL AND sog.bill_address_id IS NOT NULL THEN '账单地址已删除'
           WHEN sog.bill_address_id IS NULL THEN '无账单地址'
           ELSE '账单地址正常' END                                            as billing_status
FROM sys_order_group sog
         LEFT JOIN sys_user_address sua ON sog.address_id = sua.id AND sua.deleted = 0
         LEFT JOIN sys_user_invoice_address suia ON sog.bill_address_id = suia.id AND suia.deleted = 0
WHERE sog.deleted = 0
  AND (
    (sog.address_id IS NOT NULL AND sua.id IS NULL)
        OR (sog.bill_address_id IS NOT NULL AND suia.id IS NULL)
    )
ORDER BY sog.create_time DESC;

-- 对于地址已删除的历史订单，使用空JSON对象作为快照（保持数据完整性）
UPDATE sys_order_group
SET shipping_address_snapshot = JSON_OBJECT()
WHERE shipping_address_snapshot IS NULL
  AND address_id IS NOT NULL
  AND NOT EXISTS (SELECT 1
                  FROM sys_user_address sua
                  WHERE sua.id = address_id
                    AND sua.deleted = 0);

UPDATE sys_order_group
SET billing_address_snapshot = JSON_OBJECT()
WHERE billing_address_snapshot IS NULL
  AND bill_address_id IS NOT NULL
  AND NOT EXISTS (SELECT 1
                  FROM sys_user_invoice_address suia
                  WHERE suia.id = bill_address_id
                    AND suia.deleted = 0);

-- ==========================================
-- 执行完成后的最终验证
-- ==========================================

-- 最终数据完整性检查
SELECT '执行完成 - 数据统计'                                                                                       as status,
       COUNT(*)                                                                                                    as total_orders,
       COUNT(shipping_address_snapshot)                                                                            as orders_with_shipping_snapshot,
       COUNT(billing_address_snapshot)                                                                             as orders_with_billing_snapshot,
       ROUND(COUNT(shipping_address_snapshot) * 100.0 / COUNT(*), 2)                                               as shipping_snapshot_coverage_percent,
       ROUND(COUNT(billing_address_snapshot) * 100.0 / COUNT(CASE WHEN bill_address_id IS NOT NULL THEN 1 END),
             2)                                                                                                    as billing_snapshot_coverage_percent
FROM sys_order_group
WHERE deleted = 0;
