# B2B 电商父子订单状态流转图

该流程图完整展示了父订单和子订单在 B2B 电商系统中的状态流转关系，包含支付、发货、售后等核心业务流程：

```mermaid
graph TB
    %% 父订单状态流转
    subgraph 父订单状态
        PG_PENDING_PAYMENT(待支付) --> |支付发起| PG_IN_PAYMENT(支付中)
        PG_IN_PAYMENT --> |支付成功| PG_PAID(已支付)
        PG_IN_PAYMENT --> |支付失败| PG_PENDING_PAYMENT
        
        PG_PAID --> |准备发货| PG_PENDING_SHIPMENT(待发货)
        PG_PENDING_SHIPMENT --> |全部发货| PG_SHIPPED(已发货)
        PG_PENDING_SHIPMENT --> |部分发货| PG_PARTIALLY_SHIPPED(部分发货)
        PG_PARTIALLY_SHIPPED --> |剩余发货| PG_SHIPPED
        
        PG_SHIPPED --> |物流签收| PG_DELIVERED(已送达)
        PG_DELIVERED --> |确认完成| PG_COMPLETED(已完成)
        
        %% 售后路径
        PG_PAID --> |申请退款| PG_REFUNDING(退款中)
        PG_PENDING_SHIPMENT --> |申请退款| PG_REFUNDING
        PG_REFUNDING --> |退款完成| PG_PARTIALLY_CANCELLED(部分取消)
        PG_REFUNDING --> |全额退款| PG_CANCELLED(已取消)
        
        PG_SHIPPED --> |申请退货| PG_RETURNING(退货中)
        PG_DELIVERED --> |申请退货| PG_RETURNING
        PG_RETURNING --> |退货完成| PG_PARTIALLY_CANCELLED
        PG_RETURNING --> |全部退货| PG_CANCELLED
        
        %% 取消路径
        PG_PENDING_PAYMENT --> |订单取消| PG_CANCELLED
        PG_PENDING_PAYMENT --> |用户主动关闭| PG_SYSTEM_CANCELLED
        PG_IN_PAYMENT --> |订单取消| PG_CANCELLED
        PG_PARTIALLY_SHIPPED --> |剩余取消| PG_PARTIALLY_CANCELLED
    end
    
    %% 子订单状态流转
    subgraph 子订单状态
        PI_PENDING_PAYMENT(待支付) --> |支付成功| PI_PAID(已支付)
        PI_PAID --> |发货准备| PI_PENDING_SHIPMENT(待发货)
        PI_PENDING_SHIPMENT --> |发货完成| PI_SHIPPED(已发货)
        PI_SHIPPED --> |物流签收| PI_DELIVERED(已送达)
        PI_DELIVERED --> |确认完成| PI_COMPLETED(已完成)
        
        %% 取消路径
        PI_PENDING_PAYMENT --> |订单取消| PI_CANCELLED(已取消)
        PI_PENDING_PAYMENT --> |用户主动关闭| PI_SYSTEM_CANCELLED(系统取消)
        PI_PAID --> |订单取消| PI_CANCELLED
        PI_PENDING_SHIPMENT --> |订单取消| PI_CANCELLED
    end
    
    %% 父子订单状态关联
    PI_PAID --> |所有子订单支付成功| PG_PAID
    PI_SHIPPED --> |子订单发货触发| PG_PARTIALLY_SHIPPED
    PI_CANCELLED --> |子订单取消触发| PG_PARTIALLY_CANCELLED
    PI_CANCELLED --> |所有子订单取消| PG_CANCELLED 
    
```

## 订单超时处理机制

### 延迟消息管理

系统采用延迟消息机制处理订单超时，支持消息取消以避免重复处理：

```mermaid
graph TB
    subgraph "订单创建流程"
        A[用户创建订单] --> B[订单服务创建订单]
        B --> C[发送延迟消息到Redis ZSet]
        C --> D[商品服务锁定库存]
    end

    subgraph "订单取消流程"
        E[用户手动取消订单] --> F[订单服务处理取消]
        F --> G[发送订单取消事件]
        G --> H[商品服务释放库存]
        G --> I[调用延迟服务取消消息]
        I --> J[从Redis ZSet删除延迟消息]
    end

    subgraph "延迟消息处理"
        K[定时任务扫描延迟队列] --> L{延迟消息是否存在?}
        L -->|存在| M[发送超时消息到MQ]
        L -->|不存在已被取消| N[跳过处理]
        M --> O[各服务处理订单超时]
    end

    style A fill:#e1f5fe
    style E fill:#fff3e0
    style J fill:#c8e6c9
    style N fill:#c8e6c9
```

### 核心机制

#### 1. 延迟消息创建
- 订单创建时自动发送5分钟延迟消息
- 消息存储在Redis ZSet中，以触发时间为score
- 消息体包含订单ID、用户ID等关键信息

#### 2. 延迟消息取消
- 订单手动取消时，同时取消对应的延迟消息
- 通过订单ID匹配并删除Redis中的延迟消息
- 防止已取消订单的延迟消息触发错误处理

#### 3. 超时处理
- 定时任务扫描到期的延迟消息
- 只处理仍存在于队列中的消息
- 已取消的订单不会触发超时处理

### 技术实现

#### 延迟服务接口
```java
// 添加延迟消息
POST /delayed-services/api/add

// 取消延迟消息
POST /delayed-services/api/cancel/{orderId}
```

#### 关键组件
- **DelayedMessage**: 延迟消息实体
- **RedisDelayedQueueService**: 延迟队列管理服务
- **OrderCancelledEventListener**: 订单取消事件监听器
- **SendMessageJob**: 延迟消息扫描定时任务

### 优势特点

1. **数据一致性**: 避免延迟消息释放其他订单的库存
2. **高性能**: 基于Redis的高效操作
3. **可靠性**: 完善的错误处理和降级机制
4. **兼容性**: 不影响现有业务逻辑

### 监控指标

- 延迟消息创建/取消数量
- 延迟消息处理成功率
- Redis队列大小变化
- 服务响应时间

## 订单关闭流程

### 功能概述

用户主动关闭订单功能允许用户在订单处于待支付状态时主动关闭订单，系统将自动释放锁定的库存并取消相关的延迟消息。

### 业务规则

#### 1. 关闭条件
- **状态限制**: 只有处于`PENDING_PAYMENT`（待支付）状态的订单可以被用户主动关闭
- **权限验证**: 需要用户身份验证，只能关闭自己的订单
- **重复检查**: 已经处于取消状态的订单无需重复关闭

#### 2. 关闭流程
```mermaid
graph TB
    A[用户发起关闭订单请求] --> B[验证用户身份]
    B --> C[检查订单是否存在]
    C --> D{订单状态是否为待支付?}
    D -->|否| E[返回错误：订单状态不允许关闭]
    D -->|是| F[检查订单是否已取消]
    F -->|已取消| G[直接返回成功]
    F -->|未取消| H[更新父订单状态为系统取消]
    H --> I[更新子订单状态为系统取消]
    I --> J[更新订单项状态为系统取消]
    J --> K[发送订单取消事件]
    K --> L[释放锁定库存]
    K --> M[取消延迟消息]
    L --> N[返回关闭成功]
    M --> N

    style A fill:#e1f5fe
    style N fill:#c8e6c9
    style E fill:#ffebee
```

#### 3. 状态变更
- **父订单**: `PENDING_PAYMENT` → `SYSTEM_CANCELLED`
- **子订单**: `PENDING_PAYMENT` → `SYSTEM_CANCELLED`
- **订单项**: `PENDING_PAYMENT` → `SYSTEM_CANCELLED`

### 技术实现

#### 接口定义
```java
/**
 * 用户主动关闭订单
 * @param parentOrderId 父订单ID
 * @return 关闭结果
 */
@PostMapping("/closed/{parentOrderId}")
public HttpResult<Void> closedOrder(@PathVariable("parentOrderId") String parentOrderId)
```

#### 核心方法
```java
/**
 * 关闭订单业务逻辑
 * @param parentOrderId 父订单ID
 */
void closeOrder(String parentOrderId)
```

#### 关键特性
1. **分布式锁**: 使用`@DistributedLock`防止并发操作
2. **事务保证**: 使用`@Transactional`确保数据一致性
3. **状态验证**: 通过`KnetOrderGroupStatus.isClosable()`验证可关闭状态
4. **事件驱动**: 发送`OrderCancelledEvent`触发后续处理
5. **补偿机制**: 自动释放库存和取消延迟消息

### 消息流转

#### 1. 库存释放
- 发送`OrderCancelledEvent`事件
- 商品服务监听事件并释放锁定库存
- 支持库存释放失败的重试机制

#### 2. 延迟消息取消
- 调用延迟服务取消对应的超时消息
- 防止已关闭订单的延迟消息被错误处理
- 支持取消失败的降级处理

### 错误处理

#### 常见错误场景
1. **订单不存在**: 返回"订单不存在"错误
2. **状态不允许**: 返回"订单状态不允许关闭"错误
3. **系统异常**: 返回"关闭订单失败"错误

#### 降级策略
- 延迟消息取消失败时记录日志但不影响主流程
- 库存释放通过MQ异步处理，支持重试机制
- 数据库操作失败时回滚事务

### 监控指标

- 订单关闭请求数量
- 订单关闭成功率
- 订单关闭响应时间
- 库存释放成功率
- 延迟消息取消成功率