package com.knet.common.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 发票编号生成工具类
 */
@Slf4j
@Component
public class InvoiceNumberUtil {

    private static final String INVOICE_NUMBER_REDIS_KEY = "invoice:number:generated:";
    private static final int MAX_RETRY_TIMES = 10;

    /**
     * 生成发票编号
     * 格式：[month][day][year][随机4位数字]
     * 例如：12092025XXXX
     *
     * @return 发票编号
     */
    public String generateInvoiceNumber() {
        return generateInvoiceNumber(new Date());
    }

    /**
     * 根据指定日期生成发票编号
     * 格式：[month][day][year][随机4位数字]
     *
     * @param date 指定日期
     * @return 发票编号
     */
    public String generateInvoiceNumber(Date date) {
        LocalDate localDate = DateUtil.toLocalDateTime(date).toLocalDate();
        return generateInvoiceNumber(localDate);
    }

    /**
     * 根据指定日期生成发票编号
     * 格式：[month][day][year][随机4位数字]
     *
     * @param localDate 指定日期
     * @return 发票编号
     */
    public String generateInvoiceNumber(LocalDate localDate) {
        String datePart = localDate.format(DateTimeFormatter.ofPattern("MMddyyyy"));
        // 生成唯一的发票编号，确保不重复
        for (int i = 0; i < MAX_RETRY_TIMES; i++) {
            String randomPart = RandomUtil.randomNumbers(4);
            String invoiceNumber = datePart + randomPart;
            // 使用Redis确保编号唯一性
            String redisKey = INVOICE_NUMBER_REDIS_KEY + invoiceNumber;
            if (RedisCacheUtil.setIfAbsent(redisKey, "1", 24 * 60 * 60)) {
                log.info("生成发票编号成功: {}", invoiceNumber);
                return invoiceNumber;
            }
            log.warn("发票编号重复，重新生成: {}", invoiceNumber);
        }
        // 如果重试多次仍然重复，使用时间戳确保唯一性
        String timestamp = String.valueOf(System.currentTimeMillis()).substring(6);
        String invoiceNumber = datePart + timestamp;
        String redisKey = INVOICE_NUMBER_REDIS_KEY + invoiceNumber;
        RedisCacheUtil.set(redisKey, "1", 86400);
        log.warn("使用时间戳生成发票编号: {}", invoiceNumber);
        return invoiceNumber;
    }

    /**
     * 验证发票编号格式是否正确
     *
     * @param invoiceNumber 发票编号
     * @return 是否格式正确
     */
    public boolean isValidInvoiceNumber(String invoiceNumber) {
        if (invoiceNumber == null || invoiceNumber.length() != 12) {
            return false;
        }
        try {
            // 验证前8位是否为有效日期格式
            String datePart = invoiceNumber.substring(0, 8);
            LocalDate.parse(datePart, DateTimeFormatter.ofPattern("MMddyyyy"));
            // 验证后4位是否为数字
            String numberPart = invoiceNumber.substring(8);
            Integer.parseInt(numberPart);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 从发票编号中提取日期
     *
     * @param invoiceNumber 发票编号
     * @return 日期
     */
    public LocalDate extractDateFromInvoiceNumber(String invoiceNumber) {
        if (!isValidInvoiceNumber(invoiceNumber)) {
            throw new IllegalArgumentException("无效的发票编号格式: " + invoiceNumber);
        }
        String datePart = invoiceNumber.substring(0, 8);
        return LocalDate.parse(datePart, DateTimeFormatter.ofPattern("MMddyyyy"));
    }

    /**
     * 检查发票编号是否已存在
     *
     * @param invoiceNumber 发票编号
     * @return 是否已存在
     */
    public boolean isInvoiceNumberExists(String invoiceNumber) {
        String redisKey = INVOICE_NUMBER_REDIS_KEY + invoiceNumber;
        return RedisCacheUtil.hasKey(redisKey);
    }

    /**
     * 标记发票编号为已使用
     *
     * @param invoiceNumber 发票编号
     */
    public void markInvoiceNumberAsUsed(String invoiceNumber) {
        String redisKey = INVOICE_NUMBER_REDIS_KEY + invoiceNumber;
        RedisCacheUtil.set(redisKey, "1", 86400);
        log.info("标记发票编号为已使用: {}", invoiceNumber);
    }
}
