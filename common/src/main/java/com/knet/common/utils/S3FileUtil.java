package com.knet.common.utils;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;

/**
 * AWS S3 文件上传下载工具类
 * 基于AWS SDK v2实现，手动生成预签名URL
 *
 * <AUTHOR>
 * @date 2025/09/10
 */
@Slf4j
@Component
public class S3FileUtil {

    private final String bucketName = "knetb2b";
    private final String region = "us-west-2";
    private final Long presignedUrlDuration = 3600L;
    private final String cdnDomain = "https://uploadsb2b.knetgroup.com";

    private S3Client s3Client;
    private DefaultCredentialsProvider credentialsProvider;

    @PostConstruct
    public void init() {
        try {
            // 使用默认凭证提供者，支持EC2实例角色
            this.credentialsProvider = DefaultCredentialsProvider.builder().build();

            this.s3Client = S3Client.builder()
                    .region(Region.of(region))
                    .credentialsProvider(credentialsProvider)
                    .build();

            log.info("S3FileUtil 初始化成功, bucket: {}, region: {}", bucketName, region);
        } catch (Exception e) {
            log.error("S3FileUtil 初始化失败", e);
            throw new RuntimeException("S3FileUtil 初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        if (s3Client != null) {
            s3Client.close();
        }
        log.info("S3FileUtil 资源释放完成");
    }

    /**
     * 生成预签名上传URL
     *
     * @param fileName 文件名
     * @param fileType 文件类型 (MIME type)
     * @param folder   文件夹路径 (可选)
     * @return 预签名上传URL信息
     */
    public PresignedUploadInfo generatePresignedUploadUrl(String fileName, String fileType, String folder) {
        try {
            String key = generateFileKey(fileName, folder);

            // 生成预签名PUT URL
            String uploadUrl = generatePresignedPutUrl(key, fileType);
            String downloadUrl = generateDownloadUrl(key);

            log.info("生成预签名上传URL成功, key: {}, uploadUrl: {}", key, uploadUrl);

            return PresignedUploadInfo.builder()
                    .uploadUrl(uploadUrl)
                    .downloadUrl(downloadUrl)
                    .fileKey(key)
                    .fileName(fileName)
                    .expiresIn(presignedUrlDuration)
                    .fields(generateUploadFields(key, fileType))
                    .build();
        } catch (Exception e) {
            log.error("生成预签名上传URL失败, fileName: {}, fileType: {}", fileName, fileType, e);
            throw new RuntimeException("生成预签名上传URL失败", e);
        }
    }

    /**
     * 生成预签名下载URL
     *
     * @param fileKey 文件在S3中的key
     * @return 预签名下载URL
     */
    public String generatePresignedDownloadUrl(String fileKey) {
        try {
            // 生成预签名GET URL
            String downloadUrl = generatePresignedGetUrl(fileKey);
            log.info("生成预签名下载URL成功, key: {}, downloadUrl: {}", fileKey, downloadUrl);
            return downloadUrl;
        } catch (Exception e) {
            log.error("生成预签名下载URL失败, fileKey: {}", fileKey, e);
            throw new RuntimeException("生成预签名下载URL失败", e);
        }
    }

    /**
     * 上传文件 (字节数组)
     *
     * @param fileBytes 文件字节数组
     * @param fileName  文件名
     * @return 文件下载地址
     */
    public String uploadFile(byte[] fileBytes, String fileName) {
        try {
            String key = generateFileKey(fileName, null);
            String contentType = getContentTypeFromFileName(fileName);
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(contentType)
                    .contentLength((long) fileBytes.length)
                    .build();
            s3Client.putObject(putObjectRequest, software.amazon.awssdk.core.sync.RequestBody.fromBytes(fileBytes));
            String downloadUrl = generateDownloadUrl(key);
            log.info("文件上传成功, key: {}, fileName: {}, size: {} bytes", key, fileName, fileBytes.length);
            return downloadUrl;
        } catch (Exception e) {
            log.error("文件上传失败, fileName: {}", fileName, e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    /**
     * 上传文件 (输入流)
     *
     * @param inputStream 文件输入流
     * @param fileName    文件名
     * @return 文件下载地址
     */
    public String uploadFile(java.io.InputStream inputStream, String fileName) {
        try {
            String key = generateFileKey(fileName, null);
            String contentType = getContentTypeFromFileName(fileName);
            // 读取输入流到字节数组
            java.io.ByteArrayOutputStream buffer = new java.io.ByteArrayOutputStream();
            int nRead;
            byte[] data = new byte[1024];
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            byte[] fileBytes = buffer.toByteArray();
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(contentType)
                    .contentLength((long) fileBytes.length)
                    .build();
            s3Client.putObject(putObjectRequest,
                    software.amazon.awssdk.core.sync.RequestBody.fromBytes(fileBytes));
            String downloadUrl = generateDownloadUrl(key);
            log.info("文件上传成功, key: {}, fileName: {}, size: {} bytes", key, fileName, fileBytes.length);
            return downloadUrl;
        } catch (Exception e) {
            log.error("文件上传失败, fileName: {}", fileName, e);
            throw new RuntimeException("文件上传失败", e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (java.io.IOException e) {
                log.warn("关闭输入流失败", e);
            }
        }
    }

    /**
     * 删除文件
     *
     * @param fileKey 文件在S3中的key
     * @return 是否删除成功
     */
    public boolean deleteFile(String fileKey) {
        try {
            DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileKey)
                    .build();
            s3Client.deleteObject(deleteObjectRequest);
            log.info("删除文件成功, key: {}", fileKey);
            return true;
        } catch (Exception e) {
            log.error("删除文件失败, fileKey: {}", fileKey, e);
            return false;
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param fileKey 文件在S3中的key
     * @return 文件是否存在
     */
    public boolean fileExists(String fileKey) {
        try {
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileKey)
                    .build();
            s3Client.headObject(headObjectRequest);
            return true;
        } catch (NoSuchKeyException e) {
            return false;
        } catch (Exception e) {
            log.error("检查文件是否存在失败, fileKey: {}", fileKey, e);
            return false;
        }
    }

    /**
     * 获取文件信息
     *
     * @param fileKey 文件在S3中的key
     * @return 文件信息
     */
    public FileInfo getFileInfo(String fileKey) {
        try {
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileKey)
                    .build();
            HeadObjectResponse response = s3Client.headObject(headObjectRequest);

            return FileInfo.builder()
                    .fileKey(fileKey)
                    .contentType(response.contentType())
                    .contentLength(response.contentLength())
                    .lastModified(response.lastModified())
                    .etag(response.eTag())
                    .downloadUrl(generateDownloadUrl(fileKey))
                    .build();
        } catch (Exception e) {
            log.error("获取文件信息失败, fileKey: {}", fileKey, e);
            throw new RuntimeException("获取文件信息失败", e);
        }
    }

    /**
     * 生成预签名PUT URL
     */
    private String generatePresignedPutUrl(String key, String contentType) {
        try {
            AwsCredentials credentials = credentialsProvider.resolveCredentials();

            Instant now = Instant.now();
            String dateStamp = DateTimeFormatter.ofPattern("yyyyMMdd")
                    .withZone(ZoneOffset.UTC).format(now);
            String amzDate = DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'")
                    .withZone(ZoneOffset.UTC).format(now);

            String credentialScope = String.format("%s/%s/s3/aws4_request", dateStamp, region);
            String credential = String.format("%s/%s", credentials.accessKeyId(), credentialScope);

            // 构建查询参数
            Map<String, String> queryParams = new LinkedHashMap<>();
            queryParams.put("X-Amz-Algorithm", "AWS4-HMAC-SHA256");
            queryParams.put("X-Amz-Credential", credential);
            queryParams.put("X-Amz-Date", amzDate);
            queryParams.put("X-Amz-Expires", String.valueOf(presignedUrlDuration));
            queryParams.put("X-Amz-SignedHeaders", "host");

            // 计算签名
            String canonicalQueryString = buildCanonicalQueryString(queryParams);
            String canonicalHeaders = "host:" + bucketName + ".s3." + region + ".amazonaws.com\n";
            String signedHeaders = "host";
            String payloadHash = "UNSIGNED-PAYLOAD";

            String canonicalRequest = String.format("%s\n%s\n%s\n%s\n%s\n%s",
                    "PUT",
                    "/" + urlEncode(key),
                    canonicalQueryString,
                    canonicalHeaders,
                    signedHeaders,
                    payloadHash);

            String stringToSign = String.format("AWS4-HMAC-SHA256\n%s\n%s\n%s",
                    amzDate,
                    credentialScope,
                    sha256Hex(canonicalRequest));

            String signature = calculateSignature(credentials.secretAccessKey(), dateStamp, stringToSign);
            queryParams.put("X-Amz-Signature", signature);

            String finalQueryString = buildCanonicalQueryString(queryParams);
            return String.format("https://%s.s3.%s.amazonaws.com/%s?%s",
                    bucketName, region, urlEncode(key), finalQueryString);

        } catch (Exception e) {
            throw new RuntimeException("生成预签名PUT URL失败", e);
        }
    }

    /**
     * 生成预签名GET URL
     */
    private String generatePresignedGetUrl(String key) {
        try {
            AwsCredentials credentials = credentialsProvider.resolveCredentials();

            Instant now = Instant.now();
            String dateStamp = DateTimeFormatter.ofPattern("yyyyMMdd")
                    .withZone(ZoneOffset.UTC).format(now);
            String amzDate = DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'")
                    .withZone(ZoneOffset.UTC).format(now);

            String credentialScope = String.format("%s/%s/s3/aws4_request", dateStamp, region);
            String credential = String.format("%s/%s", credentials.accessKeyId(), credentialScope);

            // 构建查询参数
            Map<String, String> queryParams = new LinkedHashMap<>();
            queryParams.put("X-Amz-Algorithm", "AWS4-HMAC-SHA256");
            queryParams.put("X-Amz-Credential", credential);
            queryParams.put("X-Amz-Date", amzDate);
            queryParams.put("X-Amz-Expires", String.valueOf(presignedUrlDuration));
            queryParams.put("X-Amz-SignedHeaders", "host");

            // 计算签名
            String canonicalQueryString = buildCanonicalQueryString(queryParams);
            String canonicalHeaders = "host:" + bucketName + ".s3." + region + ".amazonaws.com\n";
            String signedHeaders = "host";
            String payloadHash = "UNSIGNED-PAYLOAD";

            String canonicalRequest = String.format("%s\n%s\n%s\n%s\n%s\n%s",
                    "GET",
                    "/" + urlEncode(key),
                    canonicalQueryString,
                    canonicalHeaders,
                    signedHeaders,
                    payloadHash);

            String stringToSign = String.format("AWS4-HMAC-SHA256\n%s\n%s\n%s",
                    amzDate,
                    credentialScope,
                    sha256Hex(canonicalRequest));

            String signature = calculateSignature(credentials.secretAccessKey(), dateStamp, stringToSign);
            queryParams.put("X-Amz-Signature", signature);

            String finalQueryString = buildCanonicalQueryString(queryParams);
            return String.format("https://%s.s3.%s.amazonaws.com/%s?%s",
                    bucketName, region, urlEncode(key), finalQueryString);

        } catch (Exception e) {
            throw new RuntimeException("生成预签名GET URL失败", e);
        }
    }

    /**
     * 计算AWS4签名
     */
    private String calculateSignature(String secretKey, String dateStamp, String stringToSign) throws Exception {
        byte[] kSecret = ("AWS4" + secretKey).getBytes(StandardCharsets.UTF_8);
        byte[] kDate = hmacSha256(kSecret, dateStamp);
        byte[] kRegion = hmacSha256(kDate, region);
        byte[] kService = hmacSha256(kRegion, "s3");
        byte[] kSigning = hmacSha256(kService, "aws4_request");
        byte[] signature = hmacSha256(kSigning, stringToSign);

        return bytesToHex(signature);
    }

    /**
     * HMAC-SHA256计算
     */
    private byte[] hmacSha256(byte[] key, String data) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(key, "HmacSHA256"));
        return mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * SHA256哈希
     */
    private String sha256Hex(String data) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(hash);
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 构建规范查询字符串
     */
    private String buildCanonicalQueryString(Map<String, String> queryParams) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : queryParams.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(urlEncode(entry.getKey())).append("=").append(urlEncode(entry.getValue()));
        }
        return sb.toString();
    }

    /**
     * URL编码
     */
    private String urlEncode(String value) {
        try {
            return URLEncoder.encode(value, StandardCharsets.UTF_8)
                    .replace("+", "%20")
                    .replace("*", "%2A")
                    .replace("%7E", "~");
        } catch (Exception e) {
            return value;
        }
    }

    /**
     * 生成文件key
     */
    private String generateFileKey(String fileName, String folder) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileExtension = getFileExtension(fileName);
        String newFileName = uuid + (fileExtension.isEmpty() ? "" : "." + fileExtension);

        // 确保所有文件都在/data目录下
        String baseFolder = "data";

        if (folder != null && !folder.trim().isEmpty()) {
            folder = folder.trim().replaceAll("^/+", "").replaceAll("/+$", "");
            return baseFolder + "/" + folder + "/" + newFileName;
        }
        return baseFolder + "/" + newFileName;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 生成下载URL
     */
    private String generateDownloadUrl(String fileKey) {
        // 从文件key中移除data前缀，只保留文件名部分
        String urlPath = fileKey;
        if (fileKey.startsWith("data/")) {
            urlPath = fileKey.substring(5);
        }
        if (cdnDomain != null && !cdnDomain.trim().isEmpty()) {
            // 使用CDN域名，对文件路径进行URL编码
            return cdnDomain.trim().replaceAll("/+$", "") + "/" + encodeURLPath(urlPath);
        } else {
            // 使用S3默认域名，对文件路径进行URL编码
            return String.format("https://%s.s3.%s.amazonaws.com/%s", bucketName, region, encodeURLPath(urlPath));
        }
    }

    /**
     * 对URL路径进行编码，确保特殊字符能正确处理
     *
     * @param path 需要编码的路径
     * @return 编码后的路径
     */
    private String encodeURLPath(String path) {
        try {
            // 对路径进行URL编码，但保留路径分隔符
            String[] pathParts = path.split("/");
            StringBuilder encodedPath = new StringBuilder();

            for (int i = 0; i < pathParts.length; i++) {
                if (i > 0) {
                    encodedPath.append("/");
                }
                // 对每个路径部分进行URL编码
                encodedPath.append(URLEncoder.encode(pathParts[i], StandardCharsets.UTF_8)
                        .replace("+", "%20")
                        .replace("*", "%2A")
                        .replace("%7E", "~"));
            }
            return encodedPath.toString();
        } catch (Exception e) {
            log.warn("URL路径编码失败，使用原始路径: {}", path, e);
            return path;
        }
    }

    /**
     * 生成上传表单字段
     */
    private Map<String, String> generateUploadFields(String key, String contentType) {
        Map<String, String> fields = new HashMap<>();
        fields.put("key", key);
        fields.put("Content-Type", contentType);
        return fields;
    }

    /**
     * 根据文件名获取Content-Type
     */
    private String getContentTypeFromFileName(String fileName) {
        if (fileName == null) {
            return "application/octet-stream";
        }

        String extension = getFileExtension(fileName);
        switch (extension) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "pdf":
                return "application/pdf";
            case "txt":
                return "text/plain";
            case "html":
                return "text/html";
            case "css":
                return "text/css";
            case "js":
                return "application/javascript";
            case "json":
                return "application/json";
            case "xml":
                return "application/xml";
            case "zip":
                return "application/zip";
            case "mp4":
                return "video/mp4";
            case "mp3":
                return "audio/mpeg";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 预签名上传URL信息
     */
    @Data
    @Builder
    public static class PresignedUploadInfo {
        private String uploadUrl;
        private String downloadUrl;
        private String fileKey;
        private String fileName;
        private Long expiresIn;
        private Map<String, String> fields;
    }

    /**
     * 文件信息
     */
    @Data
    @Builder
    public static class FileInfo {
        private String fileKey;
        private String contentType;
        private Long contentLength;
        private Instant lastModified;
        private String etag;
        private String downloadUrl;
    }
}
