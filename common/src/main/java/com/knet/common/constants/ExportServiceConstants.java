package com.knet.common.constants;

/**
 * <AUTHOR>
 * @date 2025/9/25 16:56
 * @description: 导出服务常量
 */
public class ExportServiceConstants {

    /**
     * 默认分页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 1000;
    /**
     * 任务队列key
     */
    public static final String TASK_QUEUE_KEY = "export:task:queue";
    /**
     * 任务锁前缀
     */
    public static final String TASK_LOCK_PREFIX = "export:task:lock:";
    /**
     * 超时监控key
     */
    public static final String TIMEOUT_MONITOR_KEY = "export:task:timeout";
    /**
     * 锁的默认过期时间（秒）30分钟
     * 考虑到大数据量导出可能需要较长时间
     */
    public static final int DEFAULT_LOCK_EXPIRE_SECONDS = 1800;

    /**
     * 任务执行超时时间（秒）20分钟
     * 超过此时间的任务将被标记为超时并释放资源
     */
    public static final int TASK_TIMEOUT_SECONDS = 1200;
}
