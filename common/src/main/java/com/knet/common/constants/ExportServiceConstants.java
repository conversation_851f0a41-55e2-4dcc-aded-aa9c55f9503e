package com.knet.common.constants;

/**
 * <AUTHOR>
 * @date 2025/9/25 16:56
 * @description: 导出服务常量
 */
public class ExportServiceConstants {

    /**
     * 默认分页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 1000;
    /**
     * 任务队列key
     */
    public static final String TASK_QUEUE_KEY = "export:task:queue";
    /**
     * 任务锁前缀
     */
    public static final String TASK_LOCK_PREFIX = "export:task:lock:";
    /**
     * 超时监控key
     */
    public static final String TIMEOUT_MONITOR_KEY = "export:task:timeout";
    /**
     *
     * 锁的默认过期时间（秒）10分钟
     */
    public static final int DEFAULT_LOCK_EXPIRE_SECONDS = 600;
}
