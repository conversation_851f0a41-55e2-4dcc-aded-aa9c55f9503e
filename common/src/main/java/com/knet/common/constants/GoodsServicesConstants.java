package com.knet.common.constants;

/**
 * <AUTHOR>
 * @date 2025/2/26 10:15
 * @description: 商品服务常量
 */
public class GoodsServicesConstants {
    /**
     * knet 价格转换比例
     * 美分换美元
     */
    public static final Long ONE_HUNDRED = 100L;
    /**
     * knet 商品缓存key
     */
    public static final String SKU_CACHE_KEY = "sku:index:cache";
    /**
     * knet 商品缓存过期时间 24小时
     */
    public static final Long SKU_CACHE_KEY_EXPIRED_TIME = 24 * 60 * 60L;

    /**
     * knet SKU备注缓存key
     */
    public static final String KNET_PRODUCT_REMARK_CACHE_KEY = "product:remark:cache";
    /**
     * sys_sku remark缓存
     */
    public static final String SKU_REMARK_CACHE_KEY = "sku:remark:cache";
    /**
     * knet SKU备注缓存过期时间 24小时
     */
    public static final Long KNET_PRODUCT_REMARK_CACHE_KEY_EXPIRED_TIME = 24 * 60 * 60L;
    /**
     * sys_sku remark缓存过期时间 24小时
     */
    public static final Long SKU_REMARK_CACHE_KEY_EXPIRED_TIME = 24 * 60 * 60L;
    /**
     * 价格缓存key前缀
     */
    public static final String PRICE_CACHE_PREFIX = "price:";
    /**
     * 价格缓存过期时间 5分钟
     */
    public static final long PRICE_CACHE_EXPIRE = 5 * 60L;
    /**
     * 重试次数
     */
    public static final int MAX_RETRY_COUNT = 3;
    /**
     * 重试间隔5分钟
     */
    public static final int RETRY_DELAY_MINUTES = 5;
    /**
     * knetGroup 价格计算公式-浮动利率
     */
    public static final double KG_FEE = 1.1;
}
