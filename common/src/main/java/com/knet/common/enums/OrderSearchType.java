package com.knet.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/9 10:58
 * @description: 订单搜索类型枚举
 */
@Getter
@AllArgsConstructor
public enum OrderSearchType {
    /**
     * 处理中
     */
    PENDING(1, "PENDING", "处理中"),

    /**
     * 已完成
     */
    COMPLETED(2, "COMPLETED", "已完成"),

    /**
     * 已取消
     */
    CANCELLED(3, "CANCELLED", "已取消"),
    
    /**
     * 已关闭
     */
    CLOSED(4, "CLOSED", "已关闭");

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 交易类型名称，用于数据库存储
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    public static OrderSearchType fromCode(int code) {
        for (OrderSearchType orderSearchType : OrderSearchType.values()) {
            if (orderSearchType.getCode() == code) {
                return orderSearchType;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }
}
