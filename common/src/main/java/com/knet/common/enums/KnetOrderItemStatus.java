package com.knet.common.enums;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/3 16:09
 * @description: 订单明细表-订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum KnetOrderItemStatus {
    /**
     * 支付状态
     */
    PENDING_PAYMENT(0, "PENDING_PAYMENT", "待支付"),
    PAID(1, "PAID", "已支付"),
    PAY_FAILED(2, "PAY_FAILED", "支付失败"),
    /**
     * 物流状态
     */
    PENDING_SHIPMENT(3, "PENDING_SHIPMENT", "待发货"),
    IN_TRANSIT(4, "IN_TRANSIT", "在途中"),
    SHIPPED(11, "SHIPPED", "已发货"),

    COMPLETED(5, "COMPLETED", "已完成"),
    CANCELLED(6, "CANCELLED", "已取消"),
    /**
     * 特殊状态，订单被KG拉走，不能取消
     */
    FROZEN(7, "FROZEN", "已冻结"),
    /**
     * 系统取消状态，订单未支付超时由系统自动取消
     */
    SYSTEM_CANCELLED(8, "SYSTEM_CANCELLED", "系统取消");
    /**
     * 状态代码
     */
    @EnumValue
    private final Integer code;
    /**
     * 状态名称
     */
    @JsonValue
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    public static KnetOrderItemStatus fromCode(int code) {
        for (KnetOrderItemStatus status : KnetOrderItemStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }

    /**
     * 确定订单是否处于可以被取消的状态
     * 已支付状态的订单可以被取消
     *
     * @param status 订单状态
     * @return 是否可以取消
     */
    public static Boolean isCancelable(KnetOrderItemStatus status) {
        return status == PAID;
    }

    /**
     * 已支付 冻结转换成为待发货
     *
     * @param status status
     * @return 状态
     */
    public static KnetOrderItemStatus displayStatus(KnetOrderItemStatus status) {
        if (BeanUtil.isEmpty(status)) {
            return null;
        }
        if (status == KnetOrderItemStatus.PAID ||
                status == KnetOrderItemStatus.FROZEN) {
            return KnetOrderItemStatus.PENDING_SHIPMENT;
        }
        // 运输中显示为，已发货
        if (status == KnetOrderItemStatus.IN_TRANSIT) {
            return KnetOrderItemStatus.SHIPPED;
        }
        return status;
    }

    /**
     * 判断当前状态是否需要被过滤（例如 CANCELLED 或 SYSTEM_CANCELLED）
     */
    public boolean invoiceFilter() {
        return this == CANCELLED || this == SYSTEM_CANCELLED;
    }
}
