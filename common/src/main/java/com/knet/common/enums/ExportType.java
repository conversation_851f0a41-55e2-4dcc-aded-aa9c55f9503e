package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导出类型枚举
 * 用于标识不同服务的各种导出场景
 *
 * <AUTHOR> Export Team
 * @since 2025-09-25
 */
@Getter
@AllArgsConstructor
@Schema(description = "导出类型枚举")
public enum ExportType {

    // 订单相关导出
    ADMIN_ORDER_LIST("ADMIN_ORDER_LIST", "管理员订单列表导出", "order"),

    // 商品相关导出
    GOODS_INVENTORY("GOODS_INVENTORY", "商品库存导出", "goods"),

    // 用户相关导出
    USER_LIST("USER_LIST", "用户列表导出", "user"),

    // 支付相关导出
    PAYMENT_TRANSACTION("PAYMENT_TRANSACTION", "支付交易记录导出", "payment"),
    REFUND_TRANSACTION("REFUND_TRANSACTION", "退款交易记录导出", "payment");

    @EnumValue
    @Schema(description = "导出类型代码")
    private final String code;

    @Schema(description = "导出类型描述")
    private final String description;

    @Schema(description = "所属系统ID")
    private final String systemId;

    /**
     * 根据代码获取枚举
     */
    public static ExportType fromCode(String code) {
        for (ExportType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的导出类型: " + code);
    }

    /**
     * 验证导出类型是否属于指定系统
     */
    public boolean belongsToSystem(String systemId) {
        return this.systemId.equals(systemId);
    }
}
