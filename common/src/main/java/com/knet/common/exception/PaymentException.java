package com.knet.common.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/12/12 10:00
 * @description: 支付异常类，用于区分不同类型的支付失败
 */
@Getter
public class PaymentException extends ServiceException {

    /**
     * 支付失败类型
     */
    public enum FailureType {
        /**
         * 业务规则失败 - 不更新订单状态
         * 如：订单状态不允许支付、重复支付、订单不存在等
         */
        BUSINESS_RULE_VIOLATION,

        /**
         * 系统异常失败 - 需要更新订单状态为支付失败
         * 如：余额不足、网络异常、数据库异常等
         */
        SYSTEM_ERROR,

        /**
         * 支付渠道失败 - 需要更新订单状态为支付失败
         * 如：第三方支付接口异常、银行卡问题等
         */
        PAYMENT_CHANNEL_ERROR
    }

    private final FailureType failureType;

    public PaymentException(String message, FailureType failureType) {
        super(message);
        this.failureType = failureType;
    }

    public PaymentException(String message, Throwable cause, FailureType failureType) {
        super(message, cause);
        this.failureType = failureType;
    }

    public PaymentException(String message, int statusCode, FailureType failureType) {
        super(message, statusCode);
        this.failureType = failureType;
    }

    /**
     * 判断是否需要更新订单状态为支付失败
     */
    public boolean shouldUpdateOrderStatus() {
        return failureType == FailureType.SYSTEM_ERROR || failureType == FailureType.PAYMENT_CHANNEL_ERROR;
    }

    /**
     * 创建业务规则违反异常
     */
    public static PaymentException businessRuleViolation(String message) {
        return new PaymentException(message, FailureType.BUSINESS_RULE_VIOLATION);
    }

    /**
     * 创建系统错误异常
     */
    public static PaymentException systemError(String message) {
        return new PaymentException(message, FailureType.SYSTEM_ERROR);
    }

    /**
     * 创建系统错误异常（带原因）
     */
    public static PaymentException systemError(String message, Throwable cause) {
        return new PaymentException(message, cause, FailureType.SYSTEM_ERROR);
    }

    /**
     * 创建支付渠道错误异常
     */
    public static PaymentException paymentChannelError(String message) {
        return new PaymentException(message, FailureType.PAYMENT_CHANNEL_ERROR);
    }

    /**
     * 创建支付渠道错误异常（带原因）
     */
    public static PaymentException paymentChannelError(String message, Throwable cause) {
        return new PaymentException(message, cause, FailureType.PAYMENT_CHANNEL_ERROR);
    }
}
