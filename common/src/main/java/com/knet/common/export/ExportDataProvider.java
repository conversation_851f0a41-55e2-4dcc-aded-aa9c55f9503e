package com.knet.common.export;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;
import java.util.stream.Stream;

/**
 * 统一导出数据接口规范
 * 各业务服务需要实现此接口来提供导出数据
 *
 * <AUTHOR> Export Team
 * @since 2025-09-25
 */
@Schema(description = "导出数据提供者接口")
public interface ExportDataProvider {

    /**
     * 流式查询导出数据
     * 使用游标分页避免深分页性能问题
     *
     * @param exportParams 导出参数（包含查询条件）
     * @param lastId       上次查询的最后一条记录ID（用于游标分页）
     * @param pageSize     分页大小，建议1000-5000
     * @return 数据流，每个Map代表一行数据
     */
    @Schema(description = "流式查询导出数据")
    Stream<Map<String, Object>> queryExportData(
            Map<String, Object> exportParams,
            String lastId,
            int pageSize
    );

    /**
     * 获取总记录数预估
     * 用于任务进度计算和资源评估
     *
     * @param exportParams 导出参数
     * @return 预估记录数
     */
    @Schema(description = "获取总记录数预估")
    long getEstimatedCount(Map<String, Object> exportParams);

    /**
     * 验证导出权限
     * 在创建导出任务前进行权限校验
     *
     * @param userId       用户ID
     * @param exportParams 导出参数
     * @return 是否有权限
     */
    @Schema(description = "验证导出权限")
    boolean validateExportPermission(String userId, Map<String, Object> exportParams);

    /**
     * 获取数据提供者名称
     * 用于日志记录和监控
     *
     * @return 提供者名称
     */
    @Schema(description = "获取数据提供者名称")
    default String getProviderName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 获取支持的最大导出记录数
     * 用于限制导出规模，防止系统资源耗尽
     *
     * @return 最大记录数，默认100000
     */
    @Schema(description = "获取支持的最大导出记录数")
    default long getMaxExportCount() {
        return 100000L;
    }
}
