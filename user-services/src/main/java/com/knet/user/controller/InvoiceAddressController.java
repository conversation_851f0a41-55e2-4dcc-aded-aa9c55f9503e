package com.knet.user.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.base.HttpResult;
import com.knet.user.model.dto.req.UserAddressQueryRequest;
import com.knet.user.model.dto.req.UserInvoiceAddressSaveRequest;
import com.knet.user.model.dto.resp.UserInvoiceAddressDtoResp;
import com.knet.user.model.entity.SysUserInvoiceAddress;
import com.knet.user.service.ISysUserInvoiceAddressService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 用户发票地址控制器
 */
@Slf4j
@RestController
@RequestMapping("/invoice-address")
@Tag(name = "用户发票地址管理", description = "用户发票地址管理")
public class InvoiceAddressController {
    @Resource
    private ISysUserInvoiceAddressService sysUserInvoiceAddressService;

    /**
     * 添加用户发票地址
     *
     * @param request 用户发票地址请求体
     * @return 返回创建结果
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Loggable(value = "添加用户发票地址")
    @Operation(summary = "添加用户发票地址")
    @PostMapping("/create")
    public HttpResult<SysUserInvoiceAddress> createInvoiceAddress(@Validated @RequestBody UserInvoiceAddressSaveRequest request) {
        return HttpResult.ok(sysUserInvoiceAddressService.createInvoiceAddress(request));
    }

    /**
     * 查询用户发票地址列表
     *
     * @param request 查询请求
     * @return 发票地址列表
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Loggable(value = "查询用户发票地址列表")
    @Operation(summary = "查询用户发票地址列表")
    @PostMapping("/list")
    public HttpResult<IPage<UserInvoiceAddressDtoResp>> listInvoiceAddress(@Validated @RequestBody UserAddressQueryRequest request) {
        return HttpResult.ok(sysUserInvoiceAddressService.listInvoiceAddress(request));
    }

    /**
     * 删除用户发票地址
     *
     * @param id 发票地址ID
     * @return 删除结果
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Loggable(value = "删除用户发票地址")
    @Operation(summary = "删除用户发票地址")
    @DeleteMapping("/{id}")
    public HttpResult<Void> deleteInvoiceAddress(
            @Parameter(description = "发票地址ID", required = true)
            @PathVariable Long id) {
        sysUserInvoiceAddressService.deleteInvoiceAddress(id);
        return HttpResult.ok();
    }

    /**
     * 修改用户发票地址
     *
     * @param id      发票地址ID
     * @param request 发票地址请求体
     * @return 更新结果
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Loggable(value = "修改用户发票地址")
    @Operation(summary = "修改用户发票地址")
    @PutMapping("/{id}")
    public HttpResult<SysUserInvoiceAddress> updateInvoiceAddress(
            @Parameter(description = "发票地址ID", required = true)
            @PathVariable Long id,
            @Validated @RequestBody UserInvoiceAddressSaveRequest request) {
        return HttpResult.ok(sysUserInvoiceAddressService.updateInvoiceAddress(id, request));
    }
}
