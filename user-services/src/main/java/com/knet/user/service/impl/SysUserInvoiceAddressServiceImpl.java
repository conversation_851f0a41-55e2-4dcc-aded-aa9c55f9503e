package com.knet.user.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.context.UserContext;
import com.knet.common.exception.ServiceException;
import com.knet.user.mapper.SysUserInvoiceAddressMapper;
import com.knet.user.model.dto.req.UserAddressQueryRequest;
import com.knet.user.model.dto.req.UserInvoiceAddressSaveRequest;
import com.knet.user.model.dto.resp.UserInvoiceAddressDtoResp;
import com.knet.user.model.entity.SysUserInvoiceAddress;
import com.knet.user.service.ISysUserInvoiceAddressService;
import com.knet.user.system.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 用户发票地址服务实现类
 */
@Slf4j
@Service
public class SysUserInvoiceAddressServiceImpl extends ServiceImpl<SysUserInvoiceAddressMapper, SysUserInvoiceAddress>
        implements ISysUserInvoiceAddressService {

    @Resource
    private JwtUtil jwtUtil;

    @DistributedLock(key = "'createInvoiceAddress:'+#request.hashCode()", expire = 1)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUserInvoiceAddress createInvoiceAddress(UserInvoiceAddressSaveRequest request) {
        String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
        if (StrUtil.isBlank(userId)) {
            throw new ServiceException("用户未登录");
        }
        request.setUserId(Long.valueOf(userId));
        // 如果设置为默认地址，先取消其他默认地址
        if (request.getIsDefault() != null && request.getIsDefault() == 1) {
            clearDefaultInvoiceAddress(Long.valueOf(userId));
        }
        SysUserInvoiceAddress address = SysUserInvoiceAddress.createInvoiceAddress(request);
        try {
            this.save(address);
        } catch (Exception e) {
            log.error("创建用户发票地址失败", e);
            throw new ServiceException("创建用户发票地址失败");
        }
        return this.getById(address.getId());
    }

    @Override
    public IPage<UserInvoiceAddressDtoResp> listInvoiceAddress(UserAddressQueryRequest request) {
        String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
        if (StrUtil.isBlank(userId)) {
            throw new ServiceException("用户未登录");
        }
        LambdaQueryWrapper<SysUserInvoiceAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(SysUserInvoiceAddress::getUserId, Long.valueOf(userId))
                .orderByDesc(SysUserInvoiceAddress::getIsDefault)
                .orderByDesc(SysUserInvoiceAddress::getCreateTime);
        Page<SysUserInvoiceAddress> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<SysUserInvoiceAddress> invoiceAddressPage = baseMapper.selectPage(page, queryWrapper);
        return invoiceAddressPage.convert(SysUserInvoiceAddress::mapToUserInvoiceAddressDtoResp);
    }

    @DistributedLock(key = "'deleteInvoiceAddress:'+#id", expire = 1)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInvoiceAddress(Long id) {
        if (null == id || id <= 0) {
            throw new ServiceException("发票地址ID不能为空或不合法");
        }
        try {
            String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
            lambdaUpdate()
                    .eq(SysUserInvoiceAddress::getId, id)
                    .eq(SysUserInvoiceAddress::getUserId, userId)
                    .remove();
        } catch (Exception e) {
            log.error("删除用户发票地址失败，id: {}", id, e);
            throw new ServiceException("删除发票地址失败");
        }
    }

    @DistributedLock(key = "'updateInvoiceAddress:'+#id", expire = 1)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUserInvoiceAddress updateInvoiceAddress(Long id, UserInvoiceAddressSaveRequest request) {
        if (null == id || id <= 0) {
            throw new ServiceException("发票地址ID不能为空或不合法");
        }
        String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
        if (StrUtil.isBlank(userId)) {
            throw new ServiceException("用户未登录");
        }
        // 如果设置为默认地址，先取消其他默认地址
        if (request.getIsDefault() != null && request.getIsDefault() == 1) {
            clearDefaultInvoiceAddress(Long.valueOf(userId));
        }
        try {
            SysUserInvoiceAddress address = SysUserInvoiceAddress.createInvoiceAddress(request);
            address.setId(id);
            lambdaUpdate()
                    .eq(SysUserInvoiceAddress::getId, id)
                    .eq(SysUserInvoiceAddress::getUserId, userId)
                    .update(address);
            return this.getById(id);
        } catch (Exception e) {
            log.error("更新用户发票地址失败，id: {}", id, e);
            throw new ServiceException("更新发票地址失败");
        }
    }

    @Override
    public SysUserInvoiceAddress getDefaultInvoiceAddress(Long userId) {
        LambdaQueryWrapper<SysUserInvoiceAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(SysUserInvoiceAddress::getUserId, userId)
                .eq(SysUserInvoiceAddress::getIsDefault, 1)
                .orderByDesc(SysUserInvoiceAddress::getCreateTime)
                .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }


    @Override
    public SysUserInvoiceAddress getOrderInvoiceAddress(Long billAddressId) {
        LambdaQueryWrapper<SysUserInvoiceAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(SysUserInvoiceAddress::getId, billAddressId)
                .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    /**
     * 清除用户的默认发票地址
     */
    private void clearDefaultInvoiceAddress(Long userId) {
        LambdaUpdateWrapper<SysUserInvoiceAddress> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(SysUserInvoiceAddress::getUserId, userId)
                .set(SysUserInvoiceAddress::getIsDefault, 0);
        this.update(updateWrapper);
    }
}
