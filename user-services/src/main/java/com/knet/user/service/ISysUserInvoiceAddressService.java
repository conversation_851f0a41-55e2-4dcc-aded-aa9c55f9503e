package com.knet.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.user.model.dto.req.UserAddressQueryRequest;
import com.knet.user.model.dto.req.UserInvoiceAddressSaveRequest;
import com.knet.user.model.dto.resp.UserInvoiceAddressDtoResp;
import com.knet.user.model.entity.SysUserInvoiceAddress;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 针对表【sys_user_invoice_address(用户发票地址表)】的数据库操作Service
 */
public interface ISysUserInvoiceAddressService extends IService<SysUserInvoiceAddress> {

    /**
     * 创建用户发票地址
     *
     * @param request 用户发票地址请求体
     * @return 返回创建结果
     */
    SysUserInvoiceAddress createInvoiceAddress(UserInvoiceAddressSaveRequest request);

    /**
     * 查询用户发票地址列表
     *
     * @param request 查询请求
     * @return 发票地址列表
     */
    IPage<UserInvoiceAddressDtoResp> listInvoiceAddress(UserAddressQueryRequest request);

    /**
     * 删除用户发票地址
     *
     * @param id 发票地址id
     */
    void deleteInvoiceAddress(Long id);

    /**
     * 修改用户发票地址
     *
     * @param id      发票地址id
     * @param request 用户发票地址请求体
     * @return 更新后的发票地址
     */
    SysUserInvoiceAddress updateInvoiceAddress(Long id, UserInvoiceAddressSaveRequest request);

    /**
     * 根据用户ID获取默认发票地址
     *
     * @param userId 用户ID
     * @return 默认发票地址
     */
    SysUserInvoiceAddress getDefaultInvoiceAddress(Long userId);

    /**
     * 根据账单地址ID获取发票地址
     *
     * @param billAddressId 账单地址ID
     * @return 发票地址
     */
    SysUserInvoiceAddress getOrderInvoiceAddress(Long billAddressId);
}
