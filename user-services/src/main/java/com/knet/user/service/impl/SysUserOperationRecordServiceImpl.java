package com.knet.user.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.OperationResult;
import com.knet.common.enums.UserOperationType;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RandomStrUtil;
import com.knet.user.mapper.SysUserOperationRecordMapper;
import com.knet.user.model.dto.req.CreateUserOperationRecordRequest;
import com.knet.user.model.dto.req.UserQueryRequest;
import com.knet.user.model.dto.req.UserRechargeQueryRequest;
import com.knet.user.model.dto.resp.UserInfoDtoResp;
import com.knet.user.model.entity.SysUserOperationRecord;
import com.knet.user.service.ISysUserOperationRecordService;
import com.knet.user.service.IUserQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 针对表【sys_user_operation_record(用户操作记录表)】的数据库操作Service实现
 * @date 2025-05-20 18:40:00
 */
@Slf4j
@Service
public class SysUserOperationRecordServiceImpl extends ServiceImpl<SysUserOperationRecordMapper, SysUserOperationRecord> implements ISysUserOperationRecordService {

    @Resource
    private RandomStrUtil randomStrUtil;
    @Resource
    private IUserQueryService userQueryService;

    @Override
    public String createOperationRecord(CreateUserOperationRecordRequest request) {
        log.info("创建用户操作记录: userId={}, operationType={}, operationResult={}",
                request.getUserId(), request.getOperationType(), request.getOperationResult());
        String operationId = randomStrUtil.getOperatedId();
        SysUserOperationRecord record = SysUserOperationRecord.builder()
                .operationId(operationId)
                .userId(request.getUserId())
                .operatorId(request.getOperatorId())
                .operatorType(request.getOperatorType())
                .operationType(request.getOperationType())
                .operationResult(request.getOperationResult())
                .operationDesc(request.getOperationDesc())
                .operationDetail(request.getOperationDetail())
                .clientIp(request.getClientIp())
                .userAgent(request.getUserAgent())
                .businessId(request.getBusinessId())
                .businessType(request.getBusinessType())
                .errorMessage(request.getErrorMessage())
                .remarks(request.getRemarks())
                .build();
        boolean saved = this.save(record);
        if (!saved) {
            log.error("用户操作记录保存失败: operationId={}", operationId);
            throw new ServiceException("用户操作记录保存失败");
        }
        log.info("用户操作记录创建成功: operationId={}", operationId);
        return operationId;
    }

    @Override
    public String createOperationRecord(Long userId, Long operatorId, String operatorType,
                                        UserOperationType operationType, OperationResult operationResult,
                                        String operationDesc, String businessId, String businessType) {
        CreateUserOperationRecordRequest request = CreateUserOperationRecordRequest.builder()
                .userId(userId)
                .operatorId(operatorId)
                .operatorType(operatorType)
                .operationType(operationType)
                .operationResult(operationResult)
                .operationDesc(operationDesc)
                .businessId(businessId)
                .businessType(businessType)
                .build();
        return createOperationRecord(request);
    }

    @Override
    public String createOperationRecord(Long userId, UserOperationType operationType,
                                        OperationResult operationResult, String operationDesc) {
        return createOperationRecord(userId, userId, "USER", operationType, operationResult,
                operationDesc, null, null);
    }

    @Override
    public IPage<SysUserOperationRecord> queryList(UserRechargeQueryRequest request) {
        LambdaQueryWrapper<SysUserOperationRecord> queryWrapper = new LambdaQueryWrapper<>();
        if (request.getStartTime() != null && request.getEndTime() != null) {
            queryWrapper.between(SysUserOperationRecord::getCreateTime, request.getStartTime(), request.getEndTime());
        }
        if (StrUtil.isNotBlank(request.getCashNumber())) {
            queryWrapper.like(SysUserOperationRecord::getOperationId, request.getCashNumber());
        }
        if (StrUtil.isNotBlank(request.getAccount())) {
            UserQueryRequest queryRequest = new UserQueryRequest();
            queryRequest.setAccount(request.getAccount());
            IPage<UserInfoDtoResp> listUser = userQueryService.listUser(queryRequest);
            if (listUser.getTotal() == 0) {
                return new Page<>();
            }
            queryWrapper.in(SysUserOperationRecord::getUserId, listUser.getRecords().stream().map(UserInfoDtoResp::getId).toList());
        }
        if (StrUtil.isNotBlank(request.getType())) {
            switch (request.getType()) {
                case "WALLET_RECHARGE" ->
                        queryWrapper.eq(SysUserOperationRecord::getOperationType, UserOperationType.WALLET_RECHARGE);
                case "WALLET_DEDUCT" ->
                        queryWrapper.eq(SysUserOperationRecord::getOperationType, UserOperationType.WALLET_DEDUCT);
                default ->
                        queryWrapper.in(SysUserOperationRecord::getOperationType, UserOperationType.WALLET_RECHARGE, UserOperationType.WALLET_DEDUCT);
            }
        } else {
            queryWrapper.in(SysUserOperationRecord::getOperationType, UserOperationType.WALLET_RECHARGE, UserOperationType.WALLET_DEDUCT);
        }
        queryWrapper.orderByDesc(SysUserOperationRecord::getId);
        Page<SysUserOperationRecord> page = new Page<>(request.getPageNo(), request.getPageSize());
        return baseMapper.selectPage(page, queryWrapper);
    }
}
