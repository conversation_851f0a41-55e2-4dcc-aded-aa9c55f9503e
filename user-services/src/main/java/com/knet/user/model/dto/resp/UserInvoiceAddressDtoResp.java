package com.knet.user.model.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 用户发票地址信息返回体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UserInvoiceAddressDtoResp extends BaseResponse {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    @Schema(description = "收件人全名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fullName;

    @Schema(description = "国家名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String country;

    @Schema(description = "地址行1（街道信息）")
    private String addressLine1;

    @Schema(description = "地址行2（补充信息）")
    private String addressLine2;

    @Schema(description = "城市名称")
    private String city;

    @Schema(description = "州/省名称")
    private String state;

    @Schema(description = "邮政编码")
    private String zipCode;

    @Schema(description = "国际电话区号(如86/852)")
    private String phonePrefix;

    @Schema(description = "本地电话号码(不含国际区号)")
    private String phoneNumber;

    @Schema(description = "收件人邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    private String email;

    @Schema(description = "是否为默认发票地址：0-否，1-是")
    private Integer isDefault;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date updateTime;
}
