package com.knet.user.model.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BasePageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/5 14:30
 * @description: UserRechargeQueryRequest
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserRechargeQueryRequest extends BasePageRequest {

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String account;

    @Schema(description = "钱包充值/扣款类型 查询全部不传值", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "WALLET_RECHARGE WALLET_DEDUCT")
    private String type;

    @Schema(description = "交易流水号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String cashNumber;

    @Schema(description = "开始时间", example = "2025-09-01 00:00:00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "结束时间", example = "2025-09-02 23:59:59", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
