package com.knet.user.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 用户发票地址保存请求体
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserInvoiceAddressSaveRequest extends BaseRequest {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long userId;

    @Schema(description = "收件人全名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotBlank(message = "收件人全名不能为空")
    @Size(max = 100, message = "收件人全名不能超过100个字符")
    private String fullName;

    @Schema(description = "国家名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotBlank(message = "国家名称不能为空")
    @Size(max = 50, message = "国家名称不能超过50个字符")
    private String country;

    @Schema(description = "地址行1（街道信息）")
    @Size(max = 200, message = "地址行1不能超过200个字符")
    private String addressLine1;

    @Schema(description = "地址行2（补充信息）")
    @Size(max = 200, message = "地址行2不能超过200个字符")
    private String addressLine2;

    @Schema(description = "城市名称")
    @Size(max = 50, message = "城市名称不能超过50个字符")
    private String city;

    @Schema(description = "州/省名称")
    @Size(max = 50, message = "州/省名称不能超过50个字符")
    private String state;

    @Schema(description = "邮政编码")
    @Size(max = 20, message = "邮政编码不能超过20个字符")
    private String zipCode;

    @Schema(description = "是否为默认发票地址：0-否，1-是")
    private Integer isDefault = 1;
}
