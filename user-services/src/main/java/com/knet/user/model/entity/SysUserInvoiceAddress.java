package com.knet.user.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.user.model.dto.req.UserInvoiceAddressSaveRequest;
import com.knet.user.model.dto.resp.UserInvoiceAddressDtoResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 用户发票地址表
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_user_invoice_address", description = "用户发票地址表")
@TableName("sys_user_invoice_address")
public class SysUserInvoiceAddress extends BaseEntity {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    /**
     * 收件人全名
     */
    @Schema(description = "收件人全名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fullName;

    /**
     * 国家名称
     */
    @Schema(description = "国家名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String country;

    /**
     * 地址行1（街道信息）
     */
    @Schema(description = "地址行1（街道信息）")
    private String addressLine1;

    /**
     * 地址行2（补充信息）
     */
    @Schema(description = "地址行2（补充信息）")
    private String addressLine2;

    /**
     * 城市名称
     */
    @Schema(description = "城市名称")
    private String city;

    /**
     * 州/省名称
     */
    @Schema(description = "州/省名称")
    private String state;

    /**
     * 邮政编码
     */
    @Schema(description = "邮政编码")
    private String zipCode;

    /**
     * 国际电话区号(如86/852)
     */
    @Schema(description = "国际电话区号(如86/852)")
    private String phonePrefix;

    /**
     * 本地电话号码(不含国际区号)
     */
    @Schema(description = "本地电话号码(不含国际区号)")
    private String phoneNumber;

    /**
     * 收件人邮箱
     */
    @Schema(description = "收件人邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    private String email;

    /**
     * 是否为默认发票地址：0-否，1-是
     */
    @Schema(description = "是否为默认发票地址：0-否，1-是")
    private Integer isDefault;

    /**
     * 完整手机号
     */
    public String getFullPhoneNumber() {
        return "+" + this.phonePrefix + this.phoneNumber;
    }

    /**
     * 创建发票地址
     */
    public static SysUserInvoiceAddress createInvoiceAddress(UserInvoiceAddressSaveRequest request) {
        return SysUserInvoiceAddress.builder()
                .userId(request.getUserId())
                .fullName(request.getFullName())
                .country(request.getCountry())
                .addressLine1(request.getAddressLine1())
                .addressLine2(request.getAddressLine2())
                .city(request.getCity())
                .state(request.getState())
                .zipCode(request.getZipCode())
                .isDefault(request.getIsDefault())
                .build();
    }

    /**
     * 转换为响应对象
     */
    public UserInvoiceAddressDtoResp mapToUserInvoiceAddressDtoResp() {
        return UserInvoiceAddressDtoResp.builder()
                .id(this.getId())
                .userId(this.getUserId())
                .fullName(this.getFullName())
                .country(this.getCountry())
                .addressLine1(this.getAddressLine1())
                .addressLine2(this.getAddressLine2())
                .city(this.getCity())
                .state(this.getState())
                .zipCode(this.getZipCode())
                .phonePrefix(this.getPhonePrefix())
                .phoneNumber(this.getPhoneNumber())
                .email(this.getEmail())
                .isDefault(this.getIsDefault())
                .createTime(this.getCreateTime())
                .updateTime(this.getUpdateTime())
                .build();
    }
}
