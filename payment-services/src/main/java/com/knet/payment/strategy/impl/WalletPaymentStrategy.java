package com.knet.payment.strategy.impl;

import com.knet.common.enums.KnetPaymentFlowStatus;
import com.knet.common.enums.PaymentChannel;
import com.knet.common.enums.WalletRecordType;
import com.knet.common.exception.PaymentException;
import com.knet.payment.model.dto.req.CreatePaymentRequest;
import com.knet.payment.model.dto.resp.CreatePaymentResponse;
import com.knet.payment.model.entity.SysPaymentFlow;
import com.knet.payment.service.ISysPaymentFlowService;
import com.knet.payment.service.ISysPaymentGroupService;
import com.knet.payment.service.ISysWalletRecordService;
import com.knet.payment.service.IWalletOperationService;
import com.knet.payment.strategy.PaymentStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/3 13:05
 * @description: 钱包支付策略
 */
@Slf4j
@Component
public class WalletPaymentStrategy implements PaymentStrategy {
    @Resource
    private IWalletOperationService walletOperationService;
    @Resource
    private ISysWalletRecordService walletRecordService;
    @Resource
    private ISysPaymentFlowService paymentFlowService;
    @Resource
    private ISysPaymentGroupService paymentGroupService;

    @Override
    public PaymentChannel getSupportedChannel() {
        return PaymentChannel.WALLET;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CreatePaymentResponse processPayment(SysPaymentFlow paymentFlow,
                                                CreatePaymentRequest request,
                                                CreatePaymentResponse.CreatePaymentResponseBuilder responseBuilder) {
        Long userId = request.getUserId();
        BigDecimal amount = paymentFlow.getAmount();
        String recordId;
        log.info("处理钱包支付: userId={}, amount={}", userId, amount);
        try {
            boolean updated = walletOperationService.deductBalance(userId, amount);
            if (!updated) {
                // 余额不足属于系统错误，需要更新订单状态
                log.error("钱包余额不足或扣减失败: userId={}, amount={}", userId, amount);
                throw PaymentException.systemError("Insufficient wallet balance or failed deduction");
            }
            recordId = walletRecordService.createWalletRecord(
                    userId, amount, WalletRecordType.PAYMENT_DEDUCTION,
                    paymentFlow.getPaymentId(), request.getOrderId(), request.getRemark());
            paymentFlowService.paySuccess(paymentFlow.getPaymentId());
            paymentGroupService.updatePaymentGroupStatus(paymentFlow.getGroupId(), amount);
        } catch (PaymentException e) {
            // 重新抛出 PaymentException，保持异常类型
            throw e;
        } catch (Exception e) {
            // 其他系统异常
            throw PaymentException.systemError("处理支付异常，userId=" + userId + "失败:" + e.getMessage(), e);
        }
        log.info("钱包支付成功: userId={}, amount={}, recordId={}", userId, amount, recordId);
        return responseBuilder.status(KnetPaymentFlowStatus.SUCCESS.getName()).build();
    }
}
