package com.knet.payment.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.payment.model.third.resp.UserInfoDtoResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/4 16:00
 * @description: 用户服务API客户端
 */
@FeignClient(name = "user-services", path = "userServices/api")
public interface ApiUserServiceProvider {

    /**
     * 批量获取用户信息
     *
     * @param userIds 用户ID列表
     * @return 用户信息列表
     */
    @PostMapping("/users/batch")
    HttpResult<List<UserInfoDtoResp>> getUsersByIds(@RequestBody List<Long> userIds);

    /**
     * 根据账户名模糊搜索获取用户ID列表
     *
     * @param account 账户名（模糊搜索）
     * @return 用户ID列表
     */
    @PostMapping("/users/userIds-by-account")
    HttpResult<List<Long>> getUserIdsByAccount(@RequestParam("account") String account);
}
