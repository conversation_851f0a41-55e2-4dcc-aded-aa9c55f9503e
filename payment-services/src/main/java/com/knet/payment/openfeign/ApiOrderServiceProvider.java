package com.knet.payment.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.payment.model.third.resp.SubOrderGroupDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2025/6/10 17:37
 * @description: 订单服务提供者- API
 */
@FeignClient(name = "order-services", path = "orderService/api")
public interface ApiOrderServiceProvider {

    /**
     * 根据订单ID获取 订单信息
     *
     * @param prentOrderId 订单ID
     * @return 订单信息
     */
    @GetMapping("/order/group/{prentOrderId}")
    @Operation(summary = "根据订单ID获取订单信息", description = "供其他服务调用，获取订单信息")
    HttpResult<SubOrderGroupDto> getOrderGroupByOrderId(
            @Parameter(description = "订单ID", required = true, example = "ORC-123456789012345678")
            @PathVariable("prentOrderId") String prentOrderId);
}
