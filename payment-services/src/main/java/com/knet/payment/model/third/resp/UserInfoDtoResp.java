package com.knet.payment.model.third.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import com.knet.common.enums.LanguageEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/8/7 10:00
 * @description: 用户信息返回体（第三方服务响应）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UserInfoDtoResp extends BaseResponse {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "createTime", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date createTime;

    @Schema(description = "updateTime", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date updateTime;

    @Schema(description = "uid", requiredMode = Schema.RequiredMode.REQUIRED)
    private String uid;

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String account;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "邮箱")
    private String email;

    /**
     * @see LanguageEnum 语言
     */
    @Schema(description = "语言", requiredMode = Schema.RequiredMode.REQUIRED)
    private LanguageEnum language;
}
