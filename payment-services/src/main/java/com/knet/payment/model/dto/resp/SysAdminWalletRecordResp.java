package com.knet.payment.model.dto.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/9/9 14:24
 * @description: 管理员查询用户钱包记录resp
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysAdminWalletRecordResp extends SysWalletRecordResp {
    /**
     * 用户账户
     */
    @Schema(description = "用户账户", requiredMode = Schema.RequiredMode.REQUIRED)
    private String account;

    @Schema(description = "备注", example = "备注信息")
    private String remarks;

    @Schema(description = "子订单号订单号")
    private String itemNo;

    @Schema(description = "关联父订单号")
    private String parentOrderId;
}
