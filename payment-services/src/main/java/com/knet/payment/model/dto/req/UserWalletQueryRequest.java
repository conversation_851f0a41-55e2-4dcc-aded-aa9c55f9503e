package com.knet.payment.model.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BasePageRequest;
import com.knet.common.enums.WalletSearchType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/20 15:17
 * @description: UserAddressQueryRequest
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserWalletQueryRequest extends BasePageRequest {
    @Schema(description = "userId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long userId;
    /**
     * @see WalletSearchType
     */
    @Schema(description = "交易类型,查询全部不传值")
    private WalletSearchType walletSearchType;

    @Schema(description = "用户账号（模糊搜索）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String account;

    @Schema(description = "订单号（支持父订单ID或子订单ID模糊搜索）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String orderId;

    @Schema(description = "记录ID（模糊搜索）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String recordId;

    @Schema(description = "开始时间", example = "2025-09-01 00:00:00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "结束时间", example = "2025-09-02 23:59:59", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
