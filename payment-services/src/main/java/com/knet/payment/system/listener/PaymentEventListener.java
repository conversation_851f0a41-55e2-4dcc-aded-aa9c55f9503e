package com.knet.payment.system.listener;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.PaymentResultMessage;
import com.knet.payment.model.entity.SysPaymentGroup;
import com.knet.payment.mq.producer.PaymentMessageProducer;
import com.knet.payment.system.event.PaymentEvent;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/11 16:12
 * @description: 支付事件监听器
 */
@Component
public class PaymentEventListener {
    @Resource
    private PaymentMessageProducer paymentMessageProducer;
    /**
     * 支付成功状态
     */
    private final static String SUCCESS = "SUCCESS";

    /**
     * 处理支付事件
     *
     * @param event 支付事件
     */
    @TransactionalEventListener(classes = PaymentEvent.class, phase = TransactionPhase.AFTER_COMPLETION)
    public void handlePaymentEventListener(PaymentEvent event) {
        SysPaymentGroup paymentGroup = event.getPaymentGroup();
        PaymentResultMessage message = PaymentResultMessage
                .builder()
                .paymentGroupId(paymentGroup.getGroupId())
                .userId(paymentGroup.getUserId())
                .orderId(paymentGroup.getOrderId())
                .status(event.getStatus())
                .resultTime(paymentGroup.getUpdateTime().getTime())
                .build();
        paymentMessageProducer.sendPaymentResultMessage(JSON.toJSONString(message));
    }
}
