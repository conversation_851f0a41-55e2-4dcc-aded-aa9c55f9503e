#1.依赖的环境：
FROM openjdk:17-jdk-slim

# 将工作目录设置为 /app
WORKDIR /app

# 安装 Google Chrome 和必要的依赖
RUN apt-get update && apt-get install -y \
    wget \
    gnupg2 \
    ca-certificates \
    && wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y \
    google-chrome-stable \
    xvfb \
    fonts-liberation \
    fonts-noto \
    fonts-noto-cjk \
    fonts-noto-color-emoji \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libx11-xcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libxss1 \
    libgbm1 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置Chrome相关环境变量
ENV DISPLAY=:99
ENV CHROME_BIN=/usr/bin/google-chrome
ENV CHROME_PATH=/usr/bin/google-chrome

# 创建虚拟显示启动脚本
RUN echo '#!/bin/bash\n\
# 启动虚拟显示\n\
Xvfb :99 -screen 0 1024x768x24 -ac +extension GLX +render -noreset &\n\
\n\
# 等待虚拟显示启动\n\
sleep 2\n\
\n\
# 测试Chrome是否能正常工作\n\
/usr/bin/google-chrome --headless --disable-gpu --no-sandbox --print-to-pdf=/tmp/test.pdf --virtual-time-budget=5000 data:text/html,Hello\n\
\n\
if [ -f "/tmp/test.pdf" ] && [ -s "/tmp/test.pdf" ]; then\n\
    echo "Chrome PDF generation test successful"\n\
    rm -f /tmp/test.pdf\n\
else\n\
    echo "Chrome PDF generation test failed"\n\
    exit 1\n\
fi\n\
\n\
# 启动Java应用\n\
exec java -Djava.awt.headless=true --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED -jar notification-service.jar' > /start.sh \
    && chmod +x /start.sh

#2.定义作者信息：
LABEL maintainer="<EMAIL>"
LABEL org.opencontainers.image.title="notification-service"
LABEL org.opencontainers.image.version="1.0.0"

#3.将jar包添加到容器（将jar包存入镜像中）：其他项目参考本文件
ADD ./target/notification-services-1.0-SNAPSHOT.jar notification-service.jar

#4.指定这个容器对外暴露的端口号
EXPOSE 7006

# 使用启动脚本启动容器
CMD ["/start.sh"]