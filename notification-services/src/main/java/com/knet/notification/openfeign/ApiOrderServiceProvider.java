package com.knet.notification.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.notification.model.dto.third.resp.OrderDetailDtoResp;
import com.knet.notification.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;
import com.knet.notification.system.handler.ApiOrderServiceFallbackImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 订单服务API客户端
 */
@FeignClient(name = "order-services", path = "orderService/api", fallback = ApiOrderServiceFallbackImpl.class)
public interface ApiOrderServiceProvider {

    /**
     * 获取订单详情
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    @GetMapping("/order/detail/{orderId}")
    @Operation(summary = "获取订单详情", description = "供其他服务调用，获取订单详细信息")
    HttpResult<OrderDetailDtoResp> getOrderDetail(
            @Parameter(description = "订单ID", required = true)
            @PathVariable("orderId") String orderId);

    /**
     * 获取发票快照
     *
     * @param invoiceNumber 发票编号
     * @return 发票快照
     */
    @GetMapping("/invoice/snapshot/number/{invoiceNumber}")
    @Operation(summary = "获取发票快照", description = "供其他服务调用，获取发票快照信息")
    HttpResult<OrderInvoiceSnapshotDtoResp> getInvoiceSnapshot(
            @Parameter(description = "发票编号", required = true)
            @PathVariable("invoiceNumber") String invoiceNumber);

    /**
     * 更新发票PDF地址
     *
     * @param invoiceNumber 发票编号
     * @param pdfUrl        PDF地址
     * @return 更新结果
     */
    @PutMapping("/invoice/pdf-url")
    @Operation(summary = "更新发票PDF地址", description = "供其他服务调用，更新发票PDF地址")
    HttpResult<Void> updateInvoicePdfUrl(
            @Parameter(description = "发票编号", required = true)
            @RequestParam("invoiceNumber") String invoiceNumber,
            @Parameter(description = "PDF地址", required = true)
            @RequestParam("pdfUrl") String pdfUrl);

    /**
     * 更新发票状态
     *
     * @param invoiceNumber 发票编号
     * @param status        发票状态
     * @return 更新结果
     */
    @PutMapping("/invoice/status")
    @Operation(summary = "更新发票状态", description = "供其他服务调用，更新发票状态")
    HttpResult<Void> updateInvoiceStatus(
            @Parameter(description = "发票编号", required = true)
            @RequestParam("invoiceNumber") String invoiceNumber,
            @Parameter(description = "发票状态", required = true)
            @RequestParam("status") Integer status);

    /**
     * 获取失败的发票编号列表
     *
     * @return 失败的发票编号列表
     */
    @GetMapping("/invoice/failed")
    @Operation(summary = "获取失败的发票编号列表", description = "获取PDF地址为空或状态为失败的发票编号列表")
    HttpResult<List<String>> getFailedInvoiceNumbers();

    /**
     * 根据订单ID获取发票快照
     * 使用OpenFeign配置的重试机制，当网络异常或超时时自动重试
     *
     * @param orderId 订单ID
     * @return 发票快照信息
     */
    @GetMapping("/invoice/snapshot/orderId/{orderId}")
    @Operation(summary = "根据订单ID获取发票快照", description = "获取订单对应的发票快照信息")
    HttpResult<OrderInvoiceSnapshotDtoResp> getInvoiceSnapshotByOrderId(@PathVariable("orderId") String orderId);

    /**
     * 根据订单ID删除发票快照
     *
     * @param orderId 订单ID
     * @return 删除结果
     */
    @DeleteMapping("/invoice/snapshot/delete/{orderId}")
    @Operation(summary = "根据订单ID删除发票快照", description = "订单取消时删除对应的发票快照")
    boolean deleteInvoiceSnapshotByOrderId(@PathVariable("orderId") String orderId);
}
