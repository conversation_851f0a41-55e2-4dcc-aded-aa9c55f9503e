package com.knet.notification.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.notification.model.dto.third.resp.UserInfoDtoResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 用户服务API客户端
 */
@FeignClient(name = "user-services", path = "userServices/api")
public interface ApiUserServiceProvider {

    /**
     * 根据用户ID获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "根据用户ID获取用户信息", description = "供其他服务调用，获取用户详细信息")
    HttpResult<UserInfoDtoResp> getUserById(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable("userId") Long userId);
}
