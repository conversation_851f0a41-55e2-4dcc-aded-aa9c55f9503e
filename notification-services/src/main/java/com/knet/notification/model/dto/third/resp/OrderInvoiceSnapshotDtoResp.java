package com.knet.notification.model.dto.third.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 订单发票快照返回体（第三方调用）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class OrderInvoiceSnapshotDtoResp extends BaseResponse {

    @Schema(description = "发票编号")
    private String invoiceNumber;

    @Schema(description = "订单ID")
    private String orderId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "发票日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date invoiceDate;

    @Schema(description = "到期日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date dueDate;

    @Schema(description = "发票总金额")
    private BigDecimal totalAmount;

    @Schema(description = "账单地址信息（JSON格式）")
    private String billToAddress;

    @Schema(description = "收货地址信息（JSON格式）")
    private String shipToAddress;

    @Schema(description = "订单商品明细（JSON格式）")
    private String orderItems;

    @Schema(description = "发票PDF文件S3地址")
    private String invoicePdfUrl;

    @Schema(description = "发票状态：0-待生成，1-已生成，2-已发送，3-发送失败")
    private Integer invoiceStatus;
}
