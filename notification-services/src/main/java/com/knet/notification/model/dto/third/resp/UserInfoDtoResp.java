package com.knet.notification.model.dto.third.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import com.knet.common.enums.LanguageEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 用户信息返回体（第三方调用）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UserInfoDtoResp extends BaseResponse {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "createTime", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date createTime;

    @Schema(description = "updateTime", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date updateTime;

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String account;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String username;

    @Schema(description = "邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    private String email;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String phone;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "语言偏好")
    private LanguageEnum language;

    @Schema(description = "用户状态：0-禁用，1-启用")
    private Integer status;

    /**
     * 获取全名（使用昵称或用户名）
     */
    public String getFullName() {
        if (nickname != null && !nickname.trim().isEmpty()) {
            return nickname;
        }
        return username;
    }
}
