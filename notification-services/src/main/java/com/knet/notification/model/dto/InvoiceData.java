package com.knet.notification.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 发票数据模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceData {

    @Schema(description = "发票编号")
    private String invoiceNumber;

    @Schema(description = "发票日期")
    private String invoiceDate;

    @Schema(description = "到期日期")
    private String dueDate;

    @Schema(description = "总金额")
    private String totalAmount;

    @Schema(description = "账单地址")
    private AddressInfo billTo;

    @Schema(description = "收货地址")
    private AddressInfo shipTo;

    @Schema(description = "商品明细列表")
    private List<InvoiceItem> items;

    @Schema(description = "账单邮箱地址")
    private String billEmails;

    /**
     * 地址信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddressInfo {
        @Schema(description = "收件人")
        private String recipient;

        @Schema(description = "街道地址")
        private String street;

        @Schema(description = "城市")
        private String city;

        @Schema(description = "州/省")
        private String state;

        @Schema(description = "邮编")
        private String zipCode;

        @Schema(description = "国家")
        private String country;
    }

    /**
     * 发票商品明细
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InvoiceItem {
        @Schema(description = "序号")
        private Integer index;

        @Schema(description = "商品名称")
        private String productName;

        @Schema(description = "SKU")
        private String sku;

        @Schema(description = "尺码")
        private String size;

        @Schema(description = "数量")
        private Integer quantity;

        @Schema(description = "金额")
        private String amount;
    }
}
