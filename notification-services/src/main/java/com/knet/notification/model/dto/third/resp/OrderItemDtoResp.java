package com.knet.notification.model.dto.third.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 订单商品明细返回体（第三方调用）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class OrderItemDtoResp extends BaseResponse {

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "SKU")
    private String sku;

    @Schema(description = "尺码")
    private String size;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "金额")
    private BigDecimal amount;
}
