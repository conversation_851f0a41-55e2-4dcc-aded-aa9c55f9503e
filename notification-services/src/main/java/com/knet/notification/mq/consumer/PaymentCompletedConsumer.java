package com.knet.notification.mq.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.knet.common.base.HttpResult;
import com.knet.common.dto.message.PaymentResultMessage;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.notification.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;
import com.knet.notification.service.IInvoiceEmailService;
import com.knet.notification.service.impl.OrderServiceRetryWrapper;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 支付完成消息消费者
 */
@Slf4j
@Component
public class PaymentCompletedConsumer {

    @Resource
    private IInvoiceEmailService invoiceEmailService;
    @Resource
    private OrderServiceRetryWrapper orderServiceRetryWrapper;

    /**
     * 监听支付完成消息，发送发票邮件
     * 使用专用的容器工厂，限制同时最大处理5条消息
     */
    @RabbitListener(
            queues = "payment-result-queue.notification-services",
            ackMode = "MANUAL",
            containerFactory = "paymentCompletedListenerContainerFactory"
    )
    public void handlePaymentCompleted(String messageBody, Message message, Channel channel) throws IOException {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        String messageId = (String) message.getMessageProperties().getHeaders().get("messageId");
        String routingKey = (String) message.getMessageProperties().getHeaders().get("routingKey");
        log.info("开始处理支付完成消息: messageId={}, deliveryTag={}", messageId, deliveryTag);
        try {
            if ("payment.result".equals(routingKey)) {
                // 幂等性检查 - 使用notification服务特定的前缀
                String notificationMessageId = "NOTIFICATION_" + messageId;
                if (!RedisCacheUtil.setIfAbsent("NOTIFICATION_PAYMENT_PROCESSED:" + notificationMessageId, "PROCESSED", 60)) {
                    log.warn("notification服务重复消息: {}", notificationMessageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                log.info("收到支付结果消息: {}", messageBody);
                PaymentResultMessage paymentResultMessage = JSON.parseObject(messageBody, PaymentResultMessage.class);
                String orderId = paymentResultMessage.getOrderId();
                Long userId = paymentResultMessage.getUserId();
                String status = paymentResultMessage.getStatus();
                if (!"SUCCESS".equals(status)) {
                    log.info("支付状态非成功，跳过发票邮件发送: orderId={}, status={}", orderId, status);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                if (StrUtil.isBlank(orderId) || BeanUtil.isEmpty(userId)) {
                    log.error("支付完成消息参数不完整: orderId={}, userId={}", orderId, userId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                String invoiceNumber = null;
                try {
                    HttpResult<OrderInvoiceSnapshotDtoResp> invoiceResult = orderServiceRetryWrapper.getInvoiceSnapshotByOrderIdWithRetry(orderId);
                    if (invoiceResult != null && invoiceResult.success() && invoiceResult.getData() != null) {
                        OrderInvoiceSnapshotDtoResp invoiceSnapshot = invoiceResult.getData();
                        invoiceNumber = invoiceSnapshot.getInvoiceNumber();
                        invoiceEmailService.sendOrderConfirmationEmail(orderId, userId, invoiceNumber, invoiceSnapshot);
                    } else {
                        log.error("获取订单发票快照失败，跳过邮件发送: orderId={}", orderId);
                    }
                } catch (Exception ex) {
                    log.error("获取订单发票快照异常，跳过邮件发送: orderId={}, error={}", orderId, ex.getMessage(), ex);
                }
                channel.basicAck(deliveryTag, false);
                log.info("支付结果发票邮件处理成功: orderId={}, invoiceNumber={}", orderId, invoiceNumber);
            }
        } catch (Exception e) {
            log.error("处理支付结果消息失败: messageBody={}, error={}", messageBody, e.getMessage(), e);
            try {
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("消息拒绝失败: {}", ex.getMessage());
            }
        } finally {
            log.info("支付完成消息处理结束: messageId={}, deliveryTag={}", messageId, deliveryTag);
        }
    }
}
