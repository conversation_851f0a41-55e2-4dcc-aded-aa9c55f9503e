package com.knet.notification.system.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.knet.notification.model.dto.InvoiceData;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 发票数据解析工具类
 */
@Slf4j
public class InvoiceDataParseUtil {

    /**
     * 解析地址信息
     */
    public static InvoiceData.AddressInfo parseAddressInfo(String addressJson) {
        try {
            if (StrUtil.isBlank(addressJson)) {
                return null;
            }
            return JSON.parseObject(addressJson, InvoiceData.AddressInfo.class);
        } catch (Exception e) {
            log.warn("解析地址信息失败: addressJson={}, error={}", addressJson, e.getMessage());
            return InvoiceData.AddressInfo.builder().build();
        }
    }

    /**
     * 解析订单项数据
     */
    public static List<InvoiceData.InvoiceItem> parseOrderItems(String orderItemsJson) {
        List<InvoiceData.InvoiceItem> items = new ArrayList<>();
        try {
            if (StrUtil.isBlank(orderItemsJson)) {
                return items;
            }
            List<?> rawList = JSON.parseArray(orderItemsJson);
            if (rawList != null) {
                for (int i = 0; i < rawList.size(); i++) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> itemMap = (Map<String, Object>) rawList.get(i);
                    // 获取kgOwningPrice作为发票显示价格（已经是元，不需要转换）
                    Object kgOwningPriceObj = itemMap.get("kgOwningPrice");
                    String displayPrice = "0.00";
                    if (kgOwningPriceObj != null) {
                        displayPrice = kgOwningPriceObj.toString();
                    }
                    InvoiceData.InvoiceItem invoiceItem = InvoiceData.InvoiceItem.builder()
                            .index(i + 1)
                            .productName(getStringValue(itemMap, "name"))
                            .sku(getStringValue(itemMap, "sku"))
                            .size(getStringValue(itemMap, "size"))
                            .quantity(getIntValue(itemMap, "count"))
                            .amount(displayPrice)
                            .build();
                    items.add(invoiceItem);
                }
            }
        } catch (Exception e) {
            log.error("解析订单项数据失败: orderItemsJson={}, error={}", orderItemsJson, e.getMessage(), e);
        }
        return items;
    }

    /**
     * 安全获取字符串值
     */
    private static String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    /**
     * 安全获取整数值
     */
    private static Integer getIntValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value != null) {
            try {
                return Integer.valueOf(value.toString());
            } catch (NumberFormatException e) {
                log.warn("转换整数失败: key={}, value={}", key, value);
            }
        }
        return 1;
    }
}
