package com.knet.notification.system.handler;

import com.knet.common.base.HttpResult;
import com.knet.notification.model.dto.third.resp.OrderDetailDtoResp;
import com.knet.notification.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;
import com.knet.notification.openfeign.ApiOrderServiceProvider;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/26 13:46
 * @description: knet 订单服务 降级处理机制
 */
@Component
public class ApiOrderServiceFallbackImpl implements ApiOrderServiceProvider {

    @Override
    public HttpResult<OrderDetailDtoResp> getOrderDetail(String orderId) {
        return HttpResult.error("订单服务不可用，请稍后再试");
    }

    @Override
    public HttpResult<OrderInvoiceSnapshotDtoResp> getInvoiceSnapshot(String invoiceNumber) {
        return HttpResult.error("订单服务不可用，请稍后再试");
    }

    @Override
    public HttpResult<Void> updateInvoicePdfUrl(String invoiceNumber, String pdfUrl) {
        return HttpResult.error("订单服务不可用，请稍后再试");
    }

    @Override
    public HttpResult<Void> updateInvoiceStatus(String invoiceNumber, Integer status) {
        return HttpResult.error("订单服务不可用，请稍后再试");
    }

    @Override
    public HttpResult<List<String>> getFailedInvoiceNumbers() {
        return HttpResult.error("订单服务不可用，请稍后再试");
    }

    @Override
    public HttpResult<OrderInvoiceSnapshotDtoResp> getInvoiceSnapshotByOrderId(String orderId) {
        return HttpResult.error("订单服务不可用，请稍后再试");
    }

    @Override
    public boolean deleteInvoiceSnapshotByOrderId(String orderId) {
        return false;
    }
}
