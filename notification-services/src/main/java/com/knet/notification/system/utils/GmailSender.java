package com.knet.notification.system.utils;

/**
 * <AUTHOR>
 * @date 2025/6/20 13:50
 * @description: 邮件发送工具
 */

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.knet.notification.system.config.MailProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.URLDataSource;
import javax.annotation.Resource;
import javax.mail.Message;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.net.URL;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2025/6/20 13:50
 * @description: 邮件发送工具（支持Nacos动态配置）
 */
@Slf4j
@Data
@Component
public class GmailSender {

    @Resource
    private MailProperties mailProperties;

    /**
     * 发送纯文本邮件
     *
     * @param to      收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 是否发送成功
     */
    @Retryable(
            value = Exception.class,
            maxAttempts = 3,
            backoff = @Backoff(delay = 2000, multiplier = 2)
    )
    public boolean sendText(String to, String subject, String content) throws Exception {
        if (StrUtil.isBlank(to)) {
            log.warn("收件人邮箱为空，邮件未发送 ");
            return false;
        }
        try {
            JSONObject body = new JSONObject();
            body.put("email", to);
            body.put("bccEmail", "");
            body.put("title", subject);
            body.put("content", content);
            sendGmail(body);
            log.info("成功发送纯文本邮件至 {}, 主题: {}", to, subject);
            return true;
        } catch (Exception e) {
            log.error("发送纯文本邮件失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 发送带附件的邮件
     *
     * @param to             收件人邮箱
     * @param subject        邮件主题
     * @param content        邮件内容
     * @param attachmentUrl  附件URL
     * @param attachmentName 附件名称
     * @return 是否发送成功
     */
    @Retryable(
            value = Exception.class,
            maxAttempts = 3,
            backoff = @Backoff(delay = 2000, multiplier = 2)
    )
    public boolean sendEmailWithAttachment(String to, String subject, String content,
                                           String attachmentUrl, String attachmentName) throws Exception {
        if (StrUtil.isBlank(to)) {
            log.warn("收件人邮箱为空，邮件未发送");
            return false;
        }
        try {
            JSONObject body = new JSONObject();
            body.put("email", to);
            body.put("bccEmail", "");
            body.put("title", subject);
            body.put("content", content);
            body.put("attachmentUrl", attachmentUrl);
            body.put("attachmentName", attachmentName);
            sendGmailWithAttachment(body);
            log.info("成功发送带附件邮件至 {}, 主题: {}, 附件: {}", to, subject, attachmentName);
            return true;
        } catch (Exception e) {
            log.error("发送带附件邮件失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    public void sendGmail(JSONObject body) throws Exception {
        String host = mailProperties.getHost();
        String username = mailProperties.getUsername();
        String usernamefrom = mailProperties.getFrom();
        String password = mailProperties.getPassword();
        String fromUser = mailProperties.getFromName();
        String toUser = body.getString("email");
        String bccEmail = body.getString("bccEmail");
        String title = body.getString("title");
        String content = body.getString("content");
        Properties props = new Properties();
        props.put("mail.transport.protocol", "smtps");
        props.put("mail.smtps.host", host);
        props.put("mail.smtps.auth", "true");
        props.put("mail.smtps.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        Session mailSession = Session.getDefaultInstance(props);
        MimeMessage message = new MimeMessage(mailSession);
        if (fromUser != null) {
            message.setFrom(new InternetAddress(usernamefrom, fromUser));
        }
        message.setSubject(title);
        message.setContentLanguage(new String[]{"utf-8"});
        message.setContent(content, "text/html;charset=utf-8");
        // 设置邮件的BCC
        if (!ObjectUtils.isEmpty(bccEmail)) {
            message.addRecipients(Message.RecipientType.BCC, InternetAddress.parse(bccEmail));
        }
        // null to 标识不设置收件人
        if (!"null to".equals(toUser)) {
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(toUser));
        }
        Transport transport = mailSession.getTransport();
        transport.connect(host, 465, username, password);
        transport.sendMessage(message, message.getAllRecipients());
        transport.close();
    }

    /**
     * 发送带附件的Gmail
     *
     * @param body 邮件内容JSON对象
     * @throws Exception 发送异常
     */
    public void sendGmailWithAttachment(JSONObject body) throws Exception {
        String host = mailProperties.getHost();
        String username = mailProperties.getUsername();
        String usernamefrom = mailProperties.getFrom();
        String password = mailProperties.getPassword();
        String fromUser = mailProperties.getFromName();
        String toUser = body.getString("email");
        String bccEmail = body.getString("bccEmail");
        String title = body.getString("title");
        String content = body.getString("content");
        String attachmentUrl = body.getString("attachmentUrl");
        String attachmentName = body.getString("attachmentName");

        Properties props = new Properties();
        props.put("mail.transport.protocol", "smtps");
        props.put("mail.smtps.host", host);
        props.put("mail.smtps.auth", "true");
        props.put("mail.smtps.socketFactory.class", "javax.net.ssl.SSLSocketFactory");

        Session mailSession = Session.getDefaultInstance(props);
        MimeMessage message = new MimeMessage(mailSession);

        if (fromUser != null) {
            message.setFrom(new InternetAddress(usernamefrom, fromUser));
        }
        message.setSubject(title);
        message.setContentLanguage(new String[]{"utf-8"});

        // 创建多部分消息
        MimeMultipart multipart = new MimeMultipart();

        // 添加邮件正文
        MimeBodyPart textPart = new MimeBodyPart();
        textPart.setContent(content, "text/html;charset=utf-8");
        multipart.addBodyPart(textPart);

        // 添加附件
        if (StrUtil.isNotBlank(attachmentUrl) && StrUtil.isNotBlank(attachmentName)) {
            MimeBodyPart attachmentPart = new MimeBodyPart();
            DataSource source = new URLDataSource(new URL(attachmentUrl));
            attachmentPart.setDataHandler(new DataHandler(source));
            attachmentPart.setFileName(attachmentName);
            multipart.addBodyPart(attachmentPart);
        }

        // 设置邮件内容
        message.setContent(multipart);

        // 设置邮件的BCC
        if (!ObjectUtils.isEmpty(bccEmail)) {
            message.addRecipients(Message.RecipientType.BCC, InternetAddress.parse(bccEmail));
        }

        // null to 标识不设置收件人
        if (!"null to".equals(toUser)) {
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(toUser));
        }

        Transport transport = mailSession.getTransport();
        transport.connect(host, 465, username, password);
        transport.sendMessage(message, message.getAllRecipients());
        transport.close();
    }
}
