package com.knet.notification.system.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/3/21 11:57
 * @description: RabbitConfig 通知服务RabbitMQ配置
 **/

@Configuration
public class RabbitConfig {

    /**
     * 通知服务专用死信交换机
     */
    @Bean
    public DirectExchange notificationDlxExchange() {
        return new DirectExchange("notification-service.dlx", true, false);
    }

    /**
     * 通知服务通知死信队列
     */
    @Bean
    public Queue notificationDlxQueue() {
        return QueueBuilder
                .durable("notification-service.dlx.notification.queue")
                .build();
    }

    /**
     * 通知服务通知死信绑定
     */
    @Bean
    public Binding notificationDlxBinding() {
        return BindingBuilder
                .bind(notificationDlxQueue())
                .to(notificationDlxExchange())
                .with("notification.notification.*");
    }

    /**
     * 通知交换机
     */
    @Bean
    public TopicExchange notificationExchange() {
        return new TopicExchange("notification-exchange", true, false);
    }

    /**
     * 通知服务队列
     */
    @Bean
    public Queue notificationQueue() {
        return QueueBuilder
                .durable("notification-queue.notification-services")
                .withArgument("x-dead-letter-exchange", "notification-service.dlx")
                .withArgument("x-dead-letter-routing-key", "notification.notification.*")
                .build();
    }

    /**
     * 通知服务队列绑定
     */
    @Bean
    public Binding notificationBinding() {
        return BindingBuilder
                .bind(notificationQueue())
                .to(notificationExchange())
                .with("notification.*");
    }

    /**
     * 支付结果交换机
     */
    @Bean
    public TopicExchange paymentResultExchange() {
        return new TopicExchange("payment-result-exchange", true, false);
    }

    /**
     * 通知服务支付结果队列
     */
    @Bean
    public Queue paymentResultNotificationQueue() {
        return QueueBuilder
                .durable("payment-result-queue.notification-services")
                .withArgument("x-dead-letter-exchange", "notification-service.dlx")
                .withArgument("x-dead-letter-routing-key", "notification.notification.*")
                .build();
    }

    /**
     * 通知服务支付结果队列绑定
     */
    @Bean
    public Binding paymentResultNotificationBinding() {
        return BindingBuilder
                .bind(paymentResultNotificationQueue())
                .to(paymentResultExchange())
                .with("payment.result");
    }

    /**
     * 支付完成消息处理的专用容器工厂
     * 设置prefetch=5，限制同时处理的消息数量，其他消息会在队列中排队等待
     */
    @Bean("paymentCompletedListenerContainerFactory")
    public SimpleRabbitListenerContainerFactory paymentCompletedListenerContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        // 设置预取数量为5，即最多同时处理5条消息
        factory.setPrefetchCount(5);
        // 设置并发消费者数量为1，确保消息按顺序处理
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(1);
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        return factory;
    }
}
