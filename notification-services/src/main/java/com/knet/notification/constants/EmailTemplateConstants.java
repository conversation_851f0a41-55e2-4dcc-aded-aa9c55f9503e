package com.knet.notification.constants;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 邮件模板常量类
 */
public class EmailTemplateConstants {

    /**
     * 发票邮件模板Redis Key
     */
    public static final String INVOICE_EMAIL_TEMPLATE_KEY = "template:invoice:email";
    
    /**
     * 发票PDF模板Redis Key
     */
    public static final String INVOICE_PDF_TEMPLATE_KEY = "template:invoice:pdf";
    
    /**
     * 模板内容字段
     */
    public static final String TEMPLATE_CONTENT_FIELD = "content";

    /**
     * 默认发票邮件模板
     */
    public static final String DEFAULT_INVOICE_EMAIL_TEMPLATE = """
            Hi [Customer Name],

            Thank you for shopping with KNET B2B. We've received your order and are getting it ready for processing.

            Order Details
            	Order Number: [Order Number]
            	Order Date: [Order Date]
            	Total Quantity: [Order Quantity]
            	Total Amount: [Order Total]

            You'll find your official invoice attached as a PDF for your records.

            If you have any questions about your order, please reach out to our sales team.

            Thank you for choosing KNET B2B to power your business.

            Best regards,
            KNETGROUP
            b2b.knetgroup.com
            """;

    /**
     * 邮件主题模板
     */
    public static final String INVOICE_EMAIL_SUBJECT_TEMPLATE = "Your KNET B2B Order Confirmation – Order #[OrderNumber]";
    
    /**
     * 发票PDF文件名模板
     */
    public static final String INVOICE_PDF_FILENAME_TEMPLATE = "invoice_[InvoiceNumber].pdf";
}
