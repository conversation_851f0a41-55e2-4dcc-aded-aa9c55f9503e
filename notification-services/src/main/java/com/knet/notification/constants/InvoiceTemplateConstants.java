package com.knet.notification.constants;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 发票模板常量类
 */
public class InvoiceTemplateConstants {

    /**
     * 发票HTML模板 - 基于test_invoice.html的简化版本
     */
    public static final String INVOICE_HTML_TEMPLATE = """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
                <style>
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                        font-family: 'Helvetica Neue', 'Arial', 'Microsoft YaHei', 'SimHei', sans-serif;
                    }
            
                    body {
                        margin: 0;
                        padding: 0;
                        background-color: #FFFFFF;
                        font-size: 12px;
                    }
            
                    .invoice-container {
                        position: relative;
                        width: 210mm;
                        min-height: 297mm;
                        background: #FFFFFF;
                        margin: 0;
                        padding: 20mm;
                        box-sizing: border-box;
                    }
            
                    .header-section {
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-start;
                        margin-bottom: 30px;
                    }
            
                    .company-info {
                           flex: 1;
                    }
            
                    .company-contact {
                           flex: 1;
                           display: flex;
                           flex-direction: column;
                           justify-content: flex-start;
                           padding-top: 55px; /* Aligns email with company-address1 */
                    }
            
                    .logo-container {
                        width: 90px;
                        height: 90px;
                        flex-shrink: 0;
                    }
            
                    .invoice-title {
                        font-weight: 700;
                        font-size: 24px;
                        line-height: 28px;
                        letter-spacing: 0.08em;
                        color: #017723;
                        margin-bottom: 10px;
                    }
            
                    .company-name {
                        font-weight: 500;
                        font-size: 16px;
                        line-height: 20px;
                        color: #393A3D;
                        margin-bottom: 5px;
                    }
            
                    .company-address {
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 16px;
                        color: #393A3D;
                        margin-bottom: 5px;
                    }
            
                    .company-contact {
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 16px;
                        color: #393A3D;
                    }
            
                    .address-section {
                        display: flex;
                        justify-content: space-between;
                        margin: 30px 0;
                        padding: 20px 0;
                        border-top: 1px dashed #D4D7DC;
                        border-bottom: 1px dashed #D4D7DC;
                    }
            
                    .bill-to, .ship-to {
                        display: flex;
                        flex-direction: column;
                        gap: 4px;
                    }
            
                    .section-title {
                        font-weight: 700;
                        font-size: 8px;
                        line-height: 10px;
                        color: #393A3D;
                    }
            
                    .address-content {
                        font-weight: 400;
                        font-size: 9px;
                        line-height: 14px;
                        color: #393A3D;
                    }
            
                    .invoice-details {
                        margin: 20px 0;
                        display: flex;
                        flex-direction: column;
                        gap: 8px;
                    }
            
                    .details-title {
                        font-weight: 700;
                        font-size: 9px;
                        line-height: 11px;
                        color: #393A3D;
                    }
            
                    .detail-item {
                        font-weight: 400;
                        font-size: 9px;
                        line-height: 11px;
                        color: #393A3D;
                        white-space: nowrap;
                    }
            
                    .products-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 20px;
                        font-size: 10px;
                    }
            
                    .table-header {
                        border-bottom: 2px solid #E3E5E8;
                        background-color: #f8f9fa;
                    }
            
                    .table-header th {
                        padding: 8px 4px;
                        font-weight: 700;
                        font-size: 10px;
                        color: #393A3D;
                        text-align: left;
                    }
            
                    .product-row {
                        border-bottom: 1px solid #E3E5E8;
                    }
            
                    .product-row td {
                        padding: 8px 4px;
                        font-weight: 400;
                        font-size: 9px;
                        line-height: 12px;
                        color: #393A3D;
                        vertical-align: top;
                    }
            
                    .total-row {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        width: 200px;
                        height: 32px;
                        margin-left: auto;
                        margin-top: 20px;
                        border-top: none;
                        border-bottom: 2px solid #E3E5E8;
                        padding-top: 10px;
                    }
            
                    .total-label {
                        font-weight: 700;
                        font-size: 8px;
                        line-height: 10px;
                        color: #393A3D;
                    }
            
                    .total-amount {
                        font-weight: 700;
                        font-size: 12px;
                        line-height: 14px;
                        text-align: right;
                        color: #393A3D;
                    }
                </style>
            </head>
            <body>
            <div class="invoice-container">
                <div class="header-section">
                   <div class="company-info">
                       <div class="invoice-title">INVOICE</div>
                       <div class="company-name">KNET Trading LLC</div>
                       <div class="company-address1">1400 Imperial Way</div>
                       <div class="company-address2">West Deptford, NJ 08066-1811</div>
                   </div>
                   <div class="company-contact">
                       <div class="content-email"><EMAIL></div>
                       <div class="website-address"> WWW.KNETGROUP.COM</div>
                   </div>
                    <div class="logo-container">
                        <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M78.3647 23.4605L20.4991 64.5572C20.1535 64.8037 19.7589 64.9408 19.4024 64.9408H13.0346C12.2835 64.9408 11.8957 64.3441 12.1692 63.6089L24.5878 30.2109C24.8613 29.4757 25.6913 28.879 26.4438 28.879H34.6063C35.3574 28.879 35.7451 29.4757 35.4716 30.2109L29.0535 47.4707L71.707 22.8531C72.3071 22.5068 72.9493 22.3203 73.5385 22.3203H78.3116C78.9198 22.3203 78.9538 23.0422 78.3647 23.4605Z"
                                  fill="url(#paint0_linear_2253_43140)"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M44.4569 50.7269L52.765 63.8069C53.0317 64.2278 52.4616 64.9404 51.8589 64.9404H38.7708C38.4061 64.9404 38.1109 64.7979 37.949 64.5435L33.9242 58.2072C33.5922 57.6825 33.9119 56.8393 34.6385 56.3238L42.5384 50.7136C43.265 50.1968 44.1236 50.2034 44.4569 50.7269Z"
                                  fill="#1D2B1C"/>
                            <defs>
                                <linearGradient id="paint0_linear_2253_43140" x1="12.0815" y1="64.9408" x2="78.3662"
                                                y2="20.9899" gradientUnits="userSpaceOnUse">
                                    <stop offset="0" stop-color="#02C739"/>
                                    <stop offset="0.5" stop-color="#B4F389"/>
                                    <stop offset="1" stop-color="#9FF136"/>
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                </div>
            
                <div class="address-section">
                    <div class="bill-to">
                        <div class="section-title">Bill to</div>
                        <div class="address-content">
                            ${billTo.recipient}<br/>
                            ${billTo.street}<br/>
                            ${billTo.city}, ${billTo.state} ${billTo.zipCode}<br/>
                            ${billTo.country}
                        </div>
                    </div>
                    <div class="ship-to">
                        <div class="section-title">Ship to</div>
                        <div class="address-content">
                            ${shipTo.recipient}<br/>
                            ${shipTo.street}<br/>
                            ${shipTo.city}, ${shipTo.state} ${shipTo.zipCode}<br/>
                            ${shipTo.country}
                        </div>
                    </div>
                </div>
            
                <div class="invoice-details">
                    <div class="details-title">Invoice details</div>
                    <div class="detail-item">Invoice no.: ${invoiceNumber}</div>
                    <div class="detail-item">Invoice date: ${invoiceDate}</div>
                    <div class="detail-item">Due date: ${dueDate}</div>
                </div>
            
                <table class="products-table">
                    <thead class="table-header">
                    <tr>
                        <th>#</th>
                        <th>Product Name</th>
                        <th>SKU</th>
                        <th>Size</th>
                        <th>Qty</th>
                        <th>Amount</th>
                    </tr>
                    </thead>
                    <tbody>
                        ${items}
                    </tbody>
                </table>
            
                <div class="total-row">
                    <div class="total-label">Total</div>
                    <div class="total-amount">$${totalAmount}</div>
                </div>
            </div>
            </body>
            </html>
            """;

    /**
     * 发票HTML模板 - 基于test_invoice.html的简化版本
     */
    public static final String INVOICE_HTML_NO_BILL_ADDRESS_TEMPLATE = """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
                <style>
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                        font-family: 'Helvetica Neue', 'Arial', 'Microsoft YaHei', 'SimHei', sans-serif;
                    }
            
                    body {
                        margin: 0;
                        padding: 0;
                        background-color: #FFFFFF;
                        font-size: 12px;
                    }
            
                    .invoice-container {
                        position: relative;
                        width: 210mm;
                        min-height: 297mm;
                        background: #FFFFFF;
                        margin: 0;
                        padding: 20mm;
                        box-sizing: border-box;
                    }
            
                    .header-section {
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-start;
                        margin-bottom: 30px;
                    }
            
                    .company-info {
                           flex: 1;
                    }
            
                    .company-contact {
                           flex: 1;
                           display: flex;
                           flex-direction: column;
                           justify-content: flex-start;
                           padding-top: 55px; /* Aligns email with company-address1 */
                    }
            
                    .logo-container {
                        width: 90px;
                        height: 90px;
                        flex-shrink: 0;
                    }
            
                    .invoice-title {
                        font-weight: 700;
                        font-size: 24px;
                        line-height: 28px;
                        letter-spacing: 0.08em;
                        color: #017723;
                        margin-bottom: 10px;
                    }
            
                    .company-name {
                        font-weight: 500;
                        font-size: 16px;
                        line-height: 20px;
                        color: #393A3D;
                        margin-bottom: 5px;
                    }
            
                    .company-address {
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 16px;
                        color: #393A3D;
                        margin-bottom: 5px;
                    }
            
                    .company-contact {
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 16px;
                        color: #393A3D;
                    }
            
                    .address-section {
                        display: flex;
                        justify-content: space-between;
                        margin: 30px 0;
                        padding: 20px 0;
                        border-top: 1px dashed #D4D7DC;
                        border-bottom: 1px dashed #D4D7DC;
                    }
            
                    .bill-to, .ship-to {
                        display: flex;
                        flex-direction: column;
                        gap: 4px;
                    }
            
                    .section-title {
                        font-weight: 700;
                        font-size: 8px;
                        line-height: 10px;
                        color: #393A3D;
                    }
            
                    .address-content {
                        font-weight: 400;
                        font-size: 9px;
                        line-height: 14px;
                        color: #393A3D;
                    }
            
                    .invoice-details {
                        margin: 20px 0;
                        display: flex;
                        flex-direction: column;
                        gap: 8px;
                    }
            
                    .details-title {
                        font-weight: 700;
                        font-size: 9px;
                        line-height: 11px;
                        color: #393A3D;
                    }
            
                    .detail-item {
                        font-weight: 400;
                        font-size: 9px;
                        line-height: 11px;
                        color: #393A3D;
                        white-space: nowrap;
                    }
            
                    .products-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 20px;
                        font-size: 10px;
                    }
            
                    .table-header {
                        border-bottom: 2px solid #E3E5E8;
                        background-color: #f8f9fa;
                    }
            
                    .table-header th {
                        padding: 8px 4px;
                        font-weight: 700;
                        font-size: 10px;
                        color: #393A3D;
                        text-align: left;
                    }
            
                    .product-row {
                        border-bottom: 1px solid #E3E5E8;
                    }
            
                    .product-row td {
                        padding: 8px 4px;
                        font-weight: 400;
                        font-size: 9px;
                        line-height: 12px;
                        color: #393A3D;
                        vertical-align: top;
                    }
            
                    .total-row {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        width: 200px;
                        height: 32px;
                        margin-left: auto;
                        margin-top: 20px;
                        border-top: none;
                        border-bottom: 2px solid #E3E5E8;
                        padding-top: 10px;
                    }
            
                    .total-label {
                        font-weight: 700;
                        font-size: 8px;
                        line-height: 10px;
                        color: #393A3D;
                    }
            
                    .total-amount {
                        font-weight: 700;
                        font-size: 12px;
                        line-height: 14px;
                        text-align: right;
                        color: #393A3D;
                    }
                </style>
            </head>
            <body>
            <div class="invoice-container">
                <div class="header-section">
                   <div class="company-info">
                       <div class="invoice-title">INVOICE</div>
                       <div class="company-name">KNET Trading LLC</div>
                       <div class="company-address1">1400 Imperial Way</div>
                       <div class="company-address2">West Deptford, NJ 08066-1811</div>
                   </div>
                   <div class="company-contact">
                       <div class="content-email"><EMAIL></div>
                       <div class="website-address"> WWW.KNETGROUP.COM</div>
                   </div>
                    <div class="logo-container">
                        <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M78.3647 23.4605L20.4991 64.5572C20.1535 64.8037 19.7589 64.9408 19.4024 64.9408H13.0346C12.2835 64.9408 11.8957 64.3441 12.1692 63.6089L24.5878 30.2109C24.8613 29.4757 25.6913 28.879 26.4438 28.879H34.6063C35.3574 28.879 35.7451 29.4757 35.4716 30.2109L29.0535 47.4707L71.707 22.8531C72.3071 22.5068 72.9493 22.3203 73.5385 22.3203H78.3116C78.9198 22.3203 78.9538 23.0422 78.3647 23.4605Z"
                                  fill="url(#paint0_linear_2253_43140)"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M44.4569 50.7269L52.765 63.8069C53.0317 64.2278 52.4616 64.9404 51.8589 64.9404H38.7708C38.4061 64.9404 38.1109 64.7979 37.949 64.5435L33.9242 58.2072C33.5922 57.6825 33.9119 56.8393 34.6385 56.3238L42.5384 50.7136C43.265 50.1968 44.1236 50.2034 44.4569 50.7269Z"
                                  fill="#1D2B1C"/>
                            <defs>
                                <linearGradient id="paint0_linear_2253_43140" x1="12.0815" y1="64.9408" x2="78.3662"
                                                y2="20.9899" gradientUnits="userSpaceOnUse">
                                    <stop offset="0" stop-color="#02C739"/>
                                    <stop offset="0.5" stop-color="#B4F389"/>
                                    <stop offset="1" stop-color="#9FF136"/>
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                </div>
            
                <div class="address-section">
                    <div class="bill-to">
                        <div class="section-title">Bill to</div>
                        <div class="address-content">
                            ${billEmails}<br/>
                        </div>
                    </div>
                    <div class="ship-to">
                        <div class="section-title">Ship to</div>
                        <div class="address-content">
                            ${shipTo.recipient}<br/>
                            ${shipTo.street}<br/>
                            ${shipTo.city}, ${shipTo.state} ${shipTo.zipCode}<br/>
                            ${shipTo.country}
                        </div>
                    </div>
                </div>
            
                <div class="invoice-details">
                    <div class="details-title">Invoice details</div>
                    <div class="detail-item">Invoice no.: ${invoiceNumber}</div>
                    <div class="detail-item">Invoice date: ${invoiceDate}</div>
                    <div class="detail-item">Due date: ${dueDate}</div>
                </div>
            
                <table class="products-table">
                    <thead class="table-header">
                    <tr>
                        <th>#</th>
                        <th>Product Name</th>
                        <th>SKU</th>
                        <th>Size</th>
                        <th>Qty</th>
                        <th>Amount</th>
                    </tr>
                    </thead>
                    <tbody>
                        ${items}
                    </tbody>
                </table>
            
                <div class="total-row">
                    <div class="total-label">Total</div>
                    <div class="total-amount">$${totalAmount}</div>
                </div>
            </div>
            </body>
            </html>
            """;
}
