package com.knet.notification.service;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 发票重试服务接口
 */
public interface IInvoiceRetryService {

    /**
     * 重新生成发票PDF（不发送邮件）
     * 用于发票生成失败的重试机制
     *
     * @param invoiceNumber 发票编号
     * @return 重新生成结果
     */
    boolean retryInvoiceGeneration(String invoiceNumber);

    /**
     * 检查发票是否需要重试
     *
     * @param invoiceNumber 发票编号
     * @return 是否需要重试
     */
    boolean needsRetry(String invoiceNumber);

    /**
     * 批量重试失败的发票
     *
     * @return 重试成功的数量
     */
    int batchRetryFailedInvoices();
}
