package com.knet.notification.service;

import com.knet.notification.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 邮件内容组装服务接口
 */
public interface IEmailCompositionService {

    /**
     * 生成邮件内容（使用发票快照数据）
     * 邮件内容为订单确认信息，发票以附件形式发送
     *
     * @param invoiceSnapshot 发票快照数据
     * @param recipientName   收件人姓名
     * @return 邮件内容
     */
    String generateEmailContent(OrderInvoiceSnapshotDtoResp invoiceSnapshot, String recipientName);

    /**
     * 生成邮件主题
     *
     * @param orderId 订单ID
     * @return 邮件主题
     */
    String generateEmailSubject(String orderId);

    /**
     * 生成PDF文件名
     *
     * @param invoiceNumber 发票编号
     * @return PDF文件名
     */
    String generatePdfFilename(String invoiceNumber);
}
