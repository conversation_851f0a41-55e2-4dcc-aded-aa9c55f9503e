package com.knet.notification.service.impl;

import com.knet.common.enums.MessageChannel;
import com.knet.common.enums.MessageType;
import com.knet.notification.model.dto.req.SendMessageRequest;
import com.knet.notification.model.entity.SysMessage;
import com.knet.notification.model.entity.SysMessageDelivery;
import com.knet.notification.openfeign.ApiOrderServiceProvider;
import com.knet.notification.service.IApiNotificationService;
import com.knet.notification.service.ISysMessageDeliveryService;
import com.knet.notification.service.ISysMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/20 13:25
 * @description:
 */
@Slf4j
@Service
public class ApiNotificationServiceImpl implements IApiNotificationService {
    @Resource
    private ISysMessageService sysMessageService;
    @Resource
    private ISysMessageDeliveryService sysMessageDeliveryService;
    @Resource
    private ApiOrderServiceProvider apiOrderServiceProvider;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendMessage(SendMessageRequest request) {
        // 1. 保存消息主表记录
        SysMessage message = SysMessage.createMessage(
                request.getSenderId() == null ? "SYSTEM" : request.getSenderId(),
                request.getReceiverId(),
                request.getContent(),
                request.getMsgType()
        );
        boolean saved = sysMessageService.save(message);
        if (!saved) {
            log.error("保存消息失败: {}", message);
            return false;
        }
        // 2. 处理发送通道
        List<MessageChannel> channels = request.getChannels();
        if (CollectionUtils.isEmpty(channels)) {
            // 如果未指定通道，根据消息类型选择默认通道
            channels = getDefaultChannels(request.getMsgType());
        }
        // 3. 创建消息发送记录
        List<SysMessageDelivery> deliveries = new ArrayList<>();
        for (MessageChannel channel : channels) {
            SysMessageDelivery delivery = SysMessageDelivery.createDelivery(message.getId(), channel);
            deliveries.add(delivery);
        }
        // 4. 保存发送记录
        boolean deliverySaved = sysMessageDeliveryService.saveBatch(deliveries);
        if (!deliverySaved) {
            log.error("保存消息发送记录失败: {}", deliveries);
            return false;
        }
        return true;
    }

    @Override
    public boolean cancelInvoiceByOrderId(String orderId) {
        log.info("删除订单对应的发票: orderId={}", orderId);
        try {
            // 调用order服务删除发票快照
            boolean result = apiOrderServiceProvider.deleteInvoiceSnapshotByOrderId(orderId);
            if (result) {
                log.info("删除订单对应的发票成功: orderId={}", orderId);
            } else {
                log.warn("删除订单对应的发票失败或不存在: orderId={}", orderId);
            }
            return result;
        } catch (Exception e) {
            log.error("删除订单对应的发票异常: orderId={}, error={}", orderId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据消息类型获取默认发送通道
     *
     * @param msgType 消息类型
     * @return 默认发送通道列表
     */
    private List<MessageChannel> getDefaultChannels(MessageType msgType) {
        return switch (msgType) {
            case TEXT, SYSTEM_NOTICE, ORDER_NOTICE, PAYMENT_NOTICE -> Collections.singletonList(MessageChannel.EMAIL);
            default -> Collections.singletonList(MessageChannel.SMS);
        };
    }
}
