package com.knet.notification.service;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 邮件发送服务接口
 */
public interface IEmailSendingService {

    /**
     * 发送带附件的邮件
     *
     * @param recipientEmail 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @param attachmentUrl 附件URL
     * @param attachmentFilename 附件文件名
     * @return 发送结果
     */
    boolean sendEmailWithAttachment(String recipientEmail, String subject, String content, 
                                  String attachmentUrl, String attachmentFilename);

    /**
     * 发送普通邮件
     *
     * @param recipientEmail 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 发送结果
     */
    boolean sendEmail(String recipientEmail, String subject, String content);
}
