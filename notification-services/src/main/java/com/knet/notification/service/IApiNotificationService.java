package com.knet.notification.service;

import com.knet.notification.model.dto.req.SendMessageRequest;

/**
 * <AUTHOR>
 * @date 2025/6/20 13:24
 * @description: 对外服务定义
 */
public interface IApiNotificationService {
    /**
     * 发送消息
     *
     * @param request 发送消息请求
     * @return 是否发送成功
     */
    boolean sendMessage(SendMessageRequest request);

    /**
     * 删除订单对应的发票
     *
     * @param orderId 订单ID
     * @return 删除结果
     */
    boolean cancelInvoiceByOrderId(String orderId);
}
