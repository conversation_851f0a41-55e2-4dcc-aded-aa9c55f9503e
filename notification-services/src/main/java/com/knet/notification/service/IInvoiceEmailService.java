package com.knet.notification.service;

import com.knet.notification.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 发票邮件发送服务接口
 */
public interface IInvoiceEmailService {

    /**
     * 发送订单确认邮件（（包含发票PDF附件）
     *
     * @param orderId         订单ID
     * @param userId          用户ID
     * @param invoiceNumber   发票编号
     * @param invoiceSnapshot 发票快照数据
     */
    void sendOrderConfirmationEmail(String orderId, Long userId, String invoiceNumber,
                                    OrderInvoiceSnapshotDtoResp invoiceSnapshot);

    /**
     * 生成并发送发票邮件
     *
     * @param orderId        订单ID
     * @param userId         用户ID
     * @param invoiceNumber  发票编号
     * @param recipientEmail 收件人邮箱
     * @param recipientName  收件人姓名
     */
    void generateAndSendInvoiceEmail(String orderId, Long userId, String invoiceNumber,
                                     String recipientEmail, String recipientName);
}
