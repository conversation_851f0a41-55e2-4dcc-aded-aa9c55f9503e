package com.knet.notification.service.impl;

import com.knet.common.enums.MessageStatus;
import com.knet.notification.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;
import com.knet.notification.model.dto.third.resp.UserInfoDtoResp;
import com.knet.notification.model.entity.SysMessage;
import com.knet.notification.model.entity.SysMessageDelivery;
import com.knet.notification.openfeign.ApiOrderServiceProvider;
import com.knet.notification.openfeign.ApiUserServiceProvider;
import com.knet.notification.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 发票邮件发送服务实现类
 */
@Slf4j
@Service
public class InvoiceEmailServiceImpl implements IInvoiceEmailService {

    @Resource
    private IInvoiceGenerationService invoiceGenerationService;
    @Resource
    private IEmailCompositionService emailCompositionService;
    @Resource
    private IEmailSendingService emailSendingService;
    @Resource
    private ApiOrderServiceProvider apiOrderServiceProvider;
    @Resource
    private ApiUserServiceProvider apiUserServiceProvider;
    @Resource
    private ISysMessageService sysMessageService;
    @Resource
    private ISysMessageDeliveryService sysMessageDeliveryService;
    @Resource
    private OrderServiceRetryWrapper orderServiceRetryWrapper;

    /**
     * 发送订单确认邮件（使用已有快照数据）
     */
    @Async
    @Override
    public void sendOrderConfirmationEmail(String orderId, Long userId, String invoiceNumber, OrderInvoiceSnapshotDtoResp invoiceSnapshot) {
        log.info("开始发送订单确认邮件: orderId={}, userId={}, invoiceNumber={}", orderId, userId, invoiceNumber);
        UserInfoDtoResp userInfo = apiUserServiceProvider.getUserById(userId).getData();
        if (userInfo == null || invoiceSnapshot == null) {
            log.error("获取必要发票必要数据失败: userInfo={}, invoiceSnapshot={}", userInfo != null, invoiceSnapshot != null);
            return;
        }
        generateAndSendInvoiceEmail(orderId, userId, invoiceNumber, userInfo.getEmail(), userInfo.getAccount(), invoiceSnapshot);
        log.info("订单确认邮件发送成功: orderId={}, invoiceNumber={}", orderId, invoiceNumber);
    }

    @Override
    public void generateAndSendInvoiceEmail(String orderId, Long userId, String invoiceNumber,
                                            String recipientEmail, String recipientName) {
        // 获取发票快照数据
        try {
            OrderInvoiceSnapshotDtoResp invoiceSnapshot = apiOrderServiceProvider.getInvoiceSnapshot(invoiceNumber).getData();
            if (invoiceSnapshot == null) {
                log.error("获取发票快照失败: invoiceNumber={}", invoiceNumber);
                return;
            }
            generateAndSendInvoiceEmailInternal(orderId, userId, invoiceNumber, recipientEmail, recipientName, invoiceSnapshot);
        } catch (Exception e) {
            log.error("获取发票快照失败: invoiceNumber={}, error={}", invoiceNumber, e.getMessage(), e);
        }
    }

    /**
     * 生成并发送发票邮件（使用已有数据）
     */
    public void generateAndSendInvoiceEmail(String orderId, Long userId, String invoiceNumber,
                                            String recipientEmail, String recipientName,
                                            OrderInvoiceSnapshotDtoResp invoiceSnapshot) {
        generateAndSendInvoiceEmailInternal(orderId, userId, invoiceNumber, recipientEmail, recipientName, invoiceSnapshot);
    }

    /**
     * 内部方法：生成并发送发票邮件
     */
    private void generateAndSendInvoiceEmailInternal(String orderId, Long userId, String invoiceNumber,
                                                     String recipientEmail, String recipientName,
                                                     OrderInvoiceSnapshotDtoResp invoiceSnapshot) {
        log.info("生成并发送发票邮件: orderId={}, invoiceNumber={}, email={}", orderId, invoiceNumber, recipientEmail);
        SysMessage message = sysMessageService.createInvoiceMessage(orderId, recipientEmail, recipientName);
        if (message == null) {
            log.error("创建消息记录失败: orderId={}", orderId);
            return;
        }
        SysMessageDelivery delivery = sysMessageDeliveryService.createEmailDelivery(message.getId());
        if (delivery == null) {
            log.error("创建邮件发送记录失败: messageId={}", message.getId());
            return;
        }
        try {
            String pdfUrl;
            if (invoiceSnapshot != null) {
                pdfUrl = invoiceGenerationService.generateInvoicePdf(orderId, invoiceNumber, invoiceSnapshot, recipientEmail);
            } else {
                pdfUrl = invoiceGenerationService.generateInvoicePdf(orderId, invoiceNumber, recipientEmail);
            }
            if (pdfUrl == null) {
                sysMessageDeliveryService.updateDeliveryStatus(delivery, MessageStatus.FAILED, "生成发票PDF失败");
                return;
            }
            // 使用快照数据生成邮件内容（发票以附件形式发送）
            String emailContent = emailCompositionService.generateEmailContent(invoiceSnapshot, recipientName);
            if (emailContent == null) {
                sysMessageDeliveryService.updateDeliveryStatus(delivery, MessageStatus.FAILED, "生成邮件内容失败");
                return;
            }
            String subject = emailCompositionService.generateEmailSubject(orderId);
            String filename = emailCompositionService.generatePdfFilename(invoiceNumber);
            boolean sendResult = emailSendingService.sendEmailWithAttachment(recipientEmail, subject, emailContent, pdfUrl, filename);
            if (sendResult) {
                orderServiceRetryWrapper.updateInvoiceStatusWithRetry(invoiceNumber, 2);
                sysMessageDeliveryService.updateDeliveryStatus(delivery, MessageStatus.SUCCESS, null);
                log.info("发票邮件发送成功: orderId={}, invoiceNumber={}, pdfUrl={}", orderId, invoiceNumber, pdfUrl);
            } else {
                sysMessageDeliveryService.updateDeliveryStatus(delivery, MessageStatus.FAILED, "邮件发送失败");
                log.error("发票邮件发送失败: orderId={}, invoiceNumber={}", orderId, invoiceNumber);
            }
        } catch (Exception e) {
            log.error("生成并发送发票邮件失败: orderId={}, error={}", orderId, e.getMessage(), e);
            sysMessageDeliveryService.updateDeliveryStatus(delivery, MessageStatus.FAILED, e.getMessage());
            try {
                orderServiceRetryWrapper.updateInvoiceStatusWithRetry(invoiceNumber, 3);
            } catch (Exception ex) {
                log.error("更新发票状态失败: invoiceNumber={}, error={}", invoiceNumber, ex.getMessage(), ex);
            }
        }
    }
}
