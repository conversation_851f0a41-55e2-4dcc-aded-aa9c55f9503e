package com.knet.notification.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.MessageChannel;
import com.knet.common.enums.MessageStatus;
import com.knet.notification.mapper.SysMessageDeliveryMapper;
import com.knet.notification.model.entity.SysMessageDelivery;
import com.knet.notification.service.ISysMessageDeliveryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:50
 * @description: SysMessageDeliveryServiceImpl 服务实现
 */
@Slf4j
@Service
public class SysMessageDeliveryServiceImpl extends ServiceImpl<SysMessageDeliveryMapper, SysMessageDelivery>
        implements ISysMessageDeliveryService {


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateDeliveryStatus(Long messageId, MessageChannel channel, MessageStatus messageStatus) {
        LambdaUpdateWrapper<SysMessageDelivery> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(SysMessageDelivery::getMessageId, messageId)
                .eq(SysMessageDelivery::getChannel, channel)
                .set(SysMessageDelivery::getDeliveryStatus, messageStatus);
        boolean updated = this.update(null, updateWrapper);
        log.info("更新消息发送记录状态: messageId={}, channel={}, status={}, result={}",
                messageId, channel, messageStatus, updated);
        return updated;
    }

    @Override
    public SysMessageDelivery createEmailDelivery(Long messageId) {
        try {
            SysMessageDelivery delivery = SysMessageDelivery.createDelivery(messageId, MessageChannel.EMAIL);
            boolean saved = this.save(delivery);
            if (saved) {
                log.info("创建邮件发送记录成功: deliveryId={}, messageId={}", delivery.getId(), messageId);
                return delivery;
            } else {
                log.error("保存邮件发送记录失败: messageId={}", messageId);
                return null;
            }
        } catch (Exception e) {
            log.error("创建邮件发送记录异常: messageId={}, error={}", messageId, e.getMessage(), e);
            return null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDeliveryStatus(SysMessageDelivery delivery, MessageStatus status, String errorLog) {
        try {
            delivery.setDeliveryStatus(status);
            delivery.setSendTime(LocalDateTime.now());
            if (StrUtil.isNotBlank(errorLog)) {
                delivery.setErrorLog(errorLog);
            }
            this.updateById(delivery);
            log.info("更新发送记录状态成功: deliveryId={}, status={}", delivery.getId(), status);
        } catch (Exception e) {
            log.error("更新发送记录状态失败: deliveryId={}, error={}", delivery.getId(), e.getMessage(), e);
        }
    }
}
