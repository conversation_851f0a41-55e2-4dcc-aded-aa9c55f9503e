package com.knet.notification.service.impl;

import com.knet.notification.service.IEmailSendingService;
import com.knet.notification.system.utils.GmailSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 邮件发送服务实现类
 */
@Slf4j
@Service
public class EmailSendingServiceImpl implements IEmailSendingService {

    @Resource
    private GmailSender gmailSender;

    @Override
    public boolean sendEmailWithAttachment(String recipientEmail, String subject, String content,
                                           String attachmentUrl, String attachmentFilename) {
        log.info("发送带附件邮件: recipientEmail={}, subject={}, attachmentUrl={}", recipientEmail, subject, attachmentUrl);
        try {
            boolean result = gmailSender.sendEmailWithAttachment(recipientEmail, subject, content, attachmentUrl, attachmentFilename);
            if (result) {
                log.info("带附件邮件发送成功: recipientEmail={}", recipientEmail);
            } else {
                log.error("带附件邮件发送失败: recipientEmail={}", recipientEmail);
            }
            return result;
        } catch (Exception e) {
            log.error("发送带附件邮件异常: recipientEmail={}, error={}", recipientEmail, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean sendEmail(String recipientEmail, String subject, String content) {
        log.info("发送普通邮件: recipientEmail={}, subject={}", recipientEmail, subject);
        try {
            boolean result = gmailSender.sendText(recipientEmail, subject, content);
            if (result) {
                log.info("普通邮件发送成功: recipientEmail={}", recipientEmail);
            } else {
                log.error("普通邮件发送失败: recipientEmail={}", recipientEmail);
            }
            return result;
        } catch (Exception e) {
            log.error("发送普通邮件异常: recipientEmail={}, error={}", recipientEmail, e.getMessage(), e);
            return false;
        }
    }
}
