package com.knet.notification.service.impl;

import cn.hutool.core.util.StrUtil;
import com.knet.notification.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;
import com.knet.notification.openfeign.ApiOrderServiceProvider;
import com.knet.notification.service.IInvoiceGenerationService;
import com.knet.notification.service.IInvoiceRetryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 发票重试服务实现类
 */
@Slf4j
@Service
public class InvoiceRetryServiceImpl implements IInvoiceRetryService {

    @Resource
    private IInvoiceGenerationService invoiceGenerationService;

    @Resource
    private ApiOrderServiceProvider apiOrderServiceProvider;

    @Override
    public boolean retryInvoiceGeneration(String invoiceNumber) {
        log.info("重试发票生成: invoiceNumber={}", invoiceNumber);
        try {
            // 1. 检查发票是否需要重试
            if (!needsRetry(invoiceNumber)) {
                log.info("发票不需要重试: invoiceNumber={}", invoiceNumber);
                return true;
            }

            // 2. 重新生成发票PDF
            String pdfUrl = invoiceGenerationService.regenerateInvoicePdf(invoiceNumber);

            if (StrUtil.isNotBlank(pdfUrl)) {
                log.info("发票重试生成成功: invoiceNumber={}, pdfUrl={}", invoiceNumber, pdfUrl);
                return true;
            } else {
                log.error("发票重试生成失败: invoiceNumber={}", invoiceNumber);
                return false;
            }
        } catch (Exception e) {
            log.error("发票重试生成异常: invoiceNumber={}, error={}", invoiceNumber, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean needsRetry(String invoiceNumber) {
        try {
            // 获取发票快照
            OrderInvoiceSnapshotDtoResp invoiceSnapshot = apiOrderServiceProvider.getInvoiceSnapshot(invoiceNumber).getData();
            if (invoiceSnapshot == null) {
                log.warn("发票快照不存在: invoiceNumber={}", invoiceNumber);
                return false;
            }

            // 检查PDF地址是否为空或发票状态为失败
            boolean noPdfUrl = StrUtil.isBlank(invoiceSnapshot.getInvoicePdfUrl());
            boolean isFailedStatus = invoiceSnapshot.getInvoiceStatus() != null &&
                    (invoiceSnapshot.getInvoiceStatus() == 3 || invoiceSnapshot.getInvoiceStatus() == 0);

            boolean needsRetry = noPdfUrl || isFailedStatus;
            log.info("检查发票是否需要重试: invoiceNumber={}, noPdfUrl={}, isFailedStatus={}, needsRetry={}",
                    invoiceNumber, noPdfUrl, isFailedStatus, needsRetry);

            return needsRetry;
        } catch (Exception e) {
            log.error("检查发票重试状态异常: invoiceNumber={}, error={}", invoiceNumber, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int batchRetryFailedInvoices() {
        log.info("开始批量重试失败的发票");
        int successCount = 0;
        try {
            // 获取失败的发票列表
            List<String> failedInvoiceNumbers = apiOrderServiceProvider.getFailedInvoiceNumbers().getData();
            if (failedInvoiceNumbers == null || failedInvoiceNumbers.isEmpty()) {
                log.info("没有需要重试的发票");
                return 0;
            }
            log.info("找到{}个需要重试的发票", failedInvoiceNumbers.size());
            for (String invoiceNumber : failedInvoiceNumbers) {
                try {
                    if (retryInvoiceGeneration(invoiceNumber)) {
                        successCount++;
                        log.info("发票重试成功: invoiceNumber={}", invoiceNumber);
                    } else {
                        log.error("发票重试失败: invoiceNumber={}", invoiceNumber);
                    }
                } catch (Exception e) {
                    log.error("发票重试异常: invoiceNumber={}, error={}", invoiceNumber, e.getMessage(), e);
                }
            }
            log.info("批量重试完成: 总数={}, 成功={}", failedInvoiceNumbers.size(), successCount);
            return successCount;
        } catch (Exception e) {
            log.error("批量重试发票异常: error={}", e.getMessage(), e);
            return successCount;
        }
    }
}
