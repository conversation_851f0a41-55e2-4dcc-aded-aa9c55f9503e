package com.knet.notification.service;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 邮件模板管理服务接口
 */
public interface IEmailTemplateService {

    /**
     * 获取发票邮件模板
     *
     * @return 邮件模板内容
     */
    String getInvoiceEmailTemplate();

    /**
     * 设置发票邮件模板
     *
     * @param template 邮件模板内容
     */
    void setInvoiceEmailTemplate(String template);

    /**
     * 初始化默认邮件模板
     */
    void initDefaultEmailTemplate();
}
