package com.knet.notification.service;

import com.knet.notification.model.dto.InvoiceData;
import com.knet.notification.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 发票生成服务接口
 */
public interface IInvoiceGenerationService {

    /**
     * 生成发票PDF并上传到S3
     *
     * @param orderId       订单ID
     * @param invoiceNumber 发票编号
     * @return PDF文件S3地址
     */
    String generateInvoicePdf(String orderId, String invoiceNumber, String billEmails);

    /**
     * 生成发票PDF并上传到S3（使用已有快照数据）
     *
     * @param orderId         订单ID
     * @param invoiceNumber   发票编号
     * @param invoiceSnapshot 发票快照数据
     * @return PDF文件S3地址
     */
    String generateInvoicePdf(String orderId, String invoiceNumber, OrderInvoiceSnapshotDtoResp invoiceSnapshot, String billEmails);

    /**
     * 生成发票PDF并上传到S3（使用已有快照数据，不更新数据库）
     *
     * @param orderId         订单ID
     * @param invoiceNumber   发票编号
     * @param invoiceSnapshot 快照
     * @param billEmails      抄送邮箱
     * @return URL
     */
    String generateInvoicePdfNotUpdate(String orderId, String invoiceNumber, OrderInvoiceSnapshotDtoResp invoiceSnapshot, String billEmails);

    /**
     * 根据发票数据生成PDF
     *
     * @param invoiceData 发票数据
     * @return PDF文件S3地址
     */
    String generateInvoicePdfFromData(InvoiceData invoiceData);


    /**
     * 重新生成发票PDF（用于重试机制）
     *
     * @param invoiceNumber 发票编号
     * @return PDF文件S3地址，如果生成失败返回null
     */
    String regenerateInvoicePdf(String invoiceNumber);
}
