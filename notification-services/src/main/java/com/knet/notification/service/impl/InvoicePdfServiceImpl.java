package com.knet.notification.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.S3FileUtil;
import com.knet.notification.model.dto.InvoiceData;
import com.knet.notification.service.IInvoicePdfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.LinkedHashMap;
import java.util.Map;

import static com.knet.notification.constants.InvoiceTemplateConstants.INVOICE_HTML_NO_BILL_ADDRESS_TEMPLATE;
import static com.knet.notification.constants.InvoiceTemplateConstants.INVOICE_HTML_TEMPLATE;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: Chrome PDF生成服务实现类 - 简化版本
 */
@Slf4j
@Service
public class InvoicePdfServiceImpl implements IInvoicePdfService {

    @Resource
    private S3FileUtil s3FileUtil;
    @Value("${invoice.pdf.chrome.executable:}")
    private String chromeExecutable;
    @Value("${invoice.pdf.chrome.headless:true}")
    private boolean headless;
    @Value("${invoice.pdf.chrome.timeout:30000}")
    private long timeout;


    @Override
    public String generateInvoicePdf(InvoiceData invoiceData) {
        try {
            String htmlContent = generateInvoiceHtml(invoiceData);
            byte[] pdfBytes = convertHtmlToPdfWithChrome(htmlContent);
            String fileName = "invoice_" + invoiceData.getInvoiceNumber() + ".pdf";
            return s3FileUtil.uploadFile(pdfBytes, fileName);
            /*            return "https://uploadsb2b.knetgroup.com/logo.jpeg";*/
        } catch (Exception e) {
            log.error("生成发票PDF失败: invoiceNumber={}, error={}", invoiceData.getInvoiceNumber(), e.getMessage(), e);
            throw new ServiceException("生成发票PDF失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String generateInvoiceHtml(InvoiceData invoiceData) {
        if (BeanUtil.isNotEmpty(invoiceData.getBillTo())) {
            return getFullInfoHtml(invoiceData);
        } else {
            return getNoBillAddressHtml(invoiceData);
        }
    }

    private String getNoBillAddressHtml(InvoiceData invoiceData) {
        String htmlContent = INVOICE_HTML_NO_BILL_ADDRESS_TEMPLATE;
        // 替换基本信息
        htmlContent = htmlContent.replace("${invoiceNumber}", escapeHtml(invoiceData.getInvoiceNumber()));
        htmlContent = htmlContent.replace("${invoiceDate}", escapeHtml(invoiceData.getInvoiceDate()));
        htmlContent = htmlContent.replace("${dueDate}", escapeHtml(invoiceData.getDueDate()));
        htmlContent = htmlContent.replace("${totalAmount}", escapeHtml(invoiceData.getTotalAmount()));
        // 替换发票地址信息
        if (invoiceData.getBillEmails() != null) {
            htmlContent = htmlContent.replace("${billEmails}", escapeHtml(invoiceData.getBillEmails()));
        }
        if (invoiceData.getShipTo() != null) {
            htmlContent = htmlContent.replace("${shipTo.recipient}", escapeHtml(invoiceData.getShipTo().getRecipient()));
            htmlContent = htmlContent.replace("${shipTo.street}", escapeHtml(invoiceData.getShipTo().getStreet()));
            htmlContent = htmlContent.replace("${shipTo.city}", escapeHtml(invoiceData.getShipTo().getCity()));
            htmlContent = htmlContent.replace("${shipTo.state}", escapeHtml(invoiceData.getShipTo().getState()));
            htmlContent = htmlContent.replace("${shipTo.zipCode}", escapeHtml(invoiceData.getShipTo().getZipCode()));
            htmlContent = htmlContent.replace("${shipTo.country}", escapeHtml(invoiceData.getShipTo().getCountry()));
        }
        // 生成商品列表 - 按SKU+Size分组聚合
        StringBuilder itemsHtml = new StringBuilder();
        if (invoiceData.getItems() != null) {
            // 按SKU+Size分组聚合商品数据
            Map<String, AggregatedItem> aggregatedItems = new LinkedHashMap<>();
            for (InvoiceData.InvoiceItem item : invoiceData.getItems()) {
                String key = item.getSku() + "_" + item.getSize();
                if (aggregatedItems.containsKey(key)) {
                    // 已存在相同SKU+Size的商品，累加数量和金额
                    AggregatedItem existing = aggregatedItems.get(key);
                    existing.quantity += item.getQuantity();
                    existing.totalAmount = existing.totalAmount.add(new BigDecimal(item.getAmount()));
                } else {
                    // 新的SKU+Size组合
                    AggregatedItem newItem = new AggregatedItem();
                    newItem.productName = item.getProductName();
                    newItem.sku = item.getSku();
                    newItem.size = item.getSize();
                    newItem.quantity = item.getQuantity();
                    newItem.totalAmount = new BigDecimal(item.getAmount());
                    aggregatedItems.put(key, newItem);
                }
            }
            // 生成HTML表格行
            int index = 1;
            for (AggregatedItem item : aggregatedItems.values()) {
                itemsHtml.append("<tr class=\"product-row\">")
                        .append("<td>").append(index++).append("</td>")
                        .append("<td>").append(escapeHtml(item.productName)).append("</td>")
                        .append("<td>").append(escapeHtml(item.sku)).append("</td>")
                        .append("<td>").append(escapeHtml(item.size)).append("</td>")
                        .append("<td>").append(item.quantity).append("</td>")
                        .append("<td>$").append(item.totalAmount.toString()).append("</td>")
                        .append("</tr>");
            }
        }
        htmlContent = htmlContent.replace("${items}", itemsHtml.toString());
        return htmlContent;
    }

    private String getFullInfoHtml(InvoiceData invoiceData) {
        String htmlContent = INVOICE_HTML_TEMPLATE;
        // 替换基本信息
        htmlContent = htmlContent.replace("${invoiceNumber}", escapeHtml(invoiceData.getInvoiceNumber()));
        htmlContent = htmlContent.replace("${invoiceDate}", escapeHtml(invoiceData.getInvoiceDate()));
        htmlContent = htmlContent.replace("${dueDate}", escapeHtml(invoiceData.getDueDate()));
        htmlContent = htmlContent.replace("${totalAmount}", escapeHtml(invoiceData.getTotalAmount()));
        // 替换地址信息
        if (invoiceData.getBillTo() != null) {
            htmlContent = htmlContent.replace("${billTo.recipient}", escapeHtml(invoiceData.getBillTo().getRecipient()));
            htmlContent = htmlContent.replace("${billTo.street}", escapeHtml(invoiceData.getBillTo().getStreet()));
            htmlContent = htmlContent.replace("${billTo.city}", escapeHtml(invoiceData.getBillTo().getCity()));
            htmlContent = htmlContent.replace("${billTo.state}", escapeHtml(invoiceData.getBillTo().getState()));
            htmlContent = htmlContent.replace("${billTo.zipCode}", escapeHtml(invoiceData.getBillTo().getZipCode()));
            htmlContent = htmlContent.replace("${billTo.country}", escapeHtml(invoiceData.getBillTo().getCountry()));
        }
        if (invoiceData.getShipTo() != null) {
            htmlContent = htmlContent.replace("${shipTo.recipient}", escapeHtml(invoiceData.getShipTo().getRecipient()));
            htmlContent = htmlContent.replace("${shipTo.street}", escapeHtml(invoiceData.getShipTo().getStreet()));
            htmlContent = htmlContent.replace("${shipTo.city}", escapeHtml(invoiceData.getShipTo().getCity()));
            htmlContent = htmlContent.replace("${shipTo.state}", escapeHtml(invoiceData.getShipTo().getState()));
            htmlContent = htmlContent.replace("${shipTo.zipCode}", escapeHtml(invoiceData.getShipTo().getZipCode()));
            htmlContent = htmlContent.replace("${shipTo.country}", escapeHtml(invoiceData.getShipTo().getCountry()));
        }
        // 生成商品列表 - 按SKU+Size分组聚合
        StringBuilder itemsHtml = new StringBuilder();
        if (invoiceData.getItems() != null) {
            // 按SKU+Size分组聚合商品数据
            Map<String, AggregatedItem> aggregatedItems = new LinkedHashMap<>();
            for (InvoiceData.InvoiceItem item : invoiceData.getItems()) {
                String key = item.getSku() + "_" + item.getSize();
                if (aggregatedItems.containsKey(key)) {
                    // 已存在相同SKU+Size的商品，累加数量和金额
                    AggregatedItem existing = aggregatedItems.get(key);
                    existing.quantity += item.getQuantity();
                    existing.totalAmount = existing.totalAmount.add(new BigDecimal(item.getAmount()));
                } else {
                    // 新的SKU+Size组合
                    AggregatedItem newItem = new AggregatedItem();
                    newItem.productName = item.getProductName();
                    newItem.sku = item.getSku();
                    newItem.size = item.getSize();
                    newItem.quantity = item.getQuantity();
                    newItem.totalAmount = new BigDecimal(item.getAmount());
                    aggregatedItems.put(key, newItem);
                }
            }
            // 生成HTML表格行
            int index = 1;
            for (AggregatedItem item : aggregatedItems.values()) {
                itemsHtml.append("<tr class=\"product-row\">")
                        .append("<td>").append(index++).append("</td>")
                        .append("<td>").append(escapeHtml(item.productName)).append("</td>")
                        .append("<td>").append(escapeHtml(item.sku)).append("</td>")
                        .append("<td>").append(escapeHtml(item.size)).append("</td>")
                        .append("<td>").append(item.quantity).append("</td>")
                        .append("<td>$").append(item.totalAmount.toString()).append("</td>")
                        .append("</tr>");
            }
        }
        htmlContent = htmlContent.replace("${items}", itemsHtml.toString());
        return htmlContent;
    }

    @Override
    public byte[] convertHtmlToPdf(String htmlContent) {
        try {
            return convertHtmlToPdfWithChrome(htmlContent);
        } catch (Exception e) {
            log.error("HTML转PDF失败: {}", e.getMessage(), e);
            throw new ServiceException("HTML转PDF失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用Chrome命令行生成PDF
     */
    private byte[] convertHtmlToPdfWithChrome(String htmlContent) throws Exception {
        // 创建临时HTML文件
        File tempHtmlFile = File.createTempFile("invoice_", ".html");
        File tempPdfFile = File.createTempFile("invoice_", ".pdf");
        log.info("开始Chrome PDF生成，临时文件: HTML={}, PDF={}",
                tempHtmlFile.getAbsolutePath(), tempPdfFile.getAbsolutePath());
        try {
            // 写入HTML内容
            try (FileWriter writer = new FileWriter(tempHtmlFile, StandardCharsets.UTF_8)) {
                writer.write(htmlContent);
            }
            log.info("HTML文件写入完成，大小: {} bytes", tempHtmlFile.length());
            // 构建Chrome命令
            String chromeCommand = getChromeExecutablePath();
            log.info("使用Chrome可执行文件: {}", chromeCommand);
            ProcessBuilder processBuilder = new ProcessBuilder(
                    chromeCommand,
                    "--headless",
                    "--disable-gpu",
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                    "--disable-extensions",
                    "--disable-plugins",
                    "--disable-default-apps",
                    "--disable-dbus",
                    "--disable-logging",
                    "--log-level=3",
                    "--print-to-pdf=" + tempPdfFile.getAbsolutePath(),
                    "--print-to-pdf-no-header",
                    "--run-all-compositor-stages-before-draw",
                    "--virtual-time-budget=10000",
                    "--timeout=30000",
                    tempHtmlFile.toURI().toString()
            );

            // 设置环境变量，适配服务器环境
            Map<String, String> env = processBuilder.environment();
            env.put("DISPLAY", ":99");
            env.put("CHROME_DEVEL_SANDBOX", "/usr/local/sbin/chrome-devel-sandbox");

            // 重定向错误输出用于调试
            processBuilder.redirectErrorStream(true);

            log.info("执行Chrome命令: {}", String.join(" ", processBuilder.command()));

            // 执行Chrome命令
            Process process = processBuilder.start();

            // 读取Chrome的输出日志
            StringBuilder chromeOutput = new StringBuilder();
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    chromeOutput.append(line).append("\n");
                }
            }

            boolean finished = process.waitFor(timeout, java.util.concurrent.TimeUnit.MILLISECONDS);
            int exitCode = process.exitValue();

            log.info("Chrome进程执行完成，退出码: {}, 是否超时: {}", exitCode, !finished);
            if (!chromeOutput.isEmpty()) {
                log.info("Chrome输出日志: \n{}", chromeOutput);
            }

            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("Chrome PDF生成超时，timeout=" + timeout + "ms");
            }

            if (exitCode != 0) {
                throw new RuntimeException("Chrome PDF生成失败，退出码: " + exitCode +
                        ", Chrome输出: " + chromeOutput);
            }

            // 检查PDF文件是否生成
            if (!tempPdfFile.exists()) {
                throw new RuntimeException("PDF文件不存在: " + tempPdfFile.getAbsolutePath());
            }

            long pdfSize = tempPdfFile.length();
            log.info("PDF文件生成成功，大小: {} bytes", pdfSize);

            if (pdfSize == 0) {
                throw new RuntimeException("PDF文件大小为0，可能生成失败，Chrome输出: " + chromeOutput);
            }

            if (pdfSize < 1000) {
                log.warn("PDF文件大小较小: {} bytes，可能内容不完整", pdfSize);
            }

            return Files.readAllBytes(tempPdfFile.toPath());

        } finally {
            // 清理临时文件
            if (tempHtmlFile.exists()) {
                boolean htmlDeleted = tempHtmlFile.delete();
                log.debug("临时HTML文件删除: {}", htmlDeleted);
            }
            if (tempPdfFile.exists()) {
                boolean pdfDeleted = tempPdfFile.delete();
                log.debug("临时PDF文件删除: {}", pdfDeleted);
            }
        }
    }

    /**
     * 获取Chrome可执行文件路径
     * 修复版本：正确处理配置文件中的Chrome路径
     */
    private String getChromeExecutablePath() {
        log.info("开始获取Chrome可执行文件路径，配置值: [{}]", chromeExecutable);

        // 首先检查配置的Chrome路径是否有效
        if (StringUtils.hasText(chromeExecutable)) {
            log.info("检查配置的Chrome路径: {}", chromeExecutable);
            File configuredFile = new File(chromeExecutable);
            if (configuredFile.exists()) {
                if (isValidChromeExecutable(chromeExecutable)) {
                    log.info("✓ 使用配置的Chrome路径: {}", chromeExecutable);
                    return chromeExecutable;
                } else {
                    log.warn("⚠️  配置的Chrome路径存在但验证失败: {}", chromeExecutable);
                    // 即使验证失败，如果文件存在也尝试使用配置的路径
                    log.info("尝试使用配置的Chrome路径（跳过验证）: {}", chromeExecutable);
                    return chromeExecutable;
                }
            } else {
                log.warn("❌ 配置的Chrome路径不存在: {}，开始自动检测", chromeExecutable);
            }
        } else {
            log.info("未配置Chrome路径，开始自动检测");
        }

        // 自动检测Chrome路径
        String os = System.getProperty("os.name").toLowerCase();
        log.info("检测到操作系统: {}", os);

        if (os.contains("win")) {
            // Windows环境
            return detectWindowsChrome();
        } else if (os.contains("mac")) {
            // macOS环境
            return detectMacChrome();
        } else {
            // Linux环境
            return detectLinuxChrome();
        }
    }

    /**
     * 检测Windows环境下的Chrome
     */
    private String detectWindowsChrome() {
        String[] windowsPaths = {
                "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe"
        };
        for (String path : windowsPaths) {
            if (new File(path).exists()) {
                log.info("找到Windows Chrome路径: {}", path);
                return path;
            }
        }
        throw new ServiceException("Windows环境下未找到Chrome可执行文件");
    }

    /**
     * 检测macOS环境下的Chrome
     */
    private String detectMacChrome() {
        String[] macPaths = {
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chromium.app/Contents/MacOS/Chromium"
        };
        for (String path : macPaths) {
            if (new File(path).exists()) {
                log.info("找到macOS Chrome路径: {}", path);
                return path;
            }
        }

        // 尝试使用which命令查找Chrome
        String whichResult = findChromeWithWhichCommand();
        if (whichResult != null) {
            log.info("通过which命令找到Chrome: {}", whichResult);
            return whichResult;
        }

        throw new ServiceException("macOS环境下未找到Chrome可执行文件");
    }

    /**
     * 检测Linux环境下的Chrome
     */
    private String detectLinuxChrome() {
        // Linux环境 - 优先检查常用的Chrome安装路径
        String[] linuxPaths = {
                "/usr/bin/google-chrome",           // 最常见的Google Chrome安装路径
                "/usr/bin/google-chrome-stable",    // Chrome稳定版
                "/usr/bin/chromium",                // Chromium
                "/usr/bin/chromium-browser",        // Ubuntu系统Chromium
                "/opt/google/chrome/chrome",        // 自定义安装路径
                "/snap/bin/chromium",               // Snap安装的Chromium
                "/snap/chromium/current/usr/lib/chromium-browser/chrome", // Snap详细路径
                "/usr/lib/chromium-browser/chromium-browser" // 库路径安装
        };

        log.info("检测Linux环境中的Chrome安装...");
        for (String path : linuxPaths) {
            if (new File(path).exists()) {
                log.info("找到Chrome可执行文件: {}", path);
                if (isValidChromeExecutable(path)) {
                    log.info("✓ 验证Chrome可执行文件有效: {}", path);
                    return path;
                } else {
                    log.warn("⚠️  Chrome可执行文件验证失败，但继续尝试使用: {}", path);
                    // 即使验证失败，也尝试使用找到的文件
                    return path;
                }
            }
        }

        // 使用which命令查找
        String whichResult = findChromeWithWhichCommand();
        if (whichResult != null) {
            log.info("通过which命令找到Chrome: {}", whichResult);
            return whichResult;
        }

        // 如果配置了Chrome路径但文件不存在，仍然尝试使用配置的路径
        if (StringUtils.hasText(chromeExecutable)) {
            log.warn("所有自动检测失败，尝试使用配置的Chrome路径: {}", chromeExecutable);
            return chromeExecutable;
        }

        // 最后的兜底：抛出异常而不是返回错误的路径
        throw new ServiceException("Linux环境下未找到Chrome可执行文件。请确保已安装Chrome并在配置中指定正确路径：invoice.pdf.chrome.executable");
    }

    /**
     * 验证Chrome可执行文件是否有效
     *
     * @param chromePath Chrome可执行文件路径
     * @return 是否有效
     */
    private boolean isValidChromeExecutable(String chromePath) {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(chromePath, "--version");
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();
            boolean finished = process.waitFor(5, java.util.concurrent.TimeUnit.SECONDS);
            if (finished && process.exitValue() == 0) {
                // 读取版本信息验证
                try (java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(process.getInputStream()))) {
                    String versionLine = reader.readLine();
                    if (versionLine != null && (versionLine.contains("Chrome") || versionLine.contains("Chromium"))) {
                        log.debug("Chrome版本验证成功: {}", versionLine.trim());
                        return true;
                    }
                }
            }
            if (!finished) {
                process.destroyForcibly();
            }
        } catch (Exception e) {
            log.debug("Chrome可执行文件验证失败: path={}, error={}", chromePath, e.getMessage());
        }
        return false;
    }

    /**
     * 使用which命令查找Chrome
     *
     * @return Chrome路径，如果未找到返回null
     */
    private String findChromeWithWhichCommand() {
        String[] commands = {"google-chrome", "chromium-browser", "chromium", "chrome"};

        for (String command : commands) {
            try {
                ProcessBuilder processBuilder = new ProcessBuilder("which", command);
                processBuilder.redirectErrorStream(true);
                Process process = processBuilder.start();
                boolean finished = process.waitFor(3, java.util.concurrent.TimeUnit.SECONDS);

                if (finished && process.exitValue() == 0) {
                    try (java.io.BufferedReader reader = new java.io.BufferedReader(
                            new java.io.InputStreamReader(process.getInputStream()))) {
                        String path = reader.readLine();
                        if (path != null && !path.trim().isEmpty()) {
                            path = path.trim();
                            log.debug("which命令找到: {} -> {}", command, path);
                            return path;
                        }
                    }
                }
                if (!finished) {
                    process.destroyForcibly();
                }
            } catch (Exception e) {
                log.debug("which命令执行失败: command={}, error={}", command, e.getMessage());
            }
        }
        return null;
    }

    /**
     * HTML转义方法
     */
    private String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#39;");
    }

    /**
     * 聚合商品项内部类 - 用于按SKU+Size分组
     */
    private static class AggregatedItem {
        String productName;
        String sku;
        String size;
        int quantity;
        BigDecimal totalAmount;
    }
}
