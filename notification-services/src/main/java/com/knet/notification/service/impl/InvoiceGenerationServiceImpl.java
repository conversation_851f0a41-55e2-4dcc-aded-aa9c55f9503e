package com.knet.notification.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.knet.notification.model.dto.InvoiceData;
import com.knet.notification.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;
import com.knet.notification.openfeign.ApiOrderServiceProvider;
import com.knet.notification.service.IInvoiceGenerationService;
import com.knet.notification.service.IInvoicePdfService;
import com.knet.notification.system.utils.InvoiceDataParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 发票生成服务实现类
 */
@Slf4j
@Service
public class InvoiceGenerationServiceImpl implements IInvoiceGenerationService {

    @Resource
    private IInvoicePdfService invoicePdfService;

    @Resource
    private ApiOrderServiceProvider apiOrderServiceProvider;

    @Resource
    private OrderServiceRetryWrapper orderServiceRetryWrapper;

    @Override
    public String generateInvoicePdf(String orderId, String invoiceNumber, String billEmails) {
        log.info("生成发票PDF: orderId={}, invoiceNumber={}", orderId, invoiceNumber);
        try {
            // 1. 获取发票快照（包含所有必要数据）
            OrderInvoiceSnapshotDtoResp invoiceSnapshot = apiOrderServiceProvider.getInvoiceSnapshot(invoiceNumber).getData();
            if (invoiceSnapshot == null) {
                log.error("获取发票快照失败: orderId={}, invoiceNumber={}", orderId, invoiceNumber);
                return null;
            }
            // 2. 使用重载方法生成PDF
            return generateInvoicePdf(orderId, invoiceNumber, invoiceSnapshot, billEmails);
        } catch (Exception e) {
            log.error("生成发票PDF失败: orderId={}, invoiceNumber={}, error={}", orderId, invoiceNumber, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String generateInvoicePdf(String orderId, String invoiceNumber, OrderInvoiceSnapshotDtoResp invoiceSnapshot, String billEmails) {
        log.info("生成发票PDF（使用已有快照）: orderId={}, invoiceNumber={}", orderId, invoiceNumber);
        try {
            if (invoiceSnapshot == null) {
                log.error("发票快照数据为空: orderId={}, invoiceNumber={}", orderId, invoiceNumber);
                return null;
            }
            InvoiceData invoiceData = buildInvoiceData(invoiceSnapshot);
            if (StrUtil.isNotBlank(billEmails)) {
                invoiceData.setBillEmails(billEmails);
            }
            String pdfUrl = invoicePdfService.generateInvoicePdf(invoiceData);
            apiOrderServiceProvider.updateInvoicePdfUrl(invoiceNumber, pdfUrl);
            log.info("发票PDF生成成功（使用已有快照）: invoiceNumber={}, pdfUrl={}", invoiceNumber, pdfUrl);
            return pdfUrl;
        } catch (Exception e) {
            log.error("生成发票PDF失败（使用已有快照）: orderId={}, invoiceNumber={}, error={}", orderId, invoiceNumber, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String generateInvoicePdfNotUpdate(String orderId, String invoiceNumber, OrderInvoiceSnapshotDtoResp invoiceSnapshot, String billEmails) {
        log.info("用户主动 生成发票PDF: orderId={}, invoiceNumber={}", orderId, invoiceNumber);
        try {
            if (invoiceSnapshot == null) {
                log.error("订单发票快照数据为空: orderId={}, invoiceNumber={}", orderId, invoiceNumber);
                return null;
            }
            InvoiceData invoiceData = buildInvoiceData(invoiceSnapshot);
            if (StrUtil.isNotBlank(billEmails)) {
                invoiceData.setBillEmails(billEmails);
            }
            String pdfUrl = invoicePdfService.generateInvoicePdf(invoiceData);
            log.info("发票PDF生成成功（使用已有快照）: invoiceNumber={}, pdfUrl={}", invoiceNumber, pdfUrl);
            return pdfUrl;
        } catch (Exception e) {
            log.error("生成发票PDF失败（使用已有快照）: orderId={}, invoiceNumber={}, error={}", orderId, invoiceNumber, e.getMessage(), e);
            return null;
        }
    }


    @Override
    public String generateInvoicePdfFromData(InvoiceData invoiceData) {
        log.info("根据发票数据生成PDF: invoiceNumber={}", invoiceData.getInvoiceNumber());
        try {
            String pdfUrl = invoicePdfService.generateInvoicePdf(invoiceData);
            log.info("发票PDF生成成功: invoiceNumber={}, pdfUrl={}", invoiceData.getInvoiceNumber(), pdfUrl);
            return pdfUrl;
        } catch (Exception e) {
            log.error("根据发票数据生成PDF失败: invoiceNumber={}, error={}", invoiceData.getInvoiceNumber(), e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String regenerateInvoicePdf(String invoiceNumber) {
        log.info("重新生成发票PDF: invoiceNumber={}", invoiceNumber);
        try {
            // 1. 获取发票快照
            OrderInvoiceSnapshotDtoResp invoiceSnapshot = apiOrderServiceProvider.getInvoiceSnapshot(invoiceNumber).getData();
            if (invoiceSnapshot == null) {
                log.error("发票快照不存在: invoiceNumber={}", invoiceNumber);
                return null;
            }
            // 2. 构建发票数据
            InvoiceData invoiceData = buildInvoiceData(invoiceSnapshot);
            // 4. 生成PDF并上传到S3
            String pdfUrl = invoicePdfService.generateInvoicePdf(invoiceData);
            // 5. 更新发票快照的PDF地址
            apiOrderServiceProvider.updateInvoicePdfUrl(invoiceNumber, pdfUrl);
            // 6. 更新发票状态为已生成 - 使用重试机制
            orderServiceRetryWrapper.updateInvoiceStatusWithRetry(invoiceNumber, 1);
            log.info("重新生成发票PDF成功: invoiceNumber={}, pdfUrl={}", invoiceNumber, pdfUrl);
            return pdfUrl;
        } catch (Exception e) {
            log.error("重新生成发票PDF失败: invoiceNumber={}, error={}", invoiceNumber, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建发票数据（直接从发票快照获取数据）
     */
    private InvoiceData buildInvoiceData(OrderInvoiceSnapshotDtoResp invoiceSnapshot) {
        // 使用工具类解析账单地址、收货地址、订单项数据
        InvoiceData.AddressInfo billTo = InvoiceDataParseUtil.parseAddressInfo(invoiceSnapshot.getBillToAddress());
        InvoiceData.AddressInfo shipTo = InvoiceDataParseUtil.parseAddressInfo(invoiceSnapshot.getShipToAddress());
        List<InvoiceData.InvoiceItem> items = InvoiceDataParseUtil.parseOrderItems(invoiceSnapshot.getOrderItems());
        return InvoiceData.builder()
                .invoiceNumber(invoiceSnapshot.getInvoiceNumber())
                .invoiceDate(DateUtil.format(invoiceSnapshot.getInvoiceDate(), "MM/dd/yyyy"))
                .dueDate(DateUtil.format(invoiceSnapshot.getDueDate(), "MM/dd/yyyy"))
                .totalAmount(invoiceSnapshot.getTotalAmount().toString())
                .billTo(billTo)
                .shipTo(shipTo)
                .items(items)
                .build();
    }
}
