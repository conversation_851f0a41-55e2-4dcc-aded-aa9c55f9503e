package com.knet.notification.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.common.dto.message.NotificationMessage;
import com.knet.notification.model.entity.SysMessage;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:42
 * @description: ISysMessageService服务定义
 */
public interface ISysMessageService extends IService<SysMessage> {

    /**
     * 保存消息
     *
     * @param notificationMessage 通知消息
     * @return 消息
     */
    SysMessage saveMessage(NotificationMessage notificationMessage);

    /**
     * 创建发票消息记录
     *
     * @param orderId 订单ID
     * @param recipientEmail 收件人邮箱
     * @param recipientName 收件人姓名
     * @return 消息记录
     */
    SysMessage createInvoiceMessage(String orderId, String recipientEmail, String recipientName);
}
