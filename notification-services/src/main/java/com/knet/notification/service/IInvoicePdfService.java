package com.knet.notification.service;

import com.knet.notification.model.dto.InvoiceData;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 发票PDF生成服务接口
 */
public interface IInvoicePdfService {

    /**
     * 生成发票PDF并上传到S3
     *
     * @param invoiceData 发票数据
     * @return S3文件地址
     */
    String generateInvoicePdf(InvoiceData invoiceData);

    /**
     * 根据模板生成HTML内容
     *
     * @param invoiceData 发票数据
     * @return HTML内容
     */
    String generateInvoiceHtml(InvoiceData invoiceData);

    /**
     * 将HTML转换为PDF字节数组
     *
     * @param htmlContent HTML内容
     * @return PDF字节数组
     */
    byte[] convertHtmlToPdf(String htmlContent);
}
