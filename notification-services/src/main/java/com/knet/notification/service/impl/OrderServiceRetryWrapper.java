package com.knet.notification.service.impl;

import com.knet.common.base.HttpResult;
import com.knet.common.exception.ServiceException;
import com.knet.notification.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;
import com.knet.notification.openfeign.ApiOrderServiceProvider;
import feign.FeignException;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.ConnectException;
import java.net.SocketException;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 订单服务API重试包装类 - Spring Retry方案实现
 */
@Slf4j
@Service
public class OrderServiceRetryWrapper {

    @Resource
    private ApiOrderServiceProvider apiOrderServiceProvider;

    /**
     * 带Spring Retry重试机制的获取发票快照方法
     * 在Service层使用@Retryable注解可以正常工作
     *
     * @param prentOrderId 订单ID
     * @return 发票快照信息
     */
    @Retryable(
            backoff = @Backoff(delay = 1000, multiplier = 2.0),
            include = {
                    ConnectException.class,
                    SocketException.class,
                    FeignException.class,
                    TimeoutException.class,
                    RetryableException.class,
                    RuntimeException.class
            },
            exclude = {IllegalArgumentException.class}
    )
    public HttpResult<OrderInvoiceSnapshotDtoResp> getInvoiceSnapshotByOrderIdWithRetry(String prentOrderId) {
        log.info("尝试获取订单发票快照: prentOrderId={}", prentOrderId);
        try {
            HttpResult<OrderInvoiceSnapshotDtoResp> result = apiOrderServiceProvider.getInvoiceSnapshotByOrderId(prentOrderId);
            if (result != null && result.success()) {
                log.info("成功获取订单发票快照: prentOrderId={}", prentOrderId);
                return result;
            } else {
                String errorMsg = "获取发票快照失败: " + (result != null ? result.getMsg() : "返回结果为空");
                log.warn("获取订单发票快照失败，将重试: prentOrderId={}, error={}", prentOrderId, errorMsg);
                throw new ServiceException(errorMsg);
            }
        } catch (Exception e) {
            log.error("获取订单发票快照异常，将重试: prentOrderId={}, error={}", prentOrderId, e.getMessage());
            throw e;
        }
    }

    /**
     * 重试失败后的降级处理方法
     * 当所有重试都失败后，会调用此方法
     * 注意：@Recover方法的参数顺序必须是异常在前，业务参数在后
     *
     * @param ex      最后一次重试时的异常
     * @param orderId 订单ID
     * @return 降级处理结果
     */
    @Recover
    public HttpResult<OrderInvoiceSnapshotDtoResp> recoverGetInvoiceSnapshot(Exception ex, String orderId) {
        log.error("获取订单发票快照重试全部失败，执行降级处理: orderId={}, finalError={}", orderId, ex.getMessage());
        // 根据业务需要选择合适的降级策略
        throw new RuntimeException("获取发票快照服务暂时不可用，已重试3次均失败: " + ex.getMessage(), ex);
    }

    /**
     * 带Spring Retry重试机制的更新发票状态方法
     * 重试3次，间隔1秒，指数退避
     *
     * @param invoiceNumber 发票编号
     * @param status        状态值
     * @return 更新结果
     */
    @Retryable(
            backoff = @Backoff(delay = 1000, multiplier = 2.0),
            include = {
                    ConnectException.class,
                    SocketException.class,
                    FeignException.class,
                    TimeoutException.class,
                    RetryableException.class,
                    RuntimeException.class
            },
            exclude = {IllegalArgumentException.class}
    )
    public HttpResult<Void> updateInvoiceStatusWithRetry(String invoiceNumber, Integer status) {
        log.info("尝试更新发票状态: invoiceNumber={}, status={}", invoiceNumber, status);
        try {
            HttpResult<Void> result = apiOrderServiceProvider.updateInvoiceStatus(invoiceNumber, status);
            if (result != null && result.success()) {
                log.info("成功更新发票状态: invoiceNumber={}, status={}", invoiceNumber, status);
                return result;
            } else {
                String errorMsg = "更新发票状态失败: " + (result != null ? result.getMsg() : "返回结果为空");
                log.warn("更新发票状态失败，将重试: invoiceNumber={}, status={}, error={}", invoiceNumber, status, errorMsg);
                throw new ServiceException(errorMsg);
            }
        } catch (Exception e) {
            log.error("更新发票状态异常，将重试: invoiceNumber={}, status={}, error={}", invoiceNumber, status, e.getMessage());
            throw e;
        }
    }

    /**
     * 更新发票状态重试失败后的降级处理方法
     * 当所有重试都失败后，会调用此方法
     *
     * @param ex            最后一次重试时的异常
     * @param invoiceNumber 发票编号
     * @param status        状态值
     * @return 降级处理结果
     */
    @Recover
    public HttpResult<Void> recoverUpdateInvoiceStatus(Exception ex, String invoiceNumber, Integer status) {
        log.error("更新发票状态重试全部失败，执行降级处理: invoiceNumber={}, status={}, finalError={}",
                invoiceNumber, status, ex.getMessage());
        throw new ServiceException("更新发票状态服务暂时不可用，已重试3次均失败: " + ex.getMessage(), ex);
    }
}
