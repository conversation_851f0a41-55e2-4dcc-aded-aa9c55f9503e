package com.knet.notification.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.knet.notification.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;
import com.knet.notification.service.IEmailCompositionService;
import com.knet.notification.service.IEmailTemplateService;
import com.knet.notification.system.utils.InvoiceDataParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 邮件内容组装服务实现类
 */
@Slf4j
@Service
public class EmailCompositionServiceImpl implements IEmailCompositionService {

    @Resource
    private IEmailTemplateService emailTemplateService;

    @Override
    public String generateEmailContent(OrderInvoiceSnapshotDtoResp invoiceSnapshot, String recipientName) {
        log.info("生成邮件内容（使用已有快照）: invoiceNumber={}, recipientName={}",
                invoiceSnapshot != null ? invoiceSnapshot.getInvoiceNumber() : "null", recipientName);
        try {
            if (invoiceSnapshot == null) {
                log.error("发票快照数据为空");
                return null;
            }
            String emailTemplate = emailTemplateService.getInvoiceEmailTemplate();
            return renderEmailContentFromSnapshot(emailTemplate, invoiceSnapshot, recipientName);
        } catch (Exception e) {
            log.error("生成邮件内容失败（使用已有快照）: invoiceNumber={}, error={}",
                    invoiceSnapshot != null ? invoiceSnapshot.getInvoiceNumber() : "null", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String generateEmailSubject(String orderId) {
        return com.knet.notification.constants.EmailTemplateConstants.INVOICE_EMAIL_SUBJECT_TEMPLATE
                .replace("[OrderNumber]", orderId);
    }

    @Override
    public String generatePdfFilename(String invoiceNumber) {
        return String.format("Invoice_%s.pdf", invoiceNumber);
    }


    /**
     * 渲染邮件内容（仅使用快照数据）
     * 邮件模板占位符：[Customer Name], [Order Number], [Order Date], [Order Quantity], [Order Total]
     * 发票以附件形式发送，邮件内容只包含订单确认信息
     */
    private String renderEmailContentFromSnapshot(String template, OrderInvoiceSnapshotDtoResp invoiceSnapshot, String recipientName) {
        try {
            String content = template;
            content = content.replace("[Customer Name]", StrUtil.blankToDefault(recipientName, "Valued Customer"));
            content = content.replace("[Order Number]", invoiceSnapshot.getOrderId());
            content = content.replace("[Order Date]", DateUtil.format(invoiceSnapshot.getInvoiceDate(), "MM/dd/yyyy"));
            content = content.replace("[Order Total]", "$" + invoiceSnapshot.getTotalAmount().toString());
            String orderQuantity = calculateTotalQuantityFromSnapshot(invoiceSnapshot.getOrderItems());
            content = content.replace("[Order Quantity]", orderQuantity);
            content = content.replace("\n", "<br>");
            return content;
        } catch (Exception e) {
            log.error("渲染邮件内容失败（仅使用快照数据）: error={}", e.getMessage(), e);
            throw new RuntimeException("渲染邮件内容失败", e);
        }
    }


    /**
     * 从快照JSON数据计算订单总数量
     */
    private String calculateTotalQuantityFromSnapshot(String orderItemsJson) {
        if (StrUtil.isBlank(orderItemsJson)) {
            return "0";
        }
        try {
            // 使用工具类解析订单项数据
            List<com.knet.notification.model.dto.InvoiceData.InvoiceItem> invoiceItems =
                    InvoiceDataParseUtil.parseOrderItems(orderItemsJson);

            if (invoiceItems == null || invoiceItems.isEmpty()) {
                return "0";
            }
            int totalQuantity = 0;
            for (com.knet.notification.model.dto.InvoiceData.InvoiceItem item : invoiceItems) {
                totalQuantity += (item.getQuantity() != null ? item.getQuantity() : 0);
            }
            return String.valueOf(totalQuantity);
        } catch (Exception e) {
            log.error("从快照JSON数据计算订单总数量失败: error={}", e.getMessage(), e);
            return "0";
        }
    }
}
