package com.knet.notification.service.impl;

import cn.hutool.core.util.StrUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.notification.constants.EmailTemplateConstants;
import com.knet.notification.service.IEmailTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 邮件模板管理服务实现类
 */
@Slf4j
@Service
public class EmailTemplateServiceImpl implements IEmailTemplateService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 应用启动完成后初始化默认邮件模板
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        initDefaultEmailTemplate();
    }

    @Override
    public String getInvoiceEmailTemplate() {
        try {
            Object templateObj = redisTemplate.opsForHash().get(EmailTemplateConstants.INVOICE_EMAIL_TEMPLATE_KEY,
                    EmailTemplateConstants.TEMPLATE_CONTENT_FIELD);
            String template = templateObj != null ? templateObj.toString() : null;
            if (StrUtil.isBlank(template)) {
                log.warn("Redis中未找到邮件模板，使用默认模板");
                return EmailTemplateConstants.DEFAULT_INVOICE_EMAIL_TEMPLATE;
            }
            return template;
        } catch (Exception e) {
            log.error("获取邮件模板失败: error={}", e.getMessage(), e);
            return EmailTemplateConstants.DEFAULT_INVOICE_EMAIL_TEMPLATE;
        }
    }

    @Override
    public void setInvoiceEmailTemplate(String template) {
        try {
            redisTemplate.opsForHash().put(EmailTemplateConstants.INVOICE_EMAIL_TEMPLATE_KEY,
                    EmailTemplateConstants.TEMPLATE_CONTENT_FIELD, template);
            log.info("邮件模板设置成功");
        } catch (Exception e) {
            log.error("设置邮件模板失败: error={}", e.getMessage(), e);
            throw new RuntimeException("设置邮件模板失败: " + e.getMessage());
        }
    }

    @Override
    public void initDefaultEmailTemplate() {
        try {
            // 检查Redis中是否已存在模板
            Object existingTemplateObj = redisTemplate.opsForHash().get(EmailTemplateConstants.INVOICE_EMAIL_TEMPLATE_KEY,
                    EmailTemplateConstants.TEMPLATE_CONTENT_FIELD);
            String existingTemplate = existingTemplateObj != null ? existingTemplateObj.toString() : null;
            if (StrUtil.isNotBlank(existingTemplate)) {
                log.info("邮件模板已存在，跳过初始化");
                return;
            }
            // 设置默认模板
            redisTemplate.opsForHash().put(EmailTemplateConstants.INVOICE_EMAIL_TEMPLATE_KEY,
                    EmailTemplateConstants.TEMPLATE_CONTENT_FIELD,
                    EmailTemplateConstants.DEFAULT_INVOICE_EMAIL_TEMPLATE);
            log.info("默认邮件模板初始化成功");
        } catch (Exception e) {
            log.error("初始化默认邮件模板失败: error={}", e.getMessage(), e);
        }
    }
}
