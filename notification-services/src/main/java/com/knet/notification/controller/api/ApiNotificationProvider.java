package com.knet.notification.controller.api;

import com.knet.common.annotation.Loggable;
import com.knet.common.base.HttpResult;
import com.knet.notification.model.dto.req.SendMessageRequest;
import com.knet.notification.model.dto.third.resp.OrderInvoiceSnapshotDtoResp;
import com.knet.notification.service.IApiNotificationService;
import com.knet.notification.service.IInvoiceGenerationService;
import com.knet.notification.service.impl.OrderServiceRetryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/20 13:22
 * @description: 通知服务对外提供服务
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = " 通知服务-对外提供接口", description = " 通知服务-对外提供接口")
public class ApiNotificationProvider {
    @Resource
    private IApiNotificationService apiNotificationService;
    @Resource
    private IInvoiceGenerationService invoiceGenerationService;
    @Resource
    private OrderServiceRetryWrapper orderServiceRetryWrapper;


    @Loggable(value = "发送消息通知")
    @PostMapping("/send/message")
    @Operation(summary = "发送消息通知", description = "供其他服务调用，发送消息通知")
    public HttpResult<Boolean> sendMessage(@Validated @RequestBody SendMessageRequest request) {
        log.info("发送消息通知: {}", request);
        try {
            boolean result = apiNotificationService.sendMessage(request);
            log.info("发送消息通知成功: {}", result);
            return HttpResult.ok(result);
        } catch (Exception e) {
            log.error("发送消息通知失败: {}", e.getMessage(), e);
            return HttpResult.error("发送消息通知失败: " + e.getMessage());
        }
    }

    @Loggable(value = "删除订单对应的发票")
    @DeleteMapping("/invoice/cancel/{orderId}")
    @Operation(summary = "删除订单对应的发票", description = "订单取消时删除对应的发票快照")
    public HttpResult<Boolean> cancelInvoiceByOrderId(@PathVariable("orderId") String orderId) {
        log.info("删除订单对应的发票: orderId={}", orderId);
        try {
            boolean result = apiNotificationService.cancelInvoiceByOrderId(orderId);
            log.info("删除订单对应的发票成功: orderId={}, result={}", orderId, result);
            return HttpResult.ok(result);
        } catch (Exception e) {
            log.error("删除订单对应的发票失败: orderId={}, error={}", orderId, e.getMessage(), e);
            return HttpResult.error("删除订单对应的发票失败: " + e.getMessage());
        }
    }

    /**
     * 根据发票快照生成PDF上传到S3的外部接口
     *
     * @param prentOrderId 订单ID
     * @return PDF文件S3地址
     */
    @PostMapping("/invoice/generate-pdf/{prentOrderId}")
    @Operation(summary = "生成发票PDF", description = "根据订单ID获取发票快照并生成PDF上传到S3，返回下载链接")
    public HttpResult<String> generateInvoicePdf(@Parameter(description = "订单ID", required = true) @PathVariable("prentOrderId") String prentOrderId) {
        log.info("生成发票PDF请求: prentOrderId={}", prentOrderId);
        try {
            HttpResult<OrderInvoiceSnapshotDtoResp> invoiceResult = orderServiceRetryWrapper.getInvoiceSnapshotByOrderIdWithRetry(prentOrderId);
            if (invoiceResult == null || !invoiceResult.success() || invoiceResult.getData() == null) {
                log.error("获取订单发票快照失败: prentOrderId={}, result={}", prentOrderId, invoiceResult);
                return HttpResult.error("获取发票快照失败");
            }
            OrderInvoiceSnapshotDtoResp invoiceSnapshot = invoiceResult.getData();
            String invoiceNumber = invoiceSnapshot.getInvoiceNumber();
            String pdfUrl = invoiceGenerationService.generateInvoicePdfNotUpdate(prentOrderId, invoiceNumber, invoiceSnapshot, null);
            if (pdfUrl == null) {
                log.error("生成发票PDF失败: prentOrderId={}, invoiceNumber={}", prentOrderId, invoiceNumber);
                return HttpResult.error("生成PDF失败");
            }
            log.info("发票PDF生成成功: prentOrderId={}, invoiceNumber={}, pdfUrl={}", prentOrderId, invoiceNumber, pdfUrl);
            return HttpResult.ok(pdfUrl);
        } catch (Exception e) {
            log.error("生成发票PDF异常: prentOrderId={}, error={}", prentOrderId, e.getMessage(), e);
            return HttpResult.error("生成PDF异常: " + e.getMessage());
        }
    }
}