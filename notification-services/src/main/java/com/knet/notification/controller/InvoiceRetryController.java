package com.knet.notification.controller;

import com.knet.common.base.HttpResult;
import com.knet.notification.service.IInvoiceRetryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/12/09
 * @description: 发票重试控制器
 */
@Slf4j
@RestController
@RequestMapping("/invoice/retry")
@Tag(name = "发票重试管理", description = "发票重试相关接口")
public class InvoiceRetryController {

    @Resource
    private IInvoiceRetryService invoiceRetryService;

    /**
     * 重新生成发票PDF
     *
     * @param invoiceNumber 发票编号
     * @return 重新生成结果
     */
    @PostMapping("/{invoiceNumber}")
    @Operation(summary = "重新生成发票PDF", description = "重新生成指定发票的PDF文件，不发送邮件")
    public HttpResult<Boolean> retryInvoiceGeneration(
            @Parameter(description = "发票编号", required = true)
            @PathVariable("invoiceNumber") String invoiceNumber) {
        log.info("重新生成发票PDF请求: invoiceNumber={}", invoiceNumber);
        try {
            boolean result = invoiceRetryService.retryInvoiceGeneration(invoiceNumber);
            if (result) {
                log.info("重新生成发票PDF成功: invoiceNumber={}", invoiceNumber);
                return HttpResult.ok(true);
            } else {
                log.error("重新生成发票PDF失败: invoiceNumber={}", invoiceNumber);
                return HttpResult.error("发票PDF重新生成失败");
            }
        } catch (Exception e) {
            log.error("重新生成发票PDF异常: invoiceNumber={}, error={}", invoiceNumber, e.getMessage(), e);
            return HttpResult.error("发票PDF重新生成异常: " + e.getMessage());
        }
    }

    /**
     * 检查发票是否需要重试
     *
     * @param invoiceNumber 发票编号
     * @return 是否需要重试
     */
    @GetMapping("/check/{invoiceNumber}")
    @Operation(summary = "检查发票是否需要重试", description = "检查指定发票是否需要重新生成PDF")
    public HttpResult<Boolean> checkNeedsRetry(
            @Parameter(description = "发票编号", required = true)
            @PathVariable("invoiceNumber") String invoiceNumber) {
        log.info("检查发票是否需要重试: invoiceNumber={}", invoiceNumber);
        try {
            boolean needsRetry = invoiceRetryService.needsRetry(invoiceNumber);
            log.info("检查发票重试状态完成: invoiceNumber={}, needsRetry={}", invoiceNumber, needsRetry);
            return HttpResult.ok(needsRetry);
        } catch (Exception e) {
            log.error("检查发票重试状态异常: invoiceNumber={}, error={}", invoiceNumber, e.getMessage(), e);
            return HttpResult.error("检查发票重试状态异常: " + e.getMessage());
        }
    }

    /**
     * 批量重试失败的发票
     *
     * @return 重试成功的数量
     */
    @PostMapping("/batch")
    @Operation(summary = "批量重试失败的发票", description = "批量重新生成所有失败发票的PDF文件")
    public HttpResult<Integer> batchRetryFailedInvoices() {
        log.info("批量重试失败的发票请求");
        try {
            int successCount = invoiceRetryService.batchRetryFailedInvoices();
            log.info("批量重试失败的发票完成: successCount={}", successCount);
            return HttpResult.ok(successCount);
        } catch (Exception e) {
            log.error("批量重试失败的发票异常: error={}", e.getMessage(), e);
            return HttpResult.error("批量重试失败的发票异常: " + e.getMessage());
        }
    }
}
