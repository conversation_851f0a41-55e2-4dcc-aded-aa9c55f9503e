server:
  port: 7008
spring:
  application:
    name: exports-services #服务名称 必须要有
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.6.5:8848 #服务注册中心地址
        namespace: 5139d7fa-db43-42ae-9697-509bddd0cbd6 #命名空间
      config:
        server-addr: 192.168.6.5:8848 #配置中心地址
        file-extension: yaml #指定yaml格式的配置
        group: DEFAULT_GROUP
        namespace: 5139d7fa-db43-42ae-9697-509bddd0cbd6

# 导出服务配置
knet:
  export:
    max-concurrent-tasks: 3
    max-memory-usage: 1073741824  # 1GB
    default-page-size: 1000
    max-records-per-export: 100000
    default-expire-days: 7
    task-lock-expire-seconds: 1800
    temp-directory: /tmp/export
    s3:
      bucket-name: knet-exports
      region: us-east-1
      access-key: ${AWS_ACCESS_KEY:}
      secret-key: ${AWS_SECRET_KEY:}
      endpoint: ${AWS_ENDPOINT:}
      presigned-url-expire-hours: 24