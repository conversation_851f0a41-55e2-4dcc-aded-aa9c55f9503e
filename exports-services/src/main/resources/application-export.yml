# 导出服务配置示例
knet:
  export:
    # 每个实例最大并发任务数（建议3-5个）
    max-concurrent-tasks: 3
    # 内存使用限制（1GB）
    max-memory-usage: 1073741824
    # 默认分页大小
    default-page-size: 1000
    # 最大导出记录数
    max-records-per-export: 100000
    # 任务默认过期天数
    default-expire-days: 7
    # 任务锁过期时间（秒，30分钟）
    task-lock-expire-seconds: 1800
    # 临时文件目录
    temp-directory: /tmp/export
    # S3存储配置
    s3:
      bucket-name: knet-exports
      file-prefix: exports/
      region: us-east-1

# XXL-Job配置
xxl:
  job:
    admin:
      addresses: http://localhost:8080/xxl-job-admin
    executor:
      appname: export-service
      address: 
      ip: 
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30

# 线程池配置
export:
  thread-pool:
    core-size: 4
    max-size: 12
    keep-alive-time: 60
    queue-capacity: 100
