package com.knet.export.openfeign;

import com.knet.common.base.HttpResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 订单服务导出接口
 * export-services调用order-services的导出数据接口
 *
 * <AUTHOR> Export Team
 * @since 2025-09-25
 */
@FeignClient(name = "order-services", path = "/orderService/api")
@Tag(name = "订单服务导出Provider", description = "export-services调用order-services的导出数据接口")
public interface ApiOrderServiceProvider {

    /**
     * 流式查询订单导出数据
     *
     * @param exportParams 导出参数
     * @param pageNo       当前页码
     * @param pageSize     分页大小
     * @return 订单数据列表
     */
    @Operation(summary = "流式查询订单导出数据", description = "分页查询订单数据用于导出")
    @PostMapping("/export/data/admin/query")
    HttpResult<List<Map<String, Object>>> queryAdminOrderExportData(
            @Parameter(description = "导出参数", required = true)
            @RequestBody Map<String, Object> exportParams,
            @Parameter(description = "当前页码", required = true)
            @RequestParam(value = "pageNo") int pageNo,
            @Parameter(description = "分页大小", required = true)
            @RequestParam("pageSize") int pageSize
    );
}
