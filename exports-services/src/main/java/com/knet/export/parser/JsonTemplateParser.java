package com.knet.export.parser;

import com.alibaba.fastjson2.JSON;
import com.knet.common.exception.ServiceException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * JSON模板解析器
 * 解析简化的JSON导出模板配置
 *
 * <AUTHOR> Export Team
 * @since 2025-09-25
 */
@Slf4j
@Component
@Schema(description = "JSON模板解析器")
public class JsonTemplateParser {

    /**
     * 解析简化的JSON模板
     *
     * @param templateJson JSON模板字符串
     * @return 解析后的模板对象
     */
    public SimpleTemplate parseTemplate(String templateJson) {
        try {
            if (!StringUtils.hasText(templateJson)) {
                throw new IllegalArgumentException("Template JSON cannot be empty");
            }
            // 移除UTF-8 BOM和其他不可见字符
            SimpleTemplate template = JSON.parseObject(templateJson, SimpleTemplate.class);
            validateAndEnhanceTemplate(template);
            log.info("Successfully parsed template with {} columns", template.getColumns().size());
            return template;
        } catch (Exception e) {
            log.error("Failed to parse template JSON: {}", templateJson, e);
            throw new ServiceException("Invalid template JSON format", e);
        }
    }

    /**
     * 验证并增强模板配置
     */
    private void validateAndEnhanceTemplate(SimpleTemplate template) {
        if (template == null) {
            throw new IllegalArgumentException("Template cannot be null");
        }
        List<ColumnConfig> columns = template.getColumns();
        if (columns == null || columns.isEmpty()) {
            throw new IllegalArgumentException("Template must have at least one column");
        }
        // 验证和增强每个列配置
        for (int i = 0; i < columns.size(); i++) {
            ColumnConfig column = columns.get(i);
            if (column == null) {
                throw new IllegalArgumentException("Column at index " + i + " is null");
            }
            if (!StringUtils.hasText(column.getField())) {
                throw new IllegalArgumentException("Column field cannot be empty at index " + i);
            }
            if (!StringUtils.hasText(column.getTitle())) {
                column.setTitle(column.getField()); // 默认使用字段名作为标题
            }
            // 自动识别字段类型和格式
            enhanceColumnConfig(column);
        }
        log.debug("Template validation and enhancement completed");
    }

    /**
     * 增强列配置（自动识别类型和格式）
     */
    private void enhanceColumnConfig(ColumnConfig column) {
        String field = column.getField().toLowerCase();
        // 设置默认列宽
        if (column.getWidth() == null) {
            column.setWidth(15);
        }
        // 根据字段名自动识别数据类型和格式
        if (field.contains("time") || field.contains("date")) {
            column.setType("datetime");
            column.setFormat("yyyy-MM-dd HH:mm:ss");
        } else if (field.contains("amount") || field.contains("price") || field.contains("money")) {
            column.setType("number");
            column.setFormat("#,##0.00");
        } else if (field.contains("count") || field.contains("quantity") || field.contains("num")) {
            column.setType("integer");
            column.setFormat("#,##0");
        } else {
            column.setType("string");
        }
        log.debug("Enhanced column config: field={}, type={}, format={}",
                column.getField(), column.getType(), column.getFormat());
    }

    /**
     * 简化模板数据结构
     */
    @Data
    @Schema(description = "简化导出模板")
    public static class SimpleTemplate {
        @Schema(description = "列配置列表")
        private List<ColumnConfig> columns;

        @Schema(description = "工作表名称", example = "订单列表")
        private String sheetName = "数据导出"; // 默认工作表名称
    }

    /**
     * 列配置
     */
    @Data
    @Schema(description = "列配置")
    public static class ColumnConfig {
        @Schema(description = "字段名", example = "orderId")
        private String field;

        @Schema(description = "列标题", example = "Order ID")
        private String title;

        @Schema(description = "数据类型", example = "string")
        private String type;

        @Schema(description = "格式化规则", example = "yyyy-MM-dd HH:mm:ss")
        private String format;

        @Schema(description = "列宽", example = "15")
        private Integer width;
    }
}
