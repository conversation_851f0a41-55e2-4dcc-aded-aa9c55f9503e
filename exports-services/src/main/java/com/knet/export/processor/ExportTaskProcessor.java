package com.knet.export.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.knet.common.utils.S3FileUtil;
import com.knet.export.model.entity.SysExportTask;
import com.knet.export.model.enums.ExportTaskStatus;
import com.knet.export.parser.JsonTemplateParser;
import com.knet.export.service.IExportTaskService;
import com.knet.export.system.utils.EasyExcelExportUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 导出任务处理器
 * 执行具体的导出任务逻辑
 *
 * <AUTHOR> Export Team
 * @since 2025-09-25
 */
@Slf4j
@Component
@Schema(description = "导出任务处理器")
public class ExportTaskProcessor {

    @Resource
    private IExportTaskService exportTaskService;
    @Resource
    private JsonTemplateParser jsonTemplateParser;
    @Resource
    private EasyExcelExportUtils excelExportUtils;
    @Resource
    private S3FileUtil s3FileUtil;

    /**
     * 执行导出任务
     *
     * @param taskId 任务ID
     * @return 是否执行成功
     */
    public boolean processExportTask(String taskId) {
        log.info("开始处理导出任务: {}", taskId);
        try {
            // 1. 获取任务信息
            SysExportTask task = exportTaskService.getTaskEntity(taskId);
            if (task == null) {
                log.error("任务不存在: {}", taskId);
                return false;
            }
            // 2. 更新任务状态为处理中
            exportTaskService.updateTaskStatus(taskId, ExportTaskStatus.PROCESSING, "开始处理导出任务");
            // 3. 解析JSON模板
            JsonTemplateParser.SimpleTemplate template = jsonTemplateParser.parseTemplate(task.getTemplateJson());
            // 4. 获取导出参数
            Map<String, Object> exportParams = parseExportParams(task.getExportParams());
            File tempFile = createTempFile(task.getFileName());
            // 流式导出数据到Excel
            long actualCount = excelExportUtils.exportDataToExcel(task, template, tempFile, exportParams);
            if (actualCount < 0) {
                exportTaskService.updateTaskStatus(taskId, ExportTaskStatus.FAILED, "数据导出失败");
                return false;
            }
            //  上传文件到S3
            String s3FileKey = uploadFileToS3(tempFile, taskId);
            if (s3FileKey == null) {
                exportTaskService.updateTaskStatus(taskId, ExportTaskStatus.FAILED, "文件上传失败");
                return false;
            }
            //  调用updateTaskComplete方法更新任务完成信息
            exportTaskService.updateTaskComplete(
                    taskId,
                    tempFile.getName(),
                    s3FileKey,
                    tempFile.length(),
                    (int) actualCount
            );
            cleanupTempFile(tempFile);
            log.info("导出任务完成: {}, 文件: {}, 记录数: {}", taskId, s3FileKey, actualCount);
            return true;
        } catch (Exception e) {
            log.error("处理导出任务异常: {}", taskId, e);
            exportTaskService.updateTaskStatus(taskId, ExportTaskStatus.FAILED, "处理异常: " + e.getMessage());
            return false;
        }
    }


    /**
     * 解析导出参数
     */
    private Map<String, Object> parseExportParams(String exportParamsStr) {
        try {
            if (exportParamsStr == null || exportParamsStr.trim().isEmpty() || "{}".equals(exportParamsStr.trim())) {
                return new java.util.HashMap<>();
            }
            // 使用JSON解析导出参数
            Map<String, Object> stringObjectMap = JSON.parseObject(exportParamsStr, new TypeReference<>() {
            });
            return stringObjectMap;
        } catch (Exception e) {
            log.error("解析导出参数失败: {}", exportParamsStr, e);
            return new HashMap<>(12);
        }
    }

    /**
     * 创建临时文件
     */
    private File createTempFile(String fileNamePrefix) throws IOException {
        String tempDir = System.getProperty("java.io.tmpdir");
        File exportDir = new File(tempDir, "export");
        if (!exportDir.exists()) {
            exportDir.mkdirs();
        }
        String fileName = fileNamePrefix + ".xlsx";
        return new File(exportDir, fileName);
    }

    /**
     * 上传文件到S3（返回S3 FileKey）
     */
    private String uploadFileToS3(File file, String taskId) {
        try {
            // 这里应该调用实际的S3上传服务
            // 返回S3 FileKey而不是完整URL
            String s3FileKey = "exports/" + taskId + ".xlsx";
            // 实际项目中应该调用S3客户端上传文件
            // s3Client.uploadFile(file, s3FileKey);
            // todo 临时保存到项目根目录下的 excel 文件夹 测试
            Path directory = Paths.get("excel");
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
            }
            String fileName = file.getName();
            Path filePath = directory.resolve(fileName);
            Files.copy(file.toPath(), filePath);
            /*            return s3FileUtil.uploadFile( FileUtil.readBytes(file), fileName);*/
            log.info("文件上传成功: {}", s3FileKey);
            return s3FileKey;
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return null;
        }
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFile(File tempFile) {
        try {
            if (tempFile.exists() && tempFile.delete()) {
                log.info("临时文件清理成功: {}", tempFile.getPath());
            }
        } catch (Exception e) {
            log.error("临时文件清理失败: {}", tempFile.getPath(), e);
        }
    }
}
