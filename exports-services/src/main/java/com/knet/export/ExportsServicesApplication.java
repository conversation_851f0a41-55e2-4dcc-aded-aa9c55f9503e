package com.knet.export;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2025/2/12 15:10
 * @description: 导出服务 主启动类
 */
@EnableScheduling
@ComponentScan(basePackages = {"com.knet.export", "com.knet.common"})
@EnableAsync
@MapperScan("com.knet.export.mapper")
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
public class ExportsServicesApplication {
    public static void main(String[] args) {
        //禁用Log4j JNDI
        System.setProperty("log4j2.formatMsgNoLookups", "true");
        SpringApplication.run(ExportsServicesApplication.class);
        System.out.println(" 🚀Exports Service started successfully!");
    }

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
