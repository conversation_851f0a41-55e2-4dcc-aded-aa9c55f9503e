package com.knet.export.manager;

import com.knet.export.model.entity.SysExportTask;
import com.knet.export.openfeign.ApiOrderServiceProvider;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * 数据提供者管理器
 * 通过OpenFeign调用各业务服务的数据接口
 *
 * <AUTHOR> Export Team
 * @since 2025-09-25
 */
@Slf4j
@Component
@Schema(description = "数据提供者管理器")
public class DataProviderManager {

    @Resource
    private ApiOrderServiceProvider apiOrderServiceProvider;

    /**
     * 流式查询导出数据
     *
     * @param task         导出任务
     * @param exportParams 导出参数
     * @param pageNo       当前页码
     * @param pageSize     分页大小
     * @return 数据流
     */
    public Stream<Map<String, Object>> queryExportData(
            SysExportTask task,
            Map<String, Object> exportParams,
            int pageNo,
            int pageSize) {
        log.info("查询导出数据 - systemId: {}, dataProvider: {}, exportType: {}, pageNo: {}, pageSize: {}",
                task.getSystemId(), task.getDataProvider(), task.getExportType(), pageNo, pageSize);
        try {
            return queryExportDataLegacy(task, exportParams, pageNo, pageSize);
        } catch (Exception e) {
            log.error("查询导出数据异常 - systemId: {}, dataProvider: {}", task.getSystemId(), task.getDataProvider(), e);
            return Stream.empty();
        }
    }

    /**
     * 通过OpenFeign调用各业务服务的数据接口
     *
     * @param task         导出任务
     * @param exportParams 导出参数
     * @param pageNo       当前页码
     * @param pageSize     分页大小
     * @return 数据流
     */
    private Stream<Map<String, Object>> queryExportDataLegacy(
            SysExportTask task, Map<String, Object> exportParams, int pageNo, int pageSize) {
        switch (task.getExportType()) {
            case ADMIN_ORDER_LIST:
                List<Map<String, Object>> orderData = apiOrderServiceProvider.queryAdminOrderExportData(exportParams, pageNo, pageSize).getData();
                return orderData != null ? orderData.stream() : Stream.empty();
            case USER_LIST:
                return Stream.empty();
            // 可以继续添加其他系统的数据提供者
            default:
                log.error("不支持的导出类型: {}", task.getExportType());
                return Stream.empty();
        }
    }
}
