package com.knet.export.manager;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Comparator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;

/**
 * 导出资源管理器
 * 控制并发导出任务数量和内存使用
 *
 * <AUTHOR> Export Team
 * @since 2025-09-24
 */
@Slf4j
@Component
public class ExportResourceManager {

    private static final int MAX_CONCURRENT_EXPORTS = 3; // 最多3个并发导出
    private static final long MAX_MEMORY_USAGE = 1024 * 1024 * 1024; // 1GB内存限制

    private final Semaphore concurrentLimit = new Semaphore(MAX_CONCURRENT_EXPORTS);
    private final Map<String, ExportTaskInfo> runningTasks = new ConcurrentHashMap<>();

    /**
     * 尝试获取导出资源
     */
    public boolean acquireResource(String taskId, long estimatedMemory) {
        // 检查内存限制
        if (getCurrentMemoryUsage() + estimatedMemory > MAX_MEMORY_USAGE) {
            log.warn("内存使用量超限，拒绝任务: {}", taskId);
            return false;
        }
        // 尝试获取并发许可
        if (concurrentLimit.tryAcquire()) {
            ExportTaskInfo taskInfo = new ExportTaskInfo(taskId, estimatedMemory, System.currentTimeMillis());
            runningTasks.put(taskId, taskInfo);
            log.info("获取导出资源成功, taskId: {}, 当前并发数: {}", taskId, MAX_CONCURRENT_EXPORTS - concurrentLimit.availablePermits());
            return true;
        } else {
            log.warn("并发导出数量已达上限，拒绝任务: {}", taskId);
            return false;
        }
    }

    /**
     * 释放导出资源
     */
    public void releaseResource(String taskId) {
        ExportTaskInfo taskInfo = runningTasks.remove(taskId);
        if (taskInfo != null) {
            concurrentLimit.release();
            cleanupTempFiles(taskId);
            log.info("释放导出资源, taskId: {}, 执行时长: {}ms", taskId, System.currentTimeMillis() - taskInfo.getStartTime());
        }
    }

    /**
     * 获取当前内存使用量
     */
    private long getCurrentMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        return runtime.totalMemory() - runtime.freeMemory();
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(String taskId) {
        try {
            String tempDir = System.getProperty("java.io.tmpdir");
            Path taskTempDir = Paths.get(tempDir, "export", taskId);
            if (Files.exists(taskTempDir)) {
                Files.walk(taskTempDir)
                        .sorted(Comparator.reverseOrder())
                        .map(Path::toFile)
                        .forEach(File::delete);
                log.info("清理临时文件完成: {}", taskId);
            }
        } catch (Exception e) {
            log.error("清理临时文件失败: {}", taskId, e);
        }
    }

    /**
     * 获取当前运行的任务数量
     */
    public int getCurrentRunningTaskCount() {
        return MAX_CONCURRENT_EXPORTS - concurrentLimit.availablePermits();
    }

    /**
     * 获取可用的并发槽数
     */
    public int getAvailableSlots() {
        return concurrentLimit.availablePermits();
    }

    /**
     * 获取所有运行中的任务信息
     */
    public Map<String, ExportTaskInfo> getRunningTasks() {
        return new ConcurrentHashMap<>(runningTasks);
    }

    /**
     * 强制释放指定任务的资源（用于异常恢复）
     */
    public void forceReleaseResource(String taskId) {
        ExportTaskInfo taskInfo = runningTasks.remove(taskId);
        if (taskInfo != null) {
            concurrentLimit.release();
            log.warn("强制释放导出资源: {}", taskId);
        }
    }

    /**
     * 任务信息内部类
     */
    @Data
    public static class ExportTaskInfo {
        private String taskId;
        private long estimatedMemory;
        private long startTime;

        public ExportTaskInfo(String taskId, long estimatedMemory, long startTime) {
            this.taskId = taskId;
            this.estimatedMemory = estimatedMemory;
            this.startTime = startTime;
        }
    }
}
