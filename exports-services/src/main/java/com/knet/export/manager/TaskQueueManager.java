package com.knet.export.manager;

import com.knet.common.utils.RedisCacheUtil;
import com.knet.export.model.enums.ExportTaskStatus;
import com.knet.export.service.IExportTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

import static com.knet.common.constants.ExportServiceConstants.*;

/**
 * 基于Redis的任务队列管理器
 * 实现FIFO队列、分布式锁、超时监控
 *
 * <AUTHOR> Export Team
 * @since 2025-09-24
 */
@Slf4j
@Component
public class TaskQueueManager {

    @Resource
    private IExportTaskService iExportTaskService;

    /**
     * 添加任务到队列（FIFO）
     */
    public boolean addToQueue(String taskId) {
        try {
            boolean result = RedisCacheUtil.lSet(TASK_QUEUE_KEY, taskId);
            log.info("Task added to queue: {}", taskId);
            return result;
        } catch (Exception e) {
            log.error("Failed to add task to queue: {}", taskId, e);
            return false;
        }
    }

    /**
     * 从队列获取任务（FIFO，非阻塞获取）
     * 使用 LPOP 操作确保任务只被获取一次
     */
    public String pollTask() {
        try {
            Object result = RedisCacheUtil.lLeftPop(TASK_QUEUE_KEY);
            if (result != null) {
                String taskId = result.toString();
                log.info("Task polled from queue: {}", taskId);
                return taskId;
            }
            return null;
        } catch (Exception e) {
            log.error("Failed to poll task from queue", e);
            return null;
        }
    }

    /**
     * 获取队列大小
     */
    public Long getQueueSize() {
        try {
            return RedisCacheUtil.lGetListSize(TASK_QUEUE_KEY);
        } catch (Exception e) {
            log.error("Failed to get queue size", e);
            return 0L;
        }
    }

    /**
     * 获取任务执行锁
     */
    public boolean acquireLock(String taskId) {
        return acquireLock(taskId, DEFAULT_LOCK_EXPIRE_SECONDS);
    }

    /**
     * 获取任务执行锁（带超时）
     */
    public boolean acquireLock(String taskId, int expireSeconds) {
        try {
            String lockKey = TASK_LOCK_PREFIX + taskId;
            boolean acquired = RedisCacheUtil.setIfAbsent(lockKey, "locked", expireSeconds);
            if (acquired) {
                log.info("Task lock acquired: {}, expire in {} seconds", taskId, expireSeconds);
                // 使用更短的超时时间进行监控，避免任务长时间占用资源
                long timeoutTimestamp = System.currentTimeMillis() + TASK_TIMEOUT_SECONDS * 1000L;
                addTimeoutMonitor(taskId, timeoutTimestamp);
                return true;
            } else {
                log.warn("Task lock acquisition failed, may be processing by another instance: {}", taskId);
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to acquire task lock: {}", taskId, e);
            return false;
        }
    }

    /**
     * 释放任务锁
     */
    public void releaseLock(String taskId) {
        try {
            String lockKey = TASK_LOCK_PREFIX + taskId;
            RedisCacheUtil.del(lockKey);
            removeTimeoutMonitor(taskId);
            log.info("Task lock released: {}", taskId);
        } catch (Exception e) {
            log.error("Failed to release task lock: {}", taskId, e);
        }
    }

    /**
     * 添加超时监控
     */
    private void addTimeoutMonitor(String taskId, long timeoutTimestamp) {
        try {
            RedisCacheUtil.addZSet(TIMEOUT_MONITOR_KEY, taskId, timeoutTimestamp);
            log.debug("Timeout monitor added for task: {}, timeout: {}", taskId, timeoutTimestamp);
        } catch (Exception e) {
            log.error("Failed to add timeout monitor for task: {}", taskId, e);
        }
    }

    /**
     * 移除超时监控
     */
    private void removeTimeoutMonitor(String taskId) {
        try {
            RedisCacheUtil.removeZSet(TIMEOUT_MONITOR_KEY, taskId);
            log.debug("Timeout monitor removed for task: {}", taskId);
        } catch (Exception e) {
            log.error("Failed to remove timeout monitor for task: {}", taskId, e);
        }
    }

    /**
     * 清理超时任务
     */
    public void cleanupTimeoutTasks() {
        try {
            long currentTime = System.currentTimeMillis();
            Set<Object> timeoutTasks = RedisCacheUtil.getZSet(TIMEOUT_MONITOR_KEY, currentTime);
            if (timeoutTasks != null && !timeoutTasks.isEmpty()) {
                for (Object taskObj : timeoutTasks) {
                    String taskId = taskObj.toString();
                    log.warn("Cleaning up timeout task: {}", taskId);
                    releaseLock(taskId);
                    iExportTaskService.updateTaskStatus(taskId, ExportTaskStatus.EXPIRED, "任务超时");
                }
                // 从监控集合中移除已超时的任务
                RedisCacheUtil.delZSetByScore(TIMEOUT_MONITOR_KEY, currentTime);
                log.info("Cleaned up {} timeout tasks", timeoutTasks.size());
            }
        } catch (Exception e) {
            log.error("Failed to cleanup timeout tasks", e);
        }
    }
}
