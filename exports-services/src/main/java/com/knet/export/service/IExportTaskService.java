package com.knet.export.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.export.model.dto.ExportRequestDto;
import com.knet.export.model.dto.req.ExportTaskQueryRequest;
import com.knet.export.model.dto.resp.ExportTaskQueryResponse;
import com.knet.export.model.entity.SysExportTask;
import com.knet.export.model.enums.ExportTaskStatus;

/**
 * 导出任务服务接口
 *
 * <AUTHOR> Export Team
 * @since 2025-09-24
 */
public interface IExportTaskService extends IService<SysExportTask> {

    /**
     * 创建导出任务
     *
     * @param requestDto 导出请求
     * @return 任务ID
     */
    String createTask(ExportRequestDto requestDto);

    /**
     * 更新任务状态
     *
     * @param taskId       任务ID
     * @param status       新状态
     * @param errorMessage 错误信息（可选）
     */
    void updateTaskStatus(String taskId, ExportTaskStatus status, String errorMessage);

    /**
     * 更新任务完成信息
     *
     * @param taskId      任务ID
     * @param fileName    文件名
     * @param s3FileKey   S3文件Key
     * @param fileSize    文件大小
     * @param recordCount 记录数
     */
    void updateTaskComplete(String taskId, String fileName, String s3FileKey, long fileSize, int recordCount);

    /**
     * 分页查询导出任务列表
     *
     * @param request 查询请求
     * @return 分页结果
     */
    IPage<ExportTaskQueryResponse> listTasks(ExportTaskQueryRequest request);

    /**
     * 根据任务ID获取任务实体
     *
     * @param taskId 任务ID
     * @return 任务实体
     */
    SysExportTask getTaskEntity(String taskId);

    /**
     * 删除导出任务
     *
     * @param taskId taskId
     */
    void deleteTaskByTaskId(String taskId);
}
