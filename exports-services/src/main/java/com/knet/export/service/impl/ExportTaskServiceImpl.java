package com.knet.export.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.context.UserContext;
import com.knet.common.exception.ServiceException;
import com.knet.export.mapper.ExportTaskMapper;
import com.knet.export.model.dto.ExportRequestDto;
import com.knet.export.model.dto.req.ExportTaskQueryRequest;
import com.knet.export.model.dto.resp.ExportTaskQueryResponse;
import com.knet.export.model.entity.SysExportTask;
import com.knet.export.model.enums.ExportTaskStatus;
import com.knet.export.service.IExportTaskService;
import com.knet.export.system.utils.ExportIdGenerator;
import com.knet.export.system.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 导出任务服务实现类
 *
 * <AUTHOR> Export Team
 * @since 2025-09-24
 */
@Slf4j
@Service
public class ExportTaskServiceImpl extends ServiceImpl<ExportTaskMapper, SysExportTask> implements IExportTaskService {
    @Resource
    private JwtUtil jwtUtil;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public String createTask(ExportRequestDto requestDto) {
        try {
            String taskId = ExportIdGenerator.generateTaskId();
            SysExportTask task = SysExportTask.builder()
                    .taskId(taskId)
                    .systemId(requestDto.getSystemId())
                    .templateJson(requestDto.getTemplateJson())
                    .exportParams(requestDto.getExportParams() != null ?
                            JSON.toJSONString(requestDto.getExportParams()) : null)
                    .status(ExportTaskStatus.QUEUED)
                    .retryCount(0)
                    .fileName(requestDto.getFileNamePrefix())
                    .dataProvider(requestDto.getDataProvider())
                    .exportType(requestDto.getExportType())
                    .createBy(requestDto.getUserId())
                    .build();
            boolean result = this.save(task);
            if (result) {
                log.info("导出任务创建成功: taskId={}, systemId={}, userId={}", taskId, requestDto.getSystemId(), requestDto.getUserId());
                return taskId;
            } else {
                throw new ServiceException("Failed to create export task");
            }
        } catch (Exception e) {
            log.error("创建导出任务失败: {}", e.getMessage(), e);
            throw new ServiceException("创建导出任务失败: " + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateTaskStatus(String taskId, ExportTaskStatus status, String errorMessage) {
        try {
            LambdaQueryWrapper<SysExportTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysExportTask::getTaskId, taskId);
            SysExportTask currentTask = baseMapper.selectOne(queryWrapper);
            if (currentTask == null) {
                log.warn("任务状态更新失败，任务不存在: taskId={}", taskId);
                return;
            }
            SysExportTask updateTask = new SysExportTask();
            updateTask.setId(currentTask.getId());
            updateTask.setVersion(currentTask.getVersion());
            updateTask.setStatus(status);
            if (ExportTaskStatus.PROCESSING == status) {
                updateTask.setStartTime(new Date());
            } else if (ExportTaskStatus.EXPIRED == status) {
                updateTask.setExpireTime(new Date());
            }
            LambdaUpdateWrapper<SysExportTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(SysExportTask::getTaskId, taskId);
            int result = baseMapper.update(updateTask, updateWrapper);
            if (result > 0) {
                log.info("任务状态更新成功: taskId={}, status={}", taskId, status);
            } else {
                log.warn("任务状态更新失败，任务不存在: taskId={}", taskId);
            }
        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}, status={}, error={}", taskId, status, e.getMessage(), e);
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateTaskComplete(String taskId, String fileName, String s3FileKey, long fileSize, int recordCount) {
        try {
            LambdaUpdateWrapper<SysExportTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(SysExportTask::getTaskId, taskId)
                    .set(SysExportTask::getStatus, ExportTaskStatus.COMPLETED)
                    .set(SysExportTask::getFileName, fileName)
                    .set(SysExportTask::getS3FileKey, s3FileKey)
                    .set(SysExportTask::getFileSize, fileSize)
                    .set(SysExportTask::getRecordCount, recordCount)
                    .set(SysExportTask::getCompleteTime, LocalDateTime.now());
            int result = baseMapper.update(null, updateWrapper);
            if (result > 0) {
                log.info("任务完成信息更新成功: taskId={}, fileName={}, recordCount={}", taskId, fileName, recordCount);
            } else {
                log.warn("任务完成信息更新失败，任务不存在: taskId={}", taskId);
            }
        } catch (Exception e) {
            log.error("更新任务完成信息失败: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }


    @Override
    public SysExportTask getTaskEntity(String taskId) {
        LambdaQueryWrapper<SysExportTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(SysExportTask::getTaskId, taskId)
                .last("LIMIT 1");
        List<SysExportTask> tasks = baseMapper.selectList(queryWrapper);
        return CollUtil.isEmpty(tasks) ? null : tasks.get(0);
    }

    @Override
    public void deleteTaskByTaskId(String taskId) {
        long userId = Long.parseLong(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        LambdaQueryWrapper<SysExportTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(SysExportTask::getTaskId, taskId)
                .eq(SysExportTask::getCreateBy, userId);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public IPage<ExportTaskQueryResponse> listTasks(ExportTaskQueryRequest request) {
        log.info("分页查询导出任务列表，请求参数: {}", request);
        long userId = Long.parseLong(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        LambdaQueryWrapper<SysExportTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(BeanUtil.isNotEmpty(request.getStatus()), SysExportTask::getStatus, request.getStatus())
                .eq(SysExportTask::getCreateBy, userId)
                .orderByDesc(SysExportTask::getCreateTime);
        Page<SysExportTask> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<SysExportTask> taskPage = this.page(page, queryWrapper);
        IPage<ExportTaskQueryResponse> responsePage = taskPage.convert(ExportTaskQueryResponse::convertToResponse);
        log.info("分页查询导出任务列表完成，总数: {}, 当前页数据: {}", responsePage.getTotal(), responsePage.getRecords().size());
        return responsePage;
    }
}
