package com.knet.export.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.annotation.PermissionCheck;
import com.knet.common.base.HttpResult;
import com.knet.export.manager.TaskQueueManager;
import com.knet.export.model.dto.req.ExportTaskQueryRequest;
import com.knet.export.model.dto.resp.ExportTaskQueryResponse;
import com.knet.export.service.IExportTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 导出服务控制器
 * 提供导出任务的REST API接口
 *
 * <AUTHOR> Export Team
 * @since 2025-09-24
 */
@Slf4j
@RestController
@RequestMapping("/export")
@Validated
@Tag(name = "导出服务API", description = "提供异步文件导出功能的REST API接口")
public class ExportController {
    @Resource
    private IExportTaskService exportTaskService;
    @Resource
    private TaskQueueManager taskQueueManager;

    @PermissionCheck(role = "admin")
    @Operation(summary = "查询队列状态", description = "查询当前导出任务队列的状态信息（管理员功能）")
    @GetMapping("/task/queue/status")
    public HttpResult<Map<String, Object>> getQueueStatus() {
        try {
            Long queueSize = taskQueueManager.getQueueSize();
            Map<String, Object> data = new HashMap<>();
            data.put("queueSize", queueSize);
            data.put("message", "队列中有 " + queueSize + " 个待处理任务");
            return HttpResult.ok(data);
        } catch (Exception e) {
            log.error("查询队列状态失败: {}", e.getMessage(), e);
            return HttpResult.error("查询队列状态失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询导出任务列表
     *
     * @param request 查询请求参数
     * @return 分页任务列表
     */
    @PermissionCheck(role = "admin")
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Operation(summary = "分页查询导出任务列表")
    @PostMapping("/task/list")
    public HttpResult<IPage<ExportTaskQueryResponse>> listTasks(@RequestBody ExportTaskQueryRequest request) {
        log.info("分页查询导出任务列表，请求参数: {}", request);
        IPage<ExportTaskQueryResponse> result = exportTaskService.listTasks(request);
        return HttpResult.ok(result);
    }


    @Operation(summary = "删除导出任务")
    @Loggable(value = "删除导出任务")
    @DistributedLock(key = "'deleteExportTask:'+#taskId", expire = 2)
    @PermissionCheck(role = "admin")
    @DeleteMapping("/{taskId}")
    public HttpResult<Void> deleteTask(@PathVariable String taskId) {
        log.info("删除导出任务: taskId: {}", taskId);
        exportTaskService.deleteTaskByTaskId(taskId);
        log.info("删除导出任务");
        return HttpResult.ok();
    }
}
