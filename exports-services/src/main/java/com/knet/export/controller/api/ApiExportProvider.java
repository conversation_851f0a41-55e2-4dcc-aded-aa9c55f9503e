package com.knet.export.controller.api;

import com.knet.common.base.HttpResult;
import com.knet.export.manager.TaskQueueManager;
import com.knet.export.model.dto.ExportRequestDto;
import com.knet.export.service.IExportTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/9/24 17:10
 * @description: 导出服务-对外提供接口"
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = " 导出服务-对外提供接口", description = " 导出服务-对外提供接口")
public class ApiExportProvider {

    @Resource
    private IExportTaskService exportTaskService;
    @Resource
    private TaskQueueManager taskQueueManager;

    @Operation(summary = "创建导出任务", description = "提交导出请求，创建异步导出任务并加入执行队列")
    @PostMapping("/tasks/create")
    public HttpResult<String> createTask(@Valid @RequestBody ExportRequestDto requestDto) {
        try {
            String taskId = exportTaskService.createTask(requestDto);
            boolean queued = taskQueueManager.addToQueue(taskId);
            if (!queued) {
                log.warn("任务入队失败: {}", taskId);
            }
            log.info("导出任务创建成功: taskId={}, userId={}", taskId, requestDto.getUserId());
            return HttpResult.ok(taskId);
        } catch (Exception e) {
            log.error("创建导出任务失败: {}", e.getMessage(), e);
            return HttpResult.error("创建导出任务失败: " + e.getMessage());
        }
    }
}
