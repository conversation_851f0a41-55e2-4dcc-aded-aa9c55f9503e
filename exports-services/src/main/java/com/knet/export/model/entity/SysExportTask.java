package com.knet.export.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.ExportType;
import com.knet.export.model.enums.ExportTaskStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

/**
 * 导出任务实体
 *
 * <AUTHOR> Export Team
 * @since 2025-09-24
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_export_task")
@Schema(description = "导出任务实体")
public class SysExportTask extends BaseEntity {

    @Schema(description = "任务唯一标识", example = "EXP_20250924_120000_0001")
    private String taskId;

    @Schema(description = "业务系统标识(order/goods/user/payment)", example = "order")
    private String systemId;

    @Schema(description = "数据提供者标识(用于标识具体的数据接口方法)", example = "adminOrderExport")
    private String dataProvider;

    @Schema(description = "导出类型标识(用于区分同一服务的不同导出场景)", example = "ADMIN_ORDER_LIST")
    private ExportType exportType;

    @Schema(description = "模板JSON配置（调用方提供）", example = "{\"columns\":[{\"field\":\"orderId\",\"title\":\"订单ID\"}]}")
    private String templateJson;

    @Schema(description = "导出参数JSON", example = "{\"startDate\":\"2025-01-01\"}")
    private String exportParams;

    @Schema(description = "任务状态:0-待处理,1-处理中,2-成功,3-失败,4-超时", example = "2")
    private ExportTaskStatus status;

    @Schema(description = "生成的文件名", example = "order_20250924_120000.xlsx")
    private String fileName;

    @Schema(description = "S3文件Key", example = "exports/order/20250924/EXP_20250924_120000_0001.xlsx")
    private String s3FileKey;

    @Schema(description = "下载链接", example = "https://s3.amazonaws.com/bucket/exports/order/20250924/EXP_20250924_120000_0001.xlsx")
    private String downloadUrl;

    @Schema(description = "文件大小(字节)", example = "1048576")
    private Long fileSize;

    @Schema(description = "导出记录数", example = "10000")
    private Integer recordCount;

    @Schema(description = "错误信息", example = "数据库连接超时")
    private String errorMessage;

    @Schema(description = "重试次数", example = "0")
    private Integer retryCount;

    @Schema(description = "开始执行时间", example = "2025-09-24T12:01:00")
    private Date startTime;

    @Schema(description = "完成时间", example = "2025-09-24T12:05:00")
    private Date completeTime;

    @Schema(description = "任务过期时间", example = "2025-10-01T12:00:00")
    private Date expireTime;

    @Schema(description = "任务创建人", example = "1")
    private Long createBy;
}
