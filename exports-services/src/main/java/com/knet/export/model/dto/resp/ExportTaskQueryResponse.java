package com.knet.export.model.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import com.knet.export.model.entity.SysExportTask;
import com.knet.export.model.enums.ExportTaskStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @description 导出任务查询响应
 * @date 2025-09-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(description = "导出任务查询响应")
public class ExportTaskQueryResponse extends BaseResponse {

    @Schema(description = "任务唯一标识", example = "EXPORT_20250926_001")
    private String taskId;

    @Schema(description = "文件名称", example = "管理员订单导出_20250926.xlsx")
    private String fileName;

    @Schema(description = "文件下载地址", example = "https://example.com/exports/admin_orders_20250926.xlsx")
    private String downloadUrl;

    /**
     * @see ExportTaskStatus
     */
    @Schema(description = "任务状态", example = "COMPLETED")
    private ExportTaskStatus status;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    @Schema(description = "任务创建时间")
    private Date createdTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    @Schema(description = "任务完成时间")
    private Date completedTime;

    @Schema(description = "创建人", example = "admin")
    private String createdBy;

    @Schema(description = "错误信息", example = "导出成功")
    private String errorMessage;

    public static ExportTaskQueryResponse convertToResponse(SysExportTask task) {
        return ExportTaskQueryResponse.builder()
                .taskId(task.getTaskId())
                .fileName(task.getFileName())
                .downloadUrl(task.getDownloadUrl())
                .status(task.getStatus())
                .createdTime(task.getCreateTime())
                .completedTime(task.getCompleteTime())
                .build();
    }
}
