package com.knet.export.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系统类型枚举
 *
 * <AUTHOR> Export Team
 * @since 2025-09-24
 */
@Getter
@AllArgsConstructor
@Schema(description = "业务系统类型枚举")
public enum SystemType {

    /**
     * 订单服务
     */
    ORDER("order", "订单服务"),
    /**
     * 商品服务
     */
    GOODS("goods", "商品服务"),
    /**
     * 用户服务
     */
    USER("user", "用户服务"),
    /**
     * 支付服务
     */
    PAYMENT("payment", "支付服务");

    @EnumValue
    @Schema(description = "系统代码", example = "order")
    private final String code;
    
    @JsonValue
    @Schema(description = "系统描述", example = "订单服务")
    private final String description;

    /**
     * 根据代码获取系统类型
     */
    public static SystemType fromCode(String code) {
        for (SystemType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown system code: " + code);
    }

    /**
     * 获取数据提供者Bean名称
     */
    public String getDataProviderBeanName() {
        return code + "ExportDataProvider";
    }
}
