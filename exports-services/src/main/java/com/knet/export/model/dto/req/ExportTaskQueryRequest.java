package com.knet.export.model.dto.req;

import com.knet.common.base.BasePageRequest;
import com.knet.export.model.enums.ExportTaskStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 导出任务查询请求
 * @date 2025-09-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "导出任务查询请求")
public class ExportTaskQueryRequest extends BasePageRequest {
    /**
     * @see ExportTaskStatus
     */
    @Schema(description = "任务状态,不传值 查询全部", example = "PENDING")
    private ExportTaskStatus status;

    @Schema(description = "创建人", example = "admin")
    private String createdBy;
}
