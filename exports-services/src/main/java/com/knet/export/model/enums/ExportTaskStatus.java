package com.knet.export.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 导出任务状态枚举
 *
 * <AUTHOR> Export Team
 * @since 2025-09-25
 */
@Schema(description = "导出任务状态")
public enum ExportTaskStatus {

    /**
     * 排队中
     */
    @Schema(description = "排队中")
    QUEUED("QUEUED", "排队中"),

    @Schema(description = "处理中")
    PROCESSING("PROCESSING", "处理中"),

    @Schema(description = "已完成")
    COMPLETED("COMPLETED", "已完成"),

    @Schema(description = "失败")
    FAILED("FAILED", "失败"),

    @Schema(description = "已取消")
    CANCELLED("CANCELLED", "已取消"),

    @Schema(description = "已过期")
    EXPIRED("EXPIRED", "已过期");

    @EnumValue
    @Schema(description = "code")
    private final String code;

    @Schema(description = "描述")
    private final String description;

    ExportTaskStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static ExportTaskStatus fromCode(String code) {
        for (ExportTaskStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown export task status code: " + code);
    }
}
