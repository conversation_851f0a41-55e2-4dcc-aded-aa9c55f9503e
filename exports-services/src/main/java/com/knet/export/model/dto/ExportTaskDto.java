package com.knet.export.model.dto;

import com.knet.export.model.enums.ExportTaskStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 导出任务响应DTO
 *
 * <AUTHOR> Export Team
 * @since 2025-09-25
 */
@Data
@Schema(description = "导出任务响应")
public class ExportTaskDto {

    @Schema(description = "任务ID", example = "EXP_20250925_143025_1001")
    private String taskId;

    @Schema(description = "系统ID", example = "order")
    private String systemId;

    @Schema(description = "导出类型", example = "订单导出")
    private String exportType;

    @Schema(description = "任务状态")
    private ExportTaskStatus status;

    @Schema(description = "进度百分比", example = "75")
    private Integer progress;

    @Schema(description = "已处理行数", example = "7500")
    private Long processedRows;

    @Schema(description = "预估总行数", example = "10000")
    private Long estimatedRows;

    @Schema(description = "文件下载URL")
    private String fileUrl;

    @Schema(description = "文件大小（字节）", example = "2048576")
    private Long fileSize;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;
}
