package com.knet.export.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/4/9 16:15
 * @description: 外部账号配置
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "account")
public class AccountConfig {
    /**
     * kg账号
     */
    private String kgAccount;
    /**
     * kg令牌
     */
    private String kgAccessToken;
}
