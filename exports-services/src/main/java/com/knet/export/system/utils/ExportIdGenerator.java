package com.knet.export.system.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 导出任务ID生成器
 *
 * <AUTHOR> Export Team
 * @since 2025-09-24
 */
@Slf4j
public class ExportIdGenerator {

    private static final AtomicLong SEQUENCE = new AtomicLong(0);
    private static final String DATE_PATTERN = "yyyyMMdd";

    /**
     * 生成导出任务ID
     * 格式: EXP_YYYYMMDD_HHMMSS_序号
     */
    public static String generateTaskId() {
        LocalDateTime now = LocalDateTime.now();
        String date = now.format(DateTimeFormatter.ofPattern(DATE_PATTERN));
        String time = now.format(DateTimeFormatter.ofPattern("HHmmss"));
        long seq = SEQUENCE.incrementAndGet() % 9999;

        return String.format("EXP_%s_%s_%04d", date, time, seq);
    }

    /**
     * 生成文件名
     * 格式: {prefix}_YYYYMMDD_HHMMSS.xlsx
     */
    public static String generateFileName(String prefix) {
        LocalDateTime now = LocalDateTime.now();
        String timestamp = now.format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return String.format("%s_%s.xlsx", prefix, timestamp);
    }

    /**
     * 生成S3文件Key
     * 格式: exports/{systemId}/{date}/{taskId}.xlsx
     */
    public static String generateS3FileKey(String systemId, String taskId) {
        String date = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DATE_PATTERN));
        return String.format("exports/%s/%s/%s.xlsx", systemId, date, taskId);
    }
}
