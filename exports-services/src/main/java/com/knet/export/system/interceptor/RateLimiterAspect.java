package com.knet.export.system.interceptor;

import cn.hutool.core.util.StrUtil;
import com.knet.common.annotation.RateLimiter;
import com.knet.common.context.UserContext;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.export.system.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

import static com.knet.common.constants.SystemConstant.API_RATE_LIMIT;

/**
 * <AUTHOR>
 * @date 2025/2/28 16:55
 * @description: 流量控制切面
 */
@Slf4j
@Aspect
@Component
public class RateLimiterAspect {

    private final JwtUtil jwtUtil;

    public RateLimiterAspect(JwtUtil jwtUtil) {
        this.jwtUtil = jwtUtil;
    }

    /**
     * SpEL表达式解析器
     */
    private final ExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer paramNameDiscoverer = new DefaultParameterNameDiscoverer();

    @Around("@annotation(rateLimiter)")
    public Object around(ProceedingJoinPoint joinPoint, RateLimiter rateLimiter) throws Throwable {
        String userKey = getUserKey(rateLimiter.keyExpression(), joinPoint);
        String fullKey = String.format(API_RATE_LIMIT, userKey);
        log.debug("Rate limiting for key: {}, capacity: {}, refillRate: {}",
                fullKey, rateLimiter.capacity(), rateLimiter.refillRate());
        //  执行令牌桶算法
        boolean allowed = RedisCacheUtil.isAllowed(fullKey, rateLimiter.capacity(), rateLimiter.refillRate());
        if (!allowed) {
            log.warn("Rate limit exceeded for key: {}", fullKey);
            throw new ServiceException(rateLimiter.message(), 200);
        }
        return joinPoint.proceed();
    }

    /**
     * 获取用户标识用于限流
     * 每个方法对每个用户都有独立的限流键，避免跨方法限流干扰
     * 优先级：UserContext > Token解析 > SpEL表达式 > 默认方法名
     */
    private String getUserKey(String expression, ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String methodKey = signature.getDeclaringTypeName() + ":" + method.getName();
        String userToken = UserContext.getContext();
        if (StrUtil.isNotEmpty(userToken)) {
            try {
                String account = jwtUtil.getAccountFromToken(userToken);
                if (StrUtil.isNotEmpty(account)) {
                    log.debug("Got user account from UserContext: {}", account);
                    return "user:" + account + ":method:" + methodKey;
                }
            } catch (Exception e) {
                log.warn("Failed to parse account from UserContext token: {}", e.getMessage());
            }
        }
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String token = request.getHeader("token");
                if (StrUtil.isNotEmpty(token)) {
                    String account = jwtUtil.getAccountFromToken(token);
                    if (StrUtil.isNotEmpty(account)) {
                        log.debug("Got user account from request header: {}", account);
                        return "user:" + account + ":method:" + methodKey;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Failed to get user from request header: {}", e.getMessage());
        }
        if (StrUtil.isNotEmpty(expression)) {
            try {
                String parsedKey = parseSpEL(expression, method, joinPoint.getArgs());
                if (StrUtil.isNotEmpty(parsedKey)) {
                    log.debug("Got user key from SpEL expression: {}", parsedKey);
                    return "user:" + parsedKey + ":method:" + methodKey;
                }
            } catch (Exception e) {
                log.warn("Failed to parse SpEL expression: {}", e.getMessage());
            }
        }
        String defaultKey = "method:" + signature.getDeclaringTypeName() + ":" + method.getName();
        log.warn("Unable to get user identifier, falling back to method-level rate limiting: {}", defaultKey);
        return defaultKey;
    }

    /**
     * 解析SpEL表达式
     */
    private String parseSpEL(String spEL, Method method, Object[] args) {
        try {
            Expression expression = parser.parseExpression(spEL);
            EvaluationContext context = new StandardEvaluationContext();
            String[] parameterNames = paramNameDiscoverer.getParameterNames(method);
            if (parameterNames != null) {
                // 将参数添加到上下文
                for (int i = 0; i < parameterNames.length; i++) {
                    context.setVariable(parameterNames[i], args[i]);
                }
            }
            // 评估表达式
            Object value = expression.getValue(context);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            log.error("解析SpEL表达式失败: {}", e.getMessage());
            return "";
        }
    }
}
