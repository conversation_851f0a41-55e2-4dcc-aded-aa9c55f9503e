package com.knet.export.system.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2025/7/23 优化版本
 * @description: XXL-Job配置
 */
@Slf4j
@Configuration
public class XxlJobConfig {

    @Value("${xxl.job.admin.addresses}")
    private String adminAddresses;

    @Value("${xxl.job.accessToken}")
    private String accessToken;

    @Value("${xxl.job.executor.appname}")
    private String appname;

    @Value("${xxl.job.executor.address}")
    private String address;

    @Value("${xxl.job.executor.ip}")
    private String ip;

    @Value("${server.port}")
    private int serverPort;

    @Value("${xxl.job.executor.port}")
    private int port;

    @Value("${xxl.job.executor.logpath}")
    private String logPath;

    @Value("${xxl.job.executor.logretentiondays}")
    private int logRetentionDays;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        // 使用相对路径作为默认值，如果配置的路径不可用
        String actualLogPath = getValidLogPath(logPath);

        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname(appname);
        xxlJobSpringExecutor.setAddress(address);
        xxlJobSpringExecutor.setIp(ip);
        if (-1 == port) {
            xxlJobSpringExecutor.setPort(serverPort + 2000);
        } else {
            xxlJobSpringExecutor.setPort(port);
        }
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setLogPath(actualLogPath);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
        return xxlJobSpringExecutor;
    }

    /**
     * 获取有效的日志路径，如果配置的路径不可用则使用默认路径
     */
    private String getValidLogPath(String configuredPath) {
        // 默认路径：使用父项目 knet 中的 logs 路径
        String defaultPath = "../logs/exports-services/xxl-job";
        try {
            // 尝试使用配置的路径
            if (ensureLogDirectoryExists(configuredPath)) {
                return configuredPath;
            }
            // 如果配置的路径不可用，使用默认路径
            log.warn("配置的 XXL-Job 日志路径不可用: {}，使用默认路径: {}", configuredPath, defaultPath);
            ensureLogDirectoryExists(defaultPath);
            return defaultPath;
        } catch (Exception e) {
            log.error("处理 XXL-Job 日志路径时发生异常，使用默认路径: {}", defaultPath, e);
            ensureLogDirectoryExists(defaultPath);
            return defaultPath;
        }
    }

    /**
     * 确保日志目录存在，如果不存在则创建
     *
     * @return true 如果目录存在或创建成功，false 如果创建失败
     */
    private boolean ensureLogDirectoryExists(String logPath) {
        try {
            File logDir = new File(logPath);
            if (!logDir.exists()) {
                boolean created = logDir.mkdirs();
                if (created) {
                    log.info("XXL-Job 日志目录创建成功: {}", logPath);
                    return true;
                } else {
                    log.warn("XXL-Job 日志目录创建失败: {}", logPath);
                    return false;
                }
            } else {
                log.info("XXL-Job 日志目录已存在: {}", logPath);
                return true;
            }
        } catch (Exception e) {
            log.error("创建 XXL-Job 日志目录时发生异常: {}", logPath, e);
            return false;
        }
    }
}