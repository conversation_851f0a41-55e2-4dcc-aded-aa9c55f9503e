package com.knet.export.system.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.knet.export.manager.DataProviderManager;
import com.knet.export.model.entity.SysExportTask;
import com.knet.export.parser.JsonTemplateParser;
import com.knet.export.system.config.ExportConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/9/25 14:08
 * @description: 导出工具类
 */
@Slf4j
@Component
public class EasyExcelExportUtils {

    @Resource
    private DataProviderManager dataProviderManager;
    @Resource
    private ExportConfig exportConfig;

    /**
     * 构建表头
     */
    private List<List<String>> buildHeaders(JsonTemplateParser.SimpleTemplate template) {
        List<List<String>> headers = new ArrayList<>();
        for (JsonTemplateParser.ColumnConfig column : template.getColumns()) {
            List<String> header = new ArrayList<>();
            header.add(column.getTitle());
            headers.add(header);
        }
        return headers;
    }

    /**
     * 流式导出数据到Excel
     *
     * @param task         导出任务
     * @param template     模板配置
     * @param tempFile     临时文件
     * @param exportParams 导出参数
     * @return 实际导出记录数
     */
    public long exportDataToExcel(
            SysExportTask task,
            JsonTemplateParser.SimpleTemplate template,
            File tempFile,
            Map<String, Object> exportParams) {
        log.info("开始流式导出数据 - taskId: {}, dataProvider: {}, exportType: {}", task.getTaskId(), task.getDataProvider(), task.getExportType());
        try (FileOutputStream outputStream = new FileOutputStream(tempFile)) {
            // 构建Excel写入器
            ExcelWriterBuilder writerBuilder = EasyExcel.write(outputStream)
                    .head(buildHeaders(template))
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy());
            WriteSheet writeSheet = EasyExcel.writerSheet(template.getSheetName()).build();
            long actualCount = 0;
            int currentPageNo = 1;
            boolean hasMoreData = true;
            // 流式分页查询并写入
            while (hasMoreData) {
                Stream<Map<String, Object>> dataStream = dataProviderManager.queryExportData(
                        task, exportParams, currentPageNo, exportConfig.getDefaultPageSize());
                List<Map<String, Object>> pageData = dataStream.toList();
                if (pageData.isEmpty()) {
                    hasMoreData = false;
                    break;
                }
                List<List<Object>> excelData = convertToExcelData(pageData, template);
                writerBuilder.sheet(writeSheet.getSheetName()).doWrite(excelData);
                actualCount += pageData.size();
                // 如果返回的数据少于页大小，说明已经是最后一页
                if (pageData.size() < exportConfig.getDefaultPageSize()) {
                    hasMoreData = false;
                } else {
                    currentPageNo++; // 递增页码
                }
                log.debug("已处理 {} 条记录, 当前批次: {} 条, 当前页: {}", actualCount, pageData.size(), currentPageNo);
            }
            log.info("导出完成 - taskId: {}, 实际记录数: {}", task.getTaskId(), actualCount);
            return actualCount;
        } catch (IOException e) {
            log.error("导出数据到Excel失败 - taskId: {}", task.getTaskId(), e);
            return -1;
        } catch (Exception e) {
            log.error("导出过程异常 - taskId: {}", task.getTaskId(), e);
            return -1;
        }
    }

    /**
     * 转换数据为Excel格式
     */
    private List<List<Object>> convertToExcelData(
            List<Map<String, Object>> pageData,
            JsonTemplateParser.SimpleTemplate template) {
        List<List<Object>> excelData = new ArrayList<>();
        for (Map<String, Object> rowData : pageData) {
            List<Object> excelRow = new ArrayList<>();
            for (JsonTemplateParser.ColumnConfig column : template.getColumns()) {
                Object value = rowData.get(column.getField());
                excelRow.add(value != null ? value.toString() : "");
            }
            excelData.add(excelRow);
        }
        return excelData;
    }

    private String extractLastId(Map<String, Object> record) {
        // 根据实际情况提取最后一条记录的ID
        return String.valueOf(record.get("orderId"));
    }
}
