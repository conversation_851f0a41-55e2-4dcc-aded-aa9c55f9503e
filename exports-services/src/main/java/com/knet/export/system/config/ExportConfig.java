package com.knet.export.system.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 导出服务配置类
 *
 * <AUTHOR> Export Team
 * @since 2025-09-24
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "knet.export")
@Schema(description = "导出服务配置参数")
public class ExportConfig {

    @Schema(description = "最大并发导出任务数", example = "3")
    private int maxConcurrentTasks = 3;

    @Schema(description = "内存使用限制（字节）", example = "1073741824")
    private long maxMemoryUsage = 1024L * 1024 * 1024; // 1GB

    @Schema(description = "默认分页大小", example = "1000")
    private int defaultPageSize = 1000;

    @Schema(description = "最大导出记录数", example = "100000")
    private int maxRecordsPerExport = 100000;

    @Schema(description = "任务默认过期天数", example = "7")
    private int defaultExpireDays = 7;

    @Schema(description = "任务锁过期时间（秒）", example = "1800")
    private int taskLockExpireSeconds = 1800; // 30分钟

    @Schema(description = "临时文件目录", example = "/tmp/export")
    private String tempDirectory = System.getProperty("java.io.tmpdir") + "/export";

    @Schema(description = "S3存储配置")
    private S3Config s3 = new S3Config();

    @Data
    @Schema(description = "S3存储配置参数")
    public static class S3Config {
        @Schema(description = "S3存储桶名称", example = "knet-exports")
        private String bucketName = "knet-exports";

        @Schema(description = "AWS区域", example = "us-east-1")
        private String region = "us-east-1";

        @Schema(description = "AWS访问密钥ID")
        private String accessKey;

        @Schema(description = "AWS访问密钥")
        private String secretKey;

        @Schema(description = "S3服务端点", example = "https://s3.amazonaws.com")
        private String endpoint;

        @Schema(description = "预签名URL过期时间（小时）", example = "24")
        private int presignedUrlExpireHours = 24;
    }
}
