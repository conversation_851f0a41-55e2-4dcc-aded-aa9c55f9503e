package com.knet.export.system.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 导出服务配置类
 *
 * <AUTHOR> Export Team
 * @since 2025-09-24
 */
@RefreshScope
@Data
@Configuration
@ConfigurationProperties(prefix = "knet.export")
@Schema(description = "导出服务配置参数")
public class ExportConfig {

    @Schema(description = "最大并发导出任务数", example = "3")
    private int maxConcurrentTasks = 3;

    @Schema(description = "默认分页大小", example = "1000")
    private int defaultPageSize = 1000;

    @Schema(description = "最大导出记录数", example = "1000000")
    private int maxRecordsPerExport = 1000000;

    @Schema(description = "任务锁过期时间（秒）", example = "1800")
    private int taskLockExpireSeconds = 1800;
    
    @Schema(description = "任务执行超时时间（秒）20分钟")
    private int taskLockTimeoutSeconds = 1200;

    @Schema(description = "临时文件目录", example = "/tmp/export")
    private String tempDirectory = System.getProperty("java.io.tmpdir") + "/export";
}
