package com.knet.export.system.schedule;

import com.knet.export.manager.TaskQueueManager;
import com.knet.export.processor.ExportTaskProcessor;
import com.knet.export.system.config.ExportConfig;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.Semaphore;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 导出任务XXL-Job处理器
 * 负责从队列中获取任务并执行，支持并发控制
 *
 * <AUTHOR> Export Team
 * @since 2025-09-24
 */
@Slf4j
@Component
public class ExportJobHandler {

    @Resource
    private TaskQueueManager taskQueueManager;
    @Resource
    private ExportTaskProcessor taskProcessor;
    @Resource
    private ExportConfig exportConfig;
    @Resource(name = "exportsThreadPoolExecutor")
    private ThreadPoolExecutor exportThreadPoolExecutor;
    /**
     * 并发控制信号量，限制同时执行的任务数量
     */
    private volatile Semaphore concurrentLimiter;

    /**
     * 初始化并发控制器
     */
    private Semaphore getConcurrentLimiter() {
        if (concurrentLimiter == null) {
            synchronized (this) {
                if (concurrentLimiter == null) {
                    concurrentLimiter = new Semaphore(exportConfig.getMaxConcurrentTasks());
                    log.info("初始化并发控制器，最大并发数: {}", exportConfig.getMaxConcurrentTasks());
                }
            }
        }
        return concurrentLimiter;
    }

    /**
     * 导出任务执行器
     * 定时从队列中获取任务并执行，支持并发控制
     */
    @XxlJob("exportTaskExecutor")
    public void executeExportTasks() {
        Semaphore limiter = getConcurrentLimiter();

        // 检查当前可用的并发槽位
        int availablePermits = limiter.availablePermits();
        if (availablePermits <= 0) {
            log.debug("当前并发任务已达上限 {}, 等待下次调度", exportConfig.getMaxConcurrentTasks());
            return;
        }

        try {
            // 尝试获取任务，如果没有任务直接返回
            String taskId = taskQueueManager.pollTask();
            if (taskId == null) {
                log.debug("队列中暂无待处理任务");
                return;
            }

            log.info("从队列获取到任务: {}, 当前可用并发槽位: {}", taskId, availablePermits);

            // 尝试获取并发许可
            if (!limiter.tryAcquire()) {
                log.warn("无法获取并发许可，任务重新入队: {}", taskId);
                taskQueueManager.addToQueue(taskId);
                return;
            }

            // 异步执行任务，避免阻塞调度线程
            CompletableFuture.runAsync(() -> {
                try {
                    executeTask(taskId);
                } finally {
                    // 释放并发许可
                    limiter.release();
                    log.info("释放并发许可，任务: {}, 当前可用槽位: {}", taskId, limiter.availablePermits());
                }
            }, exportThreadPoolExecutor);

        } catch (Exception e) {
            log.error("执行导出任务调度异常", e);
        }
    }

    /**
     * 执行单个导出任务
     */
    private void executeTask(String taskId) {
        try {
            // 获取任务锁，防止重复执行
            boolean acquired = taskQueueManager.acquireLock(taskId);
            if (!acquired) {
                log.warn("无法获取任务锁，可能已被其他实例处理: {}", taskId);
                return;
            }

            log.info("开始执行导出任务: {}", taskId);
            boolean success = taskProcessor.processExportTask(taskId);

            if (success) {
                log.info("任务执行成功: {}", taskId);
            } else {
                log.warn("任务执行失败: {}", taskId);
            }

        } catch (Exception e) {
            log.error("执行导出任务异常: {}", taskId, e);
        } finally {
            // 确保释放任务锁
            taskQueueManager.releaseLock(taskId);
        }
    }

    /**
     * 超时任务清理器
     * 定期清理超时的任务
     */
    @XxlJob("timeoutTaskCleaner")
    public void cleanupTimeoutTasks() {
        log.info("开始清理超时任务");
        try {
            taskQueueManager.cleanupTimeoutTasks();
            log.info("超时任务清理完成");
        } catch (Exception e) {
            log.error("清理超时任务异常", e);
        }
    }
}
