package com.knet.export.system.schedule;

import com.knet.export.manager.TaskQueueManager;
import com.knet.export.processor.ExportTaskProcessor;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 导出任务XXL-Job处理器
 * 负责从队列中获取任务并执行
 *
 * <AUTHOR> Export Team
 * @since 2025-09-24
 */
@Slf4j
@Component
public class ExportJobHandler {

    @Resource
    private TaskQueueManager taskQueueManager;
    @Resource
    private ExportTaskProcessor taskProcessor;

    /**
     * 导出任务执行器
     * 定时从队列中获取任务并执行
     */
    @XxlJob("exportTaskExecutor")
    public void executeExportTasks() {
        try {
            String taskId = taskQueueManager.pollTask();
            if (taskId != null) {
                log.info("从队列获取到任务: {}", taskId);
                boolean acquired = taskQueueManager.acquireLock(taskId);
                if (!acquired) {
                    return;
                }
                boolean success = taskProcessor.processExportTask(taskId);
                if (success) {
                    taskQueueManager.releaseLock(taskId);
                    log.info("任务执行成功: {}", taskId);
                } else {
                    log.warn("任务执行失败: {}", taskId);
                }
            } else {
                log.debug("队列中暂无待处理任务");
            }
        } catch (Exception e) {
            log.error("执行导出任务调度异常", e);
        }
    }

    /**
     * 超时任务清理器
     * 定期清理超时的任务
     */
    @XxlJob("timeoutTaskCleaner")
    public void cleanupTimeoutTasks() {
        log.info("开始清理超时任务");
        try {
            taskQueueManager.cleanupTimeoutTasks();
            log.info("超时任务清理完成");
        } catch (Exception e) {
            log.error("清理超时任务异常", e);
        }
    }
}
